"""
Buy-and-Hold策略实现

实现简单的买入并持有策略，作为基准与其他交易策略进行比较。
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, Any, List, Tuple, Optional

class BuyAndHoldStrategy:
    """
    买入并持有策略类

    在开始时买入，然后一直持有到结束。
    """

    def __init__(self,
                 data_path: str = 'data/eth_daily.csv',
                 starting_date: str = '2023-04-01',
                 ending_date: str = '2023-06-30',
                 initial_balance: float = 10000.0,
                 fee_rate: float = 0.001):
        """
        初始化买入并持有策略

        Args:
            data_path: 价格数据文件路径
            starting_date: 开始日期
            ending_date: 结束日期
            initial_balance: 初始资金
            fee_rate: 交易手续费率
        """
        self.data_path = data_path
        self.starting_date = starting_date
        self.ending_date = ending_date
        self.initial_balance = initial_balance
        self.fee_rate = fee_rate

        # 加载数据
        self.load_data()

        # 初始化结果存储
        self.results = {
            'dates': [],
            'prices': [],
            'balances': [],
            'positions': [],
            'net_worths': [],
            'returns': [],
            'daily_returns': [],
            'actions': []
        }

    def load_data(self) -> None:
        """
        加载价格数据
        """
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(f"数据文件 {self.data_path} 不存在")

        # 加载CSV数据
        self.data = pd.read_csv(self.data_path)

        # 确保日期列是日期类型
        if 'date' in self.data.columns:
            self.data['date'] = pd.to_datetime(self.data['date'])
        elif 'Date' in self.data.columns:
            self.data['date'] = pd.to_datetime(self.data['Date'])
            self.data = self.data.rename(columns={'Date': 'date'})
        elif 'snapped_at' in self.data.columns:
            self.data['date'] = pd.to_datetime(self.data['snapped_at'])
        else:
            raise ValueError("数据文件中没有找到日期列")

        # 过滤日期范围
        self.data = self.data[(self.data['date'] >= self.starting_date) &
                              (self.data['date'] <= self.ending_date)]

        # 确保数据按日期排序
        self.data = self.data.sort_values('date')

        # 检查是否有足够的数据
        if len(self.data) == 0:
            raise ValueError(f"在指定日期范围 {self.starting_date} 到 {self.ending_date} 内没有找到数据")

        print(f"加载了 {len(self.data)} 天的数据，从 {self.data['date'].min()} 到 {self.data['date'].max()}")

    def run(self) -> Dict[str, Any]:
        """
        运行买入并持有策略

        Returns:
            策略运行结果
        """
        # 初始状态
        cash_balance = self.initial_balance
        position = 0.0
        prev_price = None
        start_price = None

        # 遍历每一天
        for idx, row in self.data.iterrows():
            date = row['date']
            # 尝试获取价格列
            if 'close' in row:
                price = row['close']
            elif 'Close' in row:
                price = row['Close']
            elif 'open' in row:
                price = row['open']
            else:
                raise ValueError(f"无法找到价格列，可用列: {row.index.tolist()}")

            # 记录起始价格
            if idx == 0:
                start_price = price

                # 计算可以买入的数量（考虑手续费）
                buy_amount = cash_balance / (price * (1 + self.fee_rate))

                # 执行买入
                position = buy_amount
                cash_balance = 0.0
                action = 1.0  # 买入动作
            else:
                # 之后的日子只持有
                action = 0.0  # 持有动作

            # 计算当前净值
            net_worth = cash_balance + position * price

            # 计算收益率
            if prev_price is not None:
                daily_return = (price / prev_price) - 1
            else:
                daily_return = 0.0

            # 计算总收益率（相对于起始价格）
            if start_price is not None:
                total_return = (price / start_price) - 1
            else:
                total_return = 0.0

            # 记录结果
            self.results['dates'].append(date)
            self.results['prices'].append(price)
            self.results['balances'].append(cash_balance)
            self.results['positions'].append(position)
            self.results['net_worths'].append(net_worth)
            self.results['returns'].append(total_return)
            self.results['daily_returns'].append(daily_return)
            self.results['actions'].append(action)

            # 更新前一天价格
            prev_price = price

        # 计算性能指标
        self.calculate_metrics()

        return self.results

    def calculate_metrics(self) -> Dict[str, float]:
        """
        计算性能指标

        Returns:
            性能指标字典
        """
        # 提取数据
        returns = np.array(self.results['returns'])
        daily_returns = np.array(self.results['daily_returns'])
        net_worths = np.array(self.results['net_worths'])
        prices = np.array(self.results['prices'])

        # 计算总收益率（最后价格与第一个价格的比值减1）
        if len(prices) >= 2:
            total_return = ((prices[-1] / prices[0]) - 1) * 100  # 转为百分比
        else:
            total_return = 0

        # 计算Sharpe比率
        risk_free_rate = 0
        return_mean = np.mean(daily_returns) * 252  # 年化
        return_std = np.std(daily_returns) * np.sqrt(252)  # 年化
        sharpe_ratio = (return_mean - risk_free_rate) / return_std if return_std > 0 else 0

        # 计算最大回撤
        peak = np.maximum.accumulate(prices)
        drawdown = (peak - prices) / peak
        max_drawdown = np.max(drawdown) * 100 if len(drawdown) > 0 else 0  # 转为百分比

        # 计算胜率
        winning_days = np.sum(daily_returns > 0)
        total_days = len(daily_returns)
        win_rate = (winning_days / total_days) * 100 if total_days > 0 else 0  # 转为百分比

        # 计算日收益率统计
        daily_return_mean = np.mean(daily_returns) * 100  # 转为百分比
        daily_return_std = np.std(daily_returns) * 100  # 转为百分比

        # 保存指标
        self.metrics = {
            '总收益率': total_return,
            'Sharpe比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            '胜率': win_rate,
            '日收益率均值': daily_return_mean,
            '日收益率标准差': daily_return_std
        }

        return self.metrics

    def save_results(self, output_dir: str) -> str:
        """
        保存策略运行结果

        Args:
            output_dir: 输出目录

        Returns:
            结果文件路径
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 创建报告目录
        reports_dir = os.path.join(output_dir, 'reports')
        os.makedirs(reports_dir, exist_ok=True)

        # 保存指标
        metrics_path = os.path.join(reports_dir, 'metrics.json')
        with open(metrics_path, 'w', encoding='utf-8') as f:
            json.dump(self.metrics, f, ensure_ascii=False, indent=2)

        # 创建结果DataFrame
        # 确保日期正确转换
        dates = []
        for date in self.results['dates']:
            if hasattr(date, 'strftime'):
                dates.append(date.strftime('%Y-%m-%d'))
            else:
                dates.append(str(date))

        results_df = pd.DataFrame({
            '日期': dates,
            '价格': self.results['prices'],
            '现金余额': self.results['balances'],
            '持仓量': self.results['positions'],
            '净资产': self.results['net_worths'],
            '累计收益率': self.results['returns'],
            '日收益率': self.results['daily_returns'],
            '交易动作': self.results['actions']
        })

        # 保存结果CSV
        csv_path = os.path.join(output_dir, 'buyandhold_results.csv')
        results_df.to_csv(csv_path, index=False)

        # 生成HTML报告
        html_path = os.path.join(reports_dir, 'performance_report.html')
        self._generate_html_report(html_path)

        return html_path

    def _generate_html_report(self, html_path: str) -> None:
        """
        生成HTML性能报告

        Args:
            html_path: HTML文件路径
        """
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(f'''
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Buy-and-Hold策略性能报告</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1, h2, h3 {{ color: #333; }}
                    table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    .metrics {{ display: flex; flex-wrap: wrap; }}
                    .metric-card {{
                        background-color: #f8f9fa;
                        border-radius: 5px;
                        padding: 15px;
                        margin: 10px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        flex: 1;
                        min-width: 200px;
                    }}
                    .metric-value {{
                        font-size: 24px;
                        font-weight: bold;
                        margin: 10px 0;
                        color: #0066cc;
                    }}
                    .positive {{ color: green; }}
                    .negative {{ color: red; }}
                </style>
            </head>
            <body>
                <h1>Buy-and-Hold策略性能报告</h1>
                <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>数据区间: {self.starting_date} 至 {self.ending_date}</p>

                <h2>1. 性能指标摘要</h2>
                <div class="metrics">
                    <div class="metric-card">
                        <h3>总收益率</h3>
                        <div class="metric-value {('positive' if self.metrics['总收益率'] >= 0 else 'negative')}">{self.metrics['总收益率']:.2f}%</div>
                    </div>
                    <div class="metric-card">
                        <h3>Sharpe比率</h3>
                        <div class="metric-value {('positive' if self.metrics['Sharpe比率'] >= 0 else 'negative')}">{self.metrics['Sharpe比率']:.2f}</div>
                    </div>
                    <div class="metric-card">
                        <h3>最大回撤</h3>
                        <div class="metric-value negative">{self.metrics['最大回撤']:.2f}%</div>
                    </div>
                    <div class="metric-card">
                        <h3>胜率</h3>
                        <div class="metric-value">{self.metrics['胜率']:.2f}%</div>
                    </div>
                    <div class="metric-card">
                        <h3>日收益率均值</h3>
                        <div class="metric-value {('positive' if self.metrics['日收益率均值'] >= 0 else 'negative')}">{self.metrics['日收益率均值']:.2f}%</div>
                    </div>
                    <div class="metric-card">
                        <h3>日收益率标准差</h3>
                        <div class="metric-value">{self.metrics['日收益率标准差']:.2f}%</div>
                    </div>
                </div>

                <h2>2. 交易数据表格</h2>
                <table>
                    <tr>
                        <th>日期</th>
                        <th>价格</th>
                        <th>净资产</th>
                        <th>累计收益率</th>
                        <th>日收益率</th>
                    </tr>
            ''')

            # 添加交易数据表格行
            for i in range(min(len(self.results['dates']), 100)):  # 限制显示前100行
                date = self.results['dates'][i]
                price = self.results['prices'][i]
                net_worth = self.results['net_worths'][i]
                total_return = self.results['returns'][i]
                daily_return = self.results['daily_returns'][i]

                # 确保日期正确显示
                date_str = date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date)

                f.write(f'''
                    <tr>
                        <td>{date_str}</td>
                        <td>{price:.2f}</td>
                        <td>{net_worth:.2f}</td>
                        <td class="{('positive' if total_return >= 0 else 'negative')}">{total_return*100:.2f}%</td>
                        <td class="{('positive' if daily_return >= 0 else 'negative')}">{daily_return*100:.2f}%</td>
                    </tr>
                ''')

            # 如果数据超过100行，添加省略提示
            if len(self.results['dates']) > 100:
                f.write(f'''
                    <tr>
                        <td colspan="5" style="text-align: center;">... (共 {len(self.results['dates'])} 行数据，仅显示前100行)</td>
                    </tr>
                ''')

            f.write('''
                </table>
            </body>
            </html>
            ''')
