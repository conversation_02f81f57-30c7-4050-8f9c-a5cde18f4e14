import os
import sys
import time
from dotenv import load_dotenv
from eth_account import Account
from web3 import Web3
import numpy as np
import pandas as pd
from Ipfs import IPFS
import json


class AgentListener:
    def __init__(self, agent_name):
        """
        初始化AgentListener。

        Args:
            agent_name: 代理名称，用于加载对应的.env文件
        """
        self.agent_name = agent_name

        # 加载环境变量
        dotenv_path = os.path.join(os.getcwd(), f"{agent_name}/.env")
        print(f"Loading .env file from: {dotenv_path}")

        # 尝试加载.env文件
        try:
            load_dotenv(dotenv_path=dotenv_path, override=True)
        except Exception as e:
            print(f"Warning: Failed to load .env file: {e}")
            # 创建agent目录（如果不存在）
            os.makedirs(os.path.dirname(dotenv_path), exist_ok=True)
            # 创建默认.env文件
            self._create_default_env_file(agent_name, dotenv_path)
            # 重新加载.env文件
            load_dotenv(dotenv_path=dotenv_path, override=True)

        # 读取EAAC合约的ABI文件
        abi_path = os.getenv('EAAC_ABI_PATH')
        if abi_path and os.path.exists(abi_path):
            with open(abi_path, 'r') as f:
                self.EAAC_ABI = json.load(f)
        else:
            print(f"Warning: EAAC_ABI_PATH not found or invalid: {abi_path}")
            # 使用默认ABI
            self.EAAC_ABI = self._get_default_abi()

        # 建立HTTP连接
        rpc_provider = os.getenv('RPC_PROVIDER') or os.environ.get("ETH_RPC", 'HTTP://127.0.0.1:7545')
        print(f"Connecting to Ethereum node at: {rpc_provider}")
        self.w3 = Web3(Web3.HTTPProvider(rpc_provider))
        if not self.w3.is_connected():
            print("Warning: Failed to connect to Ethereum node. Make sure Ganache is running.")
        else:
            print(f"成功连接到以太坊节点: {rpc_provider}")

        # 获取EAAC合约地址，并创建合约实例
        self.EAAC_addr = os.getenv('EAAC_ADDR') or os.environ.get("CONTRACT_ADDRESS")
        if not self.EAAC_addr:
            print("Warning: Contract address not found in .env file or environment variables.")
            self.EAAC_addr = '******************************************'  # 默认地址

        self.contract = self.w3.eth.contract(address=self.EAAC_addr, abi=self.EAAC_ABI)
        print(f"Private key: {os.getenv('PRIVATE_KEY')}")

        # 初始化区块号
        self.fromblock = 0
        self.recent_block = self.w3.eth.block_number if self.w3.is_connected() else 0

        # 每次请求的区块范围大小
        self.REQ_SIZE = 1
        self.ipfs = IPFS()

        # 加载历史记录文件
        self.history_file = f'{os.path.dirname(__file__)}/{self.agent_name}_history.csv'
        if os.path.exists(self.history_file):
            self.history = pd.read_csv(self.history_file)
            self.fromblock = max(self.history['block_number']) + 1
        else:
            self.fromblock = max(self.recent_block - 1000, 0)
            self.history = pd.DataFrame(columns=['block_number', 'transaction_hash'])

    def _create_default_env_file(self, agent_name, dotenv_path):
        """创建默认的.env文件"""
        # 确保目录存在
        os.makedirs(os.path.dirname(dotenv_path), exist_ok=True)

        # 默认配置
        default_config = f"""# {agent_name} 配置
PRIVATE_KEY=0x3c7c6823d1875cd64b5785c941d7a8e69ec5a2fe773dda1b544c2484b2c6b149
EAAC_ADDR=******************************************
EAAC_ABI_PATH=contract/EAAC.json
RPC_PROVIDER=HTTP://127.0.0.1:7545
"""

        # 写入.env文件
        with open(dotenv_path, 'w') as f:
            f.write(default_config)

        print(f"Created default .env file for {agent_name}")

        # 确保ABI文件目录存在
        os.makedirs("contract", exist_ok=True)

        # 创建默认ABI文件
        abi_path = "contract/EAAC.json"
        if not os.path.exists(abi_path):
            with open(abi_path, 'w') as f:
                json.dump(self._get_default_abi(), f, indent=2)
            print(f"Created default ABI file at {abi_path}")

    def _get_default_abi(self):
        """获取默认的ABI"""
        return [
            {
                "anonymous": False,
                "inputs": [
                    {
                        "indexed": True,
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    }
                ],
                "name": "Register",
                "type": "event"
            },
            {
                "anonymous": False,
                "inputs": [
                    {
                        "indexed": True,
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "Report",
                "type": "event"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    }
                ],
                "name": "register_agent",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    }
                ],
                "name": "register_agent_generic",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    },
                    {
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "report_activity",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "report_activity_generic",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]

    def get_recent_block(self):
        """获取最新区块号"""
        if self.w3.is_connected():
            self.recent_block = self.w3.eth.block_number
            print(f"最新区块号: {self.recent_block}")
        else:
            print("Warning: Not connected to Ethereum node.")

    def get_events_log(self):
        """获取新区块中的交易日志"""
        if not self.w3.is_connected():
            print("Warning: Not connected to Ethereum node.")
            return

        if self.fromblock > self.recent_block:
            return

        # 分批次获取日志
        for step in range(self.fromblock, self.recent_block + 1, self.REQ_SIZE):
            toblock = min(step + self.REQ_SIZE - 1, self.recent_block)

            try:
                # 获取区块信息
                block = self.w3.eth.get_block(toblock, full_transactions=True)

                # 遍历区块中的交易
                for transaction in block.transactions:
                    # 获取交易凭证
                    transaction_receipt = self.w3.eth.get_transaction_receipt(transaction.hash)

                    # 处理交易凭证
                    self.process_transaction_receipt(transaction_receipt)

                    # 更新历史记录
                    self.history = self.history._append({
                        'block_number': block.number,
                        'transaction_hash': transaction.hash.hex()
                    }, ignore_index=True)

                # 更新fromblock
                self.fromblock = toblock + 1
            except Exception as e:
                print(f"Error processing block {toblock}: {e}")
                continue

        # 保存历史记录
        self.history.to_csv(self.history_file, index=False)

    def process_transaction_receipt(self, tx_receipt):
        """
        处理交易凭证，提取事件日志。

        Args:
            tx_receipt: 交易凭证。

        Returns:
            从IPFS检索到的数据。
        """
        try:
            # 从交易收据中解析Report事件日志
            logs = self.contract.events.Report().process_receipt(tx_receipt)
            for log in logs:
                # 打印事件参数和任务ID
                print(log)
                print(f"任务已部署: {log['args']}")

                # 获取IPFS哈希、操作者和标识符
                ipfs_hash = log['args']['report_hash']
                operator = log['args']['operator']
                identifier = log['args']['identifier']

                print(f"任务已部署: {log['args']}")

                try:
                    data = self.ipfs.retrieve_json_from_ipfs(ipfs_hash)
                    return data
                except Exception as e:
                    print(f"Error retrieving JSON from IPFS: {e}")
                    return None
        except Exception as e:
            print(f"Error processing transaction receipt: {e}")
            return None

        return None

# 主循环
if __name__ == "__main__":
    # 创建AgentListener实例
    agent_listener = AgentListener("test_agent")

    # 监听区块链事件
    while True:
        # 获取最新区块号
        agent_listener.get_recent_block()

        # 如果检测到新区块，处理日志
        if agent_listener.fromblock <= agent_listener.recent_block:
            agent_listener.get_events_log()

        # 延迟2秒，避免频繁请求
        time.sleep(2)
