import ipfshttpclient
import json
import os

class IPFS:
    def __init__(self, ipfs_address=None):
        """
        初始化 IPFS 类，连接到指定的 IPFS 节点。

        :param ipfs_address: IPFS 节点的地址，默认为环境变量中的地址或本地节点。
        """
        # 如果未提供地址，尝试从环境变量获取
        if ipfs_address is None:
            ipfs_address = os.environ.get("IPFS_ADDRESS", '/ip4/127.0.0.1/tcp/5001')

        try:
            self.client = ipfshttpclient.connect(ipfs_address)
            print(f"成功连接到IPFS节点: {ipfs_address}")
        except Exception as e:
            print(f"Error connecting to IPFS node at {ipfs_address}: {e}")
            self.client = None  # 或者 raise 异常，取决于你的需求

    def upload_json_to_ipfs(self, json_data):
        """
        将 JSON 数据上传到 IPFS 并返回文件的 CID（哈希值）。

        :param json_data: 要上传的 JSON 数据（字典格式）
        :return: 文件的 CID（哈希值），如果上传失败则返回 None。
        """
        if self.client is None:
            print("IPFS client is not initialized. Check connection.")
            return None

        try:
            # 将 JSON 数据转换为字符串
            json_str = json.dumps(json_data, indent=4)  # 添加 indent 使其更易读

            # 将 JSON 字符串上传到 IPFS
            result = self.client.add_str(json_str)
            print(f"Uploaded to IPFS, result: {result}")

            # 返回文件的 CID
            return result
        except Exception as e:
            print(f"Error uploading JSON to IPFS: {e}")
            return None

    def retrieve_json_from_ipfs(self, cid):
        """
        根据 CID 从 IPFS 中检索 JSON 文件并返回 JSON 格式的内容。

        :param cid: 文件的 CID（哈希值）
        :return: JSON 格式的内容（字典格式），如果检索失败则返回 None。
        """
        if self.client is None:
            print("IPFS client is not initialized. Check connection.")
            return None

        try:
            # 从 IPFS 中获取文件内容
            file_content = self.client.cat(cid)

            # 将文件内容解码为字符串
            json_str = file_content.decode('utf-8')

            # 将 JSON 字符串转换为字典
            json_data = json.loads(json_str)
            print(f"Retrieved JSON from IPFS with CID: {cid}")

            # 返回 JSON 数据
            return json_data
        except Exception as e:
            print(f"Error retrieving JSON from IPFS: {e}")
            return None


# 示例用法
if __name__ == "__main__":
    # 示例 JSON 数据
    test_json = {
        "test": "test",
        "data": {
            "key1": "value1",
            "key2": "value2"
        }
    }

    # 上传 JSON 数据到 IPFS
    ipfs = IPFS()

    cid = ipfs.upload_json_to_ipfs(test_json)
    print(f"Uploaded JSON to IPFS with CID: {cid}")

    # 从 IPFS 中检索 JSON 数据
    retrieved_json = ipfs.retrieve_json_from_ipfs(cid)
    print(f"Retrieved JSON from IPFS: {retrieved_json}")
