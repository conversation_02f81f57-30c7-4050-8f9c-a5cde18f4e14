import os
# 禁用代理（仅对当前 Python 脚本）
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)

import numpy as np
import pandas as pd
import re
import random
from datetime import datetime, timedelta
import json
from argparse import Namespace
from risk_management import RiskManager



STARTING_NET_WORTH = 1_000_000
STARTING_CASH_RATIO = 0.5
# STARTING_CASH_RATIO = 1
GAS_LIMITS = 21000  # unit
GAS_PRICE = 70  # gwei
GAS_FEE = GAS_LIMITS * GAS_PRICE * 1e-9  # eth per txn
EX_RATE = 4e-3  # exchange fee = txn_amount * ex_rate
# GAS_FEE = 0
# EX_RATE = 0
SMA_PERIODS = [5, 10, 15, 20, 30]


def get_paths(args):
    dataset = args.dataset.lower()
    # 使用新的数据文件
    price_dict = {'eth': 'eth_daily.csv', 'btc': 'bitcoin_daily_price.csv', 'bnb': 'bnb_daily_price.csv', 'sol': 'solana_daily_price.csv'}
    txn_dict = {'eth': 'eth_more_transaction_statistics.csv', 'btc': 'bitcoin_transaction_statistics.csv', 'bnb': 'bnb_transaction_statistics.csv', 'sol': 'solana_transaction_statistics.csv'}
    news_dict = {'eth': 'gnews', 'btc': 'selected_bitcoin_202301_202401', 'bnb': 'selected_bitcoin_202301_202401', 'sol': 'selected_solana_202301_202401'}
    # 各数据文件的时间列
    timecol_dict = {'eth': 'snapped_at', 'btc': 'timeOpen', 'bnb': 'timestamp', 'sol': 'timeOpen'}
    # 各数据文件的时间格式
    price_timefmt_dict = {'eth': "%Y-%m-%d %H:%M:%S UTC", 'btc': "%Y-%m-%dT%H:%M:%S.%fZ", 'bnb': "%Y-%m-%dT%H:%M:%S.%fZ", 'sol': "%Y-%m-%dT%H:%M:%S.%fZ"}
    # 交易统计数据的时间格式
    # ETH: MM/DD/YY HH:MM (例如: 1/1/23 0:00)
    # BTC: YYYY-MM-DD HH:MM:SS.SSS UTC (例如: 2023-01-01 00:00:00.000 UTC)
    # BNB: YYYY-MM-DD (例如: 2023-01-01)
    # SOL: YYYY-MM-DD HH:MM:SS.SSS UTC (例如: 2023-01-01 00:00:00.000 UTC)
    txn_timefmt_dict = {'eth': "%m/%d/%y %H:%M", 'btc': "%Y-%m-%d %H:%M:%S.%f UTC", 'bnb': "%Y-%m-%d", 'sol': "%Y-%m-%d %H:%M:%S.%f UTC"}
    return f'data/{price_dict[dataset]}', f'data/{txn_dict[dataset]}', f'data/{news_dict[dataset]}', timecol_dict[dataset], price_timefmt_dict[dataset], txn_timefmt_dict[dataset]


class ETHTradingEnv:
    def __init__(self, args):
        price_path, txn_path, self.news_dir, self.timecol, self.price_timefmt, txn_timefmt = get_paths(args)
        starting_date, ending_date = args.starting_date, args.ending_date
        df = pd.read_csv(price_path)
        df = df.sort_values(self.timecol)

        # 使用新的日期解析逻辑
        print(f"开始解析{args.dataset.upper()}数据日期")

        # 创建一个函数来解析单个日期字符串
        def parse_single_date(date_str):
            try:
                # 如果已经是datetime对象，直接返回
                if hasattr(date_str, 'strftime'):
                    return date_str

                # 转换为字符串并清理
                date_str = str(date_str).strip()

                # 记录原始日期字符串，用于调试
                original_date_str = date_str

                # 尝试解析日期
                try:
                    # 处理不同格式的日期字符串

                    # 1. 处理带有时间的格式

                    # 处理 MM/DD/YY HH:MM 格式 (ETH交易统计数据)
                    if '/' in date_str and ' ' in date_str and ':' in date_str:
                        parts = date_str.split(' ')[0].split('/')
                        if len(parts) == 3:
                            try:
                                month, day, year = parts
                                # 处理两位数年份
                                if len(year) == 2:
                                    year = '20' + year
                                # 直接使用datetime解析
                                try:
                                    dt = datetime(int(year), int(month), int(day))
                                    return pd.Timestamp(dt)
                                except ValueError:
                                    # 如果解析失败，可能是日期和月份顺序不同
                                    print(f"尝试交换月份和日期: {date_str}")
                                    try:
                                        dt = datetime(int(year), int(day), int(month))
                                        return pd.Timestamp(dt)
                                    except ValueError:
                                        pass
                            except Exception as e:
                                print(f"警告: 解析MM/DD/YY HH:MM格式失败 '{date_str}': {e}")

                    # 处理ISO格式 (YYYY-MM-DDT...)
                    if 'T' in date_str:
                        date_str = date_str.split('T')[0]
                    # 处理带UTC的格式 (YYYY-MM-DD HH:MM:SS UTC)
                    elif ' UTC' in date_str:
                        date_str = date_str.split(' ')[0]
                    # 处理带有未识别时区"U"的格式 (YYYY-MM-DD HH:MM:SS U)
                    elif ' U' in date_str:
                        date_str = date_str.split(' ')[0]
                    # 处理带时间的格式 (YYYY-MM-DD HH:MM:SS)
                    elif ' ' in date_str:
                        parts = date_str.split(' ')
                        if len(parts) >= 1:
                            if parts[0].count('-') == 2:
                                # 标准日期格式 YYYY-MM-DD
                                date_str = parts[0]
                            elif parts[0].count('/') == 2:
                                # 处理 MM/DD/YY 或 DD/MM/YY 格式
                                date_parts = parts[0].split('/')
                                if len(date_parts) == 3:
                                    # 尝试解析为MM/DD/YY格式
                                    try:
                                        month, day, year = date_parts
                                        # 处理两位数年份
                                        if len(year) == 2:
                                            year = '20' + year
                                        date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                    except Exception as e:
                                        print(f"警告: 解析日期部分失败 '{parts[0]}': {e}")

                    # 2. 处理纯日期格式

                    # 处理 YYYY-MM-DD 格式
                    if date_str.count('-') == 2:
                        try:
                            dt = datetime.strptime(date_str, "%Y-%m-%d")
                            return pd.Timestamp(dt)
                        except ValueError:
                            pass

                    # 处理 MM/DD/YY 或 DD/MM/YY 格式
                    if '/' in date_str and date_str.count('/') == 2:
                        parts = date_str.split('/')
                        if len(parts) == 3:
                            # 尝试MM/DD/YY格式
                            try:
                                month, day, year = parts
                                # 处理两位数年份
                                if len(year) == 2:
                                    year = '20' + year
                                dt = datetime(int(year), int(month), int(day))
                                return pd.Timestamp(dt)
                            except ValueError:
                                # 尝试DD/MM/YY格式
                                try:
                                    day, month, year = parts
                                    # 处理两位数年份
                                    if len(year) == 2:
                                        year = '20' + year
                                    dt = datetime(int(year), int(month), int(day))
                                    return pd.Timestamp(dt)
                                except ValueError:
                                    pass

                    # 3. 使用pandas的灵活解析作为后备方案

                    # 尝试使用pandas解析，不指定格式
                    dt = pd.to_datetime(original_date_str, errors='coerce')
                    if not pd.isna(dt):
                        return dt

                    # 如果解析失败，尝试使用更灵活的方式
                    print(f"警告: 无法解析日期 '{original_date_str}'，尝试使用更灵活的方式")

                    # 尝试使用混合格式解析
                    dt = pd.to_datetime(original_date_str, format='mixed', errors='coerce')
                    if not pd.isna(dt):
                        return dt

                    # 如果所有尝试都失败，返回None
                    print(f"错误: 所有解析尝试都失败 '{original_date_str}'")
                    return None

                except Exception as e:
                    print(f"警告: 解析日期时出错 '{original_date_str}': {e}")
                    # 尝试使用更灵活的解析方式
                    return pd.to_datetime(original_date_str, errors='coerce')
            except Exception as e:
                print(f"警告: 无法解析日期 '{date_str}': {e}")
                return None

        # 应用解析函数到整个列
        df['date'] = df[self.timecol].apply(parse_single_date)

        # 检查是否有解析失败的日期
        null_dates = df['date'].isnull().sum()
        if null_dates > 0:
            print(f"警告: {null_dates}个日期解析失败，这些行将被丢弃")
            df = df.dropna(subset=['date'])

        print(f"成功: 解析{args.dataset.upper()}数据日期，共{len(df)}行有效数据")

        # Initialize risk manager
        self.risk_window_size = getattr(args, 'risk_window_size', 5)
        self.risk_confidence = getattr(args, 'risk_confidence', 0.95)
        self.risk_threshold = getattr(args, 'risk_threshold', 0.1)
        self.risk_manager = RiskManager(
            window_size=self.risk_window_size,
            confidence_level=self.risk_confidence,
            risk_threshold=self.risk_threshold
        )
        self.daily_returns = []

        # MCP功能已移除，始终使用本地新闻数据
        self.use_mcp = False

        # SMA
        for period in SMA_PERIODS:
            df[f'SMA_{period}'] = df['open'].rolling(window=period).mean()
            df[f'STD_{period}'] = df['open'].rolling(window=period).std()
        # MACD and Signal Line
        df['EMA_12'] = df['open'].ewm(span=12, adjust=False).mean()
        df['EMA_26'] = df['open'].ewm(span=26, adjust=False).mean()
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['Signal_Line'] = df['MACD'].ewm(span=9, adjust=False).mean()
        self.data = df[(df['date'] >= starting_date) & (df['date'] <= ending_date)]  # only use ending_date for open price

        # 加载交易统计数据
        print(f"加载交易统计数据: {txn_path}")
        self.txn_stat = pd.read_csv(txn_path).sort_values('day')

        # 使用相同的日期解析逻辑处理交易统计数据
        print(f"解析交易统计数据日期")

        # 应用解析函数到day列
        self.txn_stat['date'] = self.txn_stat['day'].apply(parse_single_date)

        # 检查是否有解析失败的日期
        null_dates = self.txn_stat['date'].isnull().sum()
        if null_dates > 0:
            print(f"警告: {null_dates}个交易统计数据日期解析失败，这些行将被丢弃")
            self.txn_stat = self.txn_stat.dropna(subset=['date'])

        print(f"成功: 解析交易统计数据日期，共{len(self.txn_stat)}行有效数据")
        self.total_steps = len(self.data)
        self.starting_net_worth = STARTING_NET_WORTH
        self.starting_cash_ratio = STARTING_CASH_RATIO
        # self.reset()

    def _parse_date_to_datetime(self, date_str):
        """
        将各种格式的日期字符串解析为datetime对象，只保留日期部分（年月日）

        Args:
            date_str: 日期字符串，可能包含时间和时区信息

        Returns:
            datetime: 只包含年月日的datetime对象
        """
        # 如果已经是datetime对象，直接提取日期部分
        if hasattr(date_str, 'strftime'):
            return datetime(date_str.year, date_str.month, date_str.day)

        # 转换为字符串并清理
        date_str = str(date_str).strip()

        # 记录原始日期字符串，用于日志
        original_date_str = date_str

        try:
            # 1. 处理特殊格式

            # 处理 MM/DD/YY HH:MM 格式 (ETH交易统计数据)
            if '/' in date_str and ' ' in date_str and ':' in date_str:
                parts = date_str.split(' ')[0].split('/')
                if len(parts) == 3:
                    try:
                        month, day, year = parts
                        # 处理两位数年份
                        if len(year) == 2:
                            year = '20' + year
                        # 直接使用datetime解析
                        try:
                            dt = datetime(int(year), int(month), int(day))
                            print(f"日期解析: 成功解析MM/DD/YY HH:MM格式 '{original_date_str}' -> {dt.strftime('%Y-%m-%d')}")
                            return dt
                        except ValueError:
                            # 如果解析失败，可能是日期和月份顺序不同
                            print(f"尝试交换月份和日期: {original_date_str}")
                            try:
                                dt = datetime(int(year), int(day), int(month))
                                print(f"日期解析: 成功解析DD/MM/YY HH:MM格式 '{original_date_str}' -> {dt.strftime('%Y-%m-%d')}")
                                return dt
                            except ValueError:
                                pass
                    except Exception as e:
                        print(f"警告: 解析MM/DD/YY HH:MM格式失败 '{original_date_str}': {e}")

            # 2. 提取日期部分

            # 处理ISO格式 (YYYY-MM-DDT...)
            if 'T' in date_str:
                date_str = date_str.split('T')[0]
                print(f"日期解析: 从ISO格式提取日期部分 '{original_date_str}' -> '{date_str}'")

            # 处理带UTC的格式 (YYYY-MM-DD HH:MM:SS UTC)
            elif ' UTC' in date_str:
                date_str = date_str.split(' ')[0]
                print(f"日期解析: 从UTC格式提取日期部分 '{original_date_str}' -> '{date_str}'")

            # 处理带有未识别时区"U"的格式 (YYYY-MM-DD HH:MM:SS U)
            elif ' U' in date_str:
                date_str = date_str.replace(' U', '')
                print(f"日期解析: 从带U时区格式提取日期部分 '{original_date_str}' -> '{date_str}'")
                # 进一步提取日期部分
                if ' ' in date_str and date_str.split(' ')[0].count('-') == 2:
                    date_str = date_str.split(' ')[0]

            # 处理带时间的格式 (YYYY-MM-DD HH:MM:SS)
            elif ' ' in date_str:
                parts = date_str.split(' ')
                if len(parts) >= 1:
                    if parts[0].count('-') == 2:
                        # 标准日期格式 YYYY-MM-DD
                        date_str = parts[0]
                        print(f"日期解析: 从时间格式提取日期部分 '{original_date_str}' -> '{date_str}'")
                    elif parts[0].count('/') == 2:
                        # 处理 MM/DD/YY 或 DD/MM/YY 格式
                        date_parts = parts[0].split('/')
                        if len(date_parts) == 3:
                            # 尝试解析为MM/DD/YY格式
                            try:
                                month, day, year = date_parts
                                # 处理两位数年份
                                if len(year) == 2:
                                    year = '20' + year
                                date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                print(f"日期解析: 从MM/DD/YY格式转换 '{original_date_str}' -> '{date_str}'")
                            except Exception as e:
                                print(f"警告: 解析日期部分失败 '{parts[0]}': {e}")

            # 3. 处理纯日期格式

            # 处理 MM/DD/YY 格式 (不带时间)
            if '/' in date_str and date_str.count('/') == 2 and ' ' not in date_str:
                parts = date_str.split('/')
                if len(parts) == 3:
                    # 尝试MM/DD/YY格式
                    try:
                        month, day, year = parts
                        # 处理两位数年份
                        if len(year) == 2:
                            year = '20' + year
                        date_str = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        print(f"日期解析: 从MM/DD/YY格式转换 '{original_date_str}' -> '{date_str}'")
                    except Exception as e:
                        print(f"警告: 解析MM/DD/YY格式失败 '{original_date_str}': {e}")

            # 4. 尝试解析处理后的日期字符串

            # 尝试使用标准日期格式解析
            try:
                dt = datetime.strptime(date_str, "%Y-%m-%d")
                print(f"日期解析: 成功解析日期 '{date_str}' -> {dt.strftime('%Y-%m-%d')}")
                return dt
            except ValueError:
                # 如果标准格式失败，尝试使用pandas更灵活的解析
                try:
                    # 使用format='%Y-%m-%d'避免时区警告
                    try:
                        dt = pd.to_datetime(date_str, format='%Y-%m-%d', errors='raise')
                    except ValueError:
                        # 如果指定格式失败，使用更灵活的解析
                        dt = pd.to_datetime(date_str, errors='coerce')

                    if pd.isna(dt):
                        # 如果解析失败，尝试使用原始字符串
                        dt = pd.to_datetime(original_date_str, errors='coerce')
                        if pd.isna(dt):
                            raise ValueError(f"pandas无法解析日期: {date_str}")

                    dt = dt.to_pydatetime()
                    # 只保留年月日部分
                    dt = datetime(dt.year, dt.month, dt.day)
                    print(f"日期解析: 使用pandas解析日期 '{date_str}' -> {dt.strftime('%Y-%m-%d')}")
                    return dt
                except Exception as e:
                    print(f"日期解析: pandas解析失败 '{date_str}': {e}")
                    # 尝试使用混合格式解析
                    try:
                        dt = pd.to_datetime(original_date_str, format='mixed', errors='coerce')
                        if not pd.isna(dt):
                            dt = dt.to_pydatetime()
                            dt = datetime(dt.year, dt.month, dt.day)
                            print(f"日期解析: 使用混合格式成功解析日期 '{original_date_str}' -> {dt.strftime('%Y-%m-%d')}")
                            return dt
                    except Exception:
                        pass

                    # 如果所有尝试都失败，抛出异常
                    raise ValueError(f"无法解析日期: '{original_date_str}'")

        except Exception as e:
            print(f"警告: 无法解析日期 '{original_date_str}': {e}")
            # 尝试使用pandas的混合格式解析作为最后的尝试
            try:
                dt = pd.to_datetime(original_date_str, format='mixed', errors='coerce')
                if not pd.isna(dt):
                    dt = dt.to_pydatetime()
                    dt = datetime(dt.year, dt.month, dt.day)
                    print(f"日期解析: 使用混合格式成功解析日期 '{original_date_str}' -> {dt.strftime('%Y-%m-%d')}")
                    return dt
            except Exception as inner_e:
                print(f"警告: 混合格式解析也失败 '{original_date_str}': {inner_e}")

            # 如果所有尝试都失败，抛出异常
            raise ValueError(f"无法解析日期: '{original_date_str}'")

    def get_close_state(self, today, next_day, first_day=False):
        next_open_price = next_day['open']
        close_net_worth = self.cash + self.eth_held * next_open_price
        close_roi = close_net_worth / self.starting_net_worth - 1  # return on investment
        today_roi = close_net_worth / self.last_net_worth - 1
        self.last_net_worth = close_net_worth

        date = today[self.timecol]

        # 使用新的日期解析方法
        try:
            parsed_time = self._parse_date_to_datetime(date)
        except ValueError as e:
            print(f"错误: {e}")
            # 如果解析失败，使用当前日期作为后备选项
            parsed_time = datetime.now()
            print(f"使用当前日期作为后备选项: {parsed_time.strftime('%Y-%m-%d')}")

        if first_day:
            parsed_time = parsed_time - timedelta(days=1)
        year, month, day = parsed_time.year, parsed_time.month, parsed_time.day

        # next day's opening technical indicators
        ma5 = next_day['SMA_5']
        ma10 = next_day['SMA_10']
        ma15 = next_day['SMA_15']
        ma20 = next_day['SMA_20']
        slma_signal = 'hold'
        short_ma = ma15
        long_ma = ma20
        if short_ma > long_ma:
            slma_signal = 'sell'
        elif short_ma < long_ma:
            slma_signal = 'buy'

        sma = next_day[f'SMA_20']
        sd = next_day[f'STD_20']
        multiplier = 2
        upper_band = sma + (sd * multiplier)
        lower_band = sma - (sd * multiplier)
        boll_signal = 'hold'
        if next_open_price < lower_band:
            boll_signal = 'buy'
        elif next_open_price > upper_band:
            boll_signal = 'sell'

        macd = next_day['MACD']
        macd_signal_line = next_day['Signal_Line']
        macd_signal = 'hold'
        if macd < macd_signal_line:
            macd_signal = 'buy'
        elif macd > macd_signal_line:
            macd_signal = 'sell'

        # today's txn stats
        # 打印日期信息，用于调试
        print(f"查找交易统计数据: 日期={parsed_time.strftime('%Y-%m-%d')}")

        # 将parsed_time转换为只包含日期部分的datetime对象
        parsed_date = datetime(parsed_time.year, parsed_time.month, parsed_time.day)

        # 将txn_stat中的date列也转换为只包含日期部分的datetime对象
        # 使用更健壮的方式处理可能的NaT值
        def extract_date_parts(x):
            if pd.isna(x):
                return None
            try:
                return datetime(x.year, x.month, x.day)
            except Exception as e:
                print(f"警告: 提取日期部分失败: {e}")
                return None

        date_only_series = self.txn_stat['date'].apply(extract_date_parts)

        # 使用转换后的日期进行匹配
        txn_stat = self.txn_stat[date_only_series == parsed_date]

        # 如果没有找到匹配的记录，尝试使用字符串比较
        if txn_stat.empty:
            print(f"尝试使用字符串比较查找日期 {parsed_date.strftime('%Y-%m-%d')} 的交易统计数据")
            date_str = parsed_date.strftime('%Y-%m-%d')
            # 将日期转换为字符串格式进行比较
            date_str_series = self.txn_stat['date'].apply(lambda x: x.strftime('%Y-%m-%d') if not pd.isna(x) else '')
            txn_stat = self.txn_stat[date_str_series == date_str]

        txn_cols = set(self.txn_stat.columns.tolist()) - set(['date', 'day'])
        if txn_stat.empty:
            print(f"警告: 未找到日期 {parsed_date.strftime('%Y-%m-%d')} 的交易统计数据")
            txn_data = {col: 'N/A' for col in txn_cols}
        else:
            print(f"成功: 找到日期 {parsed_date.strftime('%Y-%m-%d')} 的交易统计数据")
            txn_data = {col: txn_stat[col].values[0] for col in txn_cols}

        # today's news
        date_str = f"{year}-{str(month).zfill(2)}-{str(day).zfill(2)}"

        # MCP功能已移除，始终使用本地新闻数据
        news = self._get_news_from_file(date_str)

        close_state = {  # selectively used in prompt
            'cash': self.cash,
            'eth_held': self.eth_held,
            'open': next_open_price,
            'net_worth': close_net_worth,
            'roi': close_roi,
            'today_roi': today_roi,
            'technical': {
                # 'short_long_ma_signal': slma_signal,
                'macd_signal': macd_signal,
                # 'bollinger_bands_signal': boll_signal,
            },
            'txnstat': txn_data,
            'news': news,
            'date': date,
        }
        return close_state

    def reset(self):
        self.current_step = 0
        next_day = today = self.data.iloc[self.current_step]
        self.starting_price = today['open']
        self.cash = self.starting_net_worth * STARTING_CASH_RATIO
        self.eth_held = (self.starting_net_worth - self.cash) / self.starting_price
        self.last_net_worth = self.starting_net_worth

        # Reset risk manager and daily returns
        self.risk_manager = RiskManager(
            window_size=self.risk_window_size,
            confidence_level=self.risk_confidence,
            risk_threshold=self.risk_threshold
        )
        self.daily_returns = []

        close_state = self.get_close_state(today, next_day, first_day=True)
        info = {
            'starting_cash': self.cash,
        }
        reward = 0
        self.done = False
        self.last_state = close_state
        return close_state, reward, self.done, info

    # the agent receives last state and reward, takes an action, and receives new state and reward.
    # last state: yesterday's news, today's open price, cash, held ETH
    # last reward: yesterday's profit
    # action: buy, sell, or hold
    # new state: today's news, tomorrow's open price, cash, held ETH
    # new reward: today's profit
    def step(self, action):
        raw_action = action
        if type(action) == str:
            # actions = re.findall(r"[-+]?\d*\.\d+|\d+", action)
            actions = re.findall(r"-?(?:0(?:\.\d{1})|1\.0)", action)

            if len(actions) == 0:
                print(f'ERROR: Invalid llm response: {action}. Set to no action.')
                action = 0.00
            elif len(actions) == 1:
                action = float(actions[0])
            else:
                # print(f'Multiple actions in llm response: {action}. Pick one action.')
                # action = float(actions[0])
                action = float(actions[-1])

        if not -1 <= action <= 1:
            print(f"ERROR: Invalid action: {action}. Set to no action.")
            action = 0.00

        today = self.data.iloc[self.current_step]
        next_day = self.data.iloc[self.current_step + 1]
        open_price = today['open']
        next_open_price = next_day['open']  # assume today's close = next day's open

        if -1 <= action < 0 and self.eth_held > 0:  # -1 sell
            eth_diff = abs(action) * self.eth_held
            cash_diff = eth_diff * open_price
            self.eth_held -= eth_diff
            self.cash += cash_diff
            self.cash -= GAS_FEE * open_price + cash_diff * EX_RATE
        if 0 < action <= 1 and self.cash > 0:  # 1 buy
            cash_diff = abs(action) * self.cash
            eth_diff = cash_diff / open_price
            self.cash -= cash_diff
            self.eth_held += eth_diff
            self.cash -= GAS_FEE * open_price + cash_diff * EX_RATE

        self.current_step += 1
        if self.current_step >= self.total_steps - 1:
            self.done = True

        close_state = self.get_close_state(today, next_day)
        reward = close_state['roi'] - self.last_state['roi']  # reward = today's roi gain.

        # Update risk manager with daily return
        daily_return = close_state['today_roi']
        self.daily_returns.append(daily_return)
        self.risk_manager.update(daily_return)

        # Generate risk warning if necessary
        risk_warning = self.risk_manager.generate_warning()
        risk_metrics = self.risk_manager.get_risk_metrics()

        # Add risk metrics to state
        close_state['risk'] = {
            'cvar': risk_metrics.get('cvar'),
            'cvar_change': risk_metrics.get('cvar_change'),
            'warning': risk_warning
        }

        self.last_state = close_state
        info = {
            'raw_action': raw_action,
            'actual_action': action,
            'starting_cash': self.starting_net_worth,
            'ref_all_in': self.starting_net_worth / self.starting_price * next_open_price,
            'today': today[self.timecol],
            'risk_warning': risk_warning
        }
        return close_state, reward, self.done, info

    def close(self):
        pass

    def get_current_date(self):
        """获取当前交易日的日期，格式为YYYY-MM-DD"""
        if self.current_step < len(self.data):
            today = self.data.iloc[self.current_step]
            date_str = today[self.timecol]
            try:
                # 使用新的日期解析方法
                dt = self._parse_date_to_datetime(date_str)
                if dt is not None:
                    return dt.strftime("%Y-%m-%d")
                else:
                    # 如果解析失败，尝试使用pandas直接解析
                    dt = pd.to_datetime(date_str, errors='coerce')
                    if not pd.isna(dt):
                        return dt.strftime("%Y-%m-%d")
                    else:
                        print(f"警告: 无法解析日期 '{date_str}'，返回原始字符串")
                        return str(date_str)
            except Exception as e:
                print(f"警告: 获取当前日期失败: {e}")
                # 尝试使用pandas的混合格式解析作为最后的尝试
                try:
                    dt = pd.to_datetime(date_str, format='mixed', errors='coerce')
                    if not pd.isna(dt):
                        return dt.strftime("%Y-%m-%d")
                except Exception:
                    pass
                return str(date_str)
        return None

    def _get_news_from_file(self, date_str):
        """从文件中获取新闻数据"""
        # 确保日期字符串格式正确
        try:
            # 尝试解析日期字符串，确保格式为YYYY-MM-DD
            dt = pd.to_datetime(date_str, errors='coerce')
            if not pd.isna(dt):
                # 重新格式化日期字符串
                date_str = dt.strftime("%Y-%m-%d")
        except Exception as e:
            print(f"警告: 格式化新闻日期失败: {e}")

        news_path = f"{self.news_dir}/{date_str}.json"
        print(f"尝试加载新闻文件: {news_path}")

        if not os.path.exists(news_path):
            print(f"警告: 未找到日期 {date_str} 的新闻文件")
            # 尝试查找相近日期的新闻文件
            try:
                news_dir_files = os.listdir(self.news_dir)
                json_files = [f for f in news_dir_files if f.endswith('.json')]
                if json_files:
                    # 按文件名排序
                    json_files.sort()
                    # 找到最接近的日期文件
                    closest_file = min(json_files, key=lambda x: abs((pd.to_datetime(x.split('.')[0], errors='coerce') - pd.to_datetime(date_str, errors='coerce')).total_seconds() if not pd.isna(pd.to_datetime(x.split('.')[0], errors='coerce')) and not pd.isna(pd.to_datetime(date_str, errors='coerce')) else float('inf')))
                    print(f"尝试使用最接近的新闻文件: {closest_file}")
                    return self._load_news_from_file(f"{self.news_dir}/{closest_file}")
            except Exception as e:
                print(f"警告: 查找相近日期新闻文件失败: {e}")
            return 'N/A'
        else:
            return self._load_news_from_file(news_path)

    def _load_news_from_file(self, file_path):
        """从指定路径加载新闻文件"""
        try:
            loaded_news = json.load(open(file_path))
            seen_titles = set()  # remove duplicate
            news = []
            for loaded_item in loaded_news:
                if loaded_item['title'] not in seen_titles:
                    item = {k: loaded_item[k] for k in ['id', 'time', 'title', 'content']}  # omit url
                    K = 5000  # clip characters
                    if len(item['content']) > K:
                        item['content'] = item['content'][:K] + '...'
                    news.append(item)
                    seen_titles.add(item['title'])
            return news
        except Exception as e:
            print(f"从文件加载新闻失败: {e}")
            return 'N/A'
