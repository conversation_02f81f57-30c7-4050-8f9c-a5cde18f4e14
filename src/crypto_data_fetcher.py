#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
加密货币历史数据获取工具

获取比特币(BTC)、以太坊(ETH)和币安币(BNB)从面世以来到现在的每日数据，
并以CSV格式保存，包含开盘价、收盘价、最高价、最低价和交易量等基本数据。

使用CoinGecko API获取数据。
"""

import os
import time
import pandas as pd
import requests
from datetime import datetime, timedelta
import argparse
import logging
from tqdm import tqdm

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crypto_data_fetcher.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# CoinGecko API基础URL
BASE_URL = "https://api.coingecko.com/api/v3"

# 加密货币ID映射
CRYPTO_IDS = {
    "btc": "bitcoin",
    "eth": "ethereum",
    "bnb": "binancecoin"
}

# 加密货币面世日期（大约）
LAUNCH_DATES = {
    "btc": "2009-01-03",  # 比特币创世区块日期
    "eth": "2015-07-30",  # 以太坊主网上线日期
    "bnb": "2017-07-01"   # 币安币发行日期（近似）
}

def fetch_with_retry(url, params=None, max_retries=5, initial_delay=1):
    """
    带重试机制的API请求函数
    
    Args:
        url: API请求URL
        params: 请求参数
        max_retries: 最大重试次数
        initial_delay: 初始延迟时间（秒）
        
    Returns:
        响应JSON数据
    """
    delay = initial_delay
    for retry in range(max_retries):
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.warning(f"请求失败 (尝试 {retry+1}/{max_retries}): {e}")
            if retry == max_retries - 1:
                logger.error(f"达到最大重试次数，请求失败: {url}")
                raise
            logger.info(f"等待 {delay} 秒后重试...")
            time.sleep(delay)
            delay *= 2  # 指数退避

def get_coin_history(coin_id, from_date, to_date=None, vs_currency="usd"):
    """
    获取指定加密货币的历史价格数据
    
    Args:
        coin_id: CoinGecko上的加密货币ID
        from_date: 起始日期 (YYYY-MM-DD)
        to_date: 结束日期 (YYYY-MM-DD)，默认为今天
        vs_currency: 计价货币，默认为美元
        
    Returns:
        包含历史价格数据的DataFrame
    """
    if to_date is None:
        to_date = datetime.now().strftime("%Y-%m-%d")
    
    # 转换日期为Unix时间戳
    from_timestamp = int(datetime.strptime(from_date, "%Y-%m-%d").timestamp())
    to_timestamp = int(datetime.strptime(to_date, "%Y-%m-%d").timestamp())
    
    url = f"{BASE_URL}/coins/{coin_id}/market_chart/range"
    params = {
        "vs_currency": vs_currency,
        "from": from_timestamp,
        "to": to_timestamp
    }
    
    logger.info(f"获取 {coin_id} 从 {from_date} 到 {to_date} 的历史数据...")
    
    try:
        data = fetch_with_retry(url, params)
        
        # 提取价格、市值和交易量数据
        prices = data.get("prices", [])
        market_caps = data.get("market_caps", [])
        total_volumes = data.get("total_volumes", [])
        
        # 确保所有数据长度一致
        min_length = min(len(prices), len(market_caps), len(total_volumes))
        
        # 创建数据列表
        records = []
        for i in range(min_length):
            timestamp = prices[i][0]
            date = datetime.fromtimestamp(timestamp / 1000).strftime("%Y-%m-%d")
            price = prices[i][1]
            market_cap = market_caps[i][1]
            volume = total_volumes[i][1]
            
            records.append({
                "date": date,
                "price": price,
                "market_cap": market_cap,
                "volume": volume
            })
        
        # 创建DataFrame
        df = pd.DataFrame(records)
        
        # 按日期去重（保留最后一个）
        df = df.drop_duplicates(subset=["date"], keep="last")
        
        # 按日期排序
        df = df.sort_values("date")
        
        return df
    
    except Exception as e:
        logger.error(f"获取 {coin_id} 历史数据失败: {e}")
        return pd.DataFrame()

def get_coin_ohlc(coin_id, date, vs_currency="usd"):
    """
    获取指定加密货币在特定日期的OHLC数据
    
    Args:
        coin_id: CoinGecko上的加密货币ID
        date: 日期 (YYYY-MM-DD)
        vs_currency: 计价货币，默认为美元
        
    Returns:
        包含OHLC数据的字典
    """
    # 计算日期范围（前后各1天，确保能获取到当天数据）
    date_obj = datetime.strptime(date, "%Y-%m-%d")
    from_date = (date_obj - timedelta(days=1)).strftime("%Y-%m-%d")
    to_date = (date_obj + timedelta(days=1)).strftime("%Y-%m-%d")
    
    # 转换日期为Unix时间戳
    from_timestamp = int(datetime.strptime(from_date, "%Y-%m-%d").timestamp())
    to_timestamp = int(datetime.strptime(to_date, "%Y-%m-%d").timestamp())
    
    url = f"{BASE_URL}/coins/{coin_id}/ohlc"
    params = {
        "vs_currency": vs_currency,
        "from": from_timestamp,
        "to": to_timestamp,
        "days": 3  # 获取3天的数据，确保包含目标日期
    }
    
    try:
        data = fetch_with_retry(url, params)
        
        # 筛选目标日期的数据
        target_data = None
        for item in data:
            item_date = datetime.fromtimestamp(item[0] / 1000).strftime("%Y-%m-%d")
            if item_date == date:
                target_data = {
                    "timestamp": item[0],
                    "open": item[1],
                    "high": item[2],
                    "low": item[3],
                    "close": item[4]
                }
                break
        
        return target_data
    
    except Exception as e:
        logger.error(f"获取 {coin_id} 在 {date} 的OHLC数据失败: {e}")
        return None

def fetch_complete_history(crypto, vs_currency="usd"):
    """
    获取指定加密货币的完整历史数据（从面世到现在）
    
    Args:
        crypto: 加密货币代码 (btc, eth, bnb)
        vs_currency: 计价货币，默认为美元
        
    Returns:
        包含完整历史数据的DataFrame
    """
    coin_id = CRYPTO_IDS.get(crypto.lower())
    if not coin_id:
        logger.error(f"不支持的加密货币: {crypto}")
        return pd.DataFrame()
    
    launch_date = LAUNCH_DATES.get(crypto.lower())
    today = datetime.now().strftime("%Y-%m-%d")
    
    logger.info(f"获取 {crypto.upper()} 从 {launch_date} 到 {today} 的完整历史数据...")
    
    # 获取基础历史数据
    df = get_coin_history(coin_id, launch_date, today, vs_currency)
    
    if df.empty:
        logger.error(f"无法获取 {crypto.upper()} 的历史数据")
        return df
    
    # 添加OHLC数据
    logger.info(f"获取 {crypto.upper()} 的OHLC数据...")
    
    # 准备OHLC数据列
    df["open"] = None
    df["high"] = None
    df["low"] = None
    df["close"] = None
    
    # 由于CoinGecko API限制，我们需要分批获取OHLC数据
    # 为避免API限制，每次请求后等待一段时间
    unique_dates = df["date"].unique()
    
    for date in tqdm(unique_dates, desc=f"获取 {crypto.upper()} OHLC数据"):
        ohlc_data = get_coin_ohlc(coin_id, date, vs_currency)
        if ohlc_data:
            df.loc[df["date"] == date, "open"] = ohlc_data["open"]
            df.loc[df["date"] == date, "high"] = ohlc_data["high"]
            df.loc[df["date"] == date, "low"] = ohlc_data["low"]
            df.loc[df["date"] == date, "close"] = ohlc_data["close"]
        
        # 避免API限制，每次请求后等待
        time.sleep(1.5)
    
    # 重命名列
    df = df.rename(columns={
        "price": "close_price",
        "volume": "volume",
        "market_cap": "market_cap"
    })
    
    # 如果OHLC数据不完整，使用close_price填充
    df["close"] = df["close"].fillna(df["close_price"])
    df["open"] = df["open"].fillna(df["close_price"])
    df["high"] = df["high"].fillna(df["close_price"])
    df["low"] = df["low"].fillna(df["close_price"])
    
    return df

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="获取加密货币历史数据")
    parser.add_argument("--cryptos", nargs="+", default=["btc", "eth", "bnb"],
                        help="要获取的加密货币列表 (默认: btc eth bnb)")
    parser.add_argument("--currency", default="usd",
                        help="计价货币 (默认: usd)")
    parser.add_argument("--output-dir", default="data",
                        help="输出目录 (默认: data)")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    for crypto in args.cryptos:
        crypto = crypto.lower()
        logger.info(f"开始获取 {crypto.upper()} 数据...")
        
        try:
            df = fetch_complete_history(crypto, args.currency)
            
            if not df.empty:
                # 保存为CSV
                output_file = os.path.join(args.output_dir, f"{crypto}_complete_history.csv")
                df.to_csv(output_file, index=False)
                logger.info(f"{crypto.upper()} 数据已保存到 {output_file}")
                
                # 打印数据范围
                date_range = f"{df['date'].min()} 到 {df['date'].max()}"
                logger.info(f"{crypto.upper()} 数据范围: {date_range}")
                logger.info(f"{crypto.upper()} 数据行数: {len(df)}")
            else:
                logger.error(f"无法获取 {crypto.upper()} 数据")
        
        except Exception as e:
            logger.error(f"处理 {crypto.upper()} 数据时出错: {e}")
    
    logger.info("所有数据获取完成")

if __name__ == "__main__":
    main()
