from typing import List, Dict
import os


class EnvironmentHistory:
    def __init__(self, base_query: str, start_state, memory: List[str], history: List[Dict[str, str]], args) -> None:
        self.args = args
        self._cur_query: str = f'{_get_base_query(base_query, memory)}'
        self._history: List[Dict[str, str]] = history  # prompt, action, state, ...

        self.add('state', start_state)

    def add(self, label, value) -> None:
        self._history += [{
            'label': label,
            'value': value,
        }]

    def reset(self) -> None:
        self._history = []

    def get_current_state(self) -> Dict:
        """获取当前状态"""
        for item in reversed(self._history):
            if item['label'] == 'state':
                return item['value']
        return {}

    def _load_updated_prompt(self, agent_name: str, run_name: str = None) -> str:
        """
        尝试加载更新后的提示词文件

        Args:
            agent_name: 智能体名称
            run_name: 运行名称

        Returns:
            更新后的提示词内容，如果文件不存在则返回None
        """
        if not run_name:
            print(f"🔍 [{agent_name}] 没有提供run_name，使用默认提示词")
            return None

        # 检查临时提示词文件
        temp_prompt_path = os.path.join(run_name, f'temp_{agent_name}_prompt.txt')
        print(f"🔍 [{agent_name}] 检查更新提示词文件: {temp_prompt_path}")

        if os.path.exists(temp_prompt_path):
            try:
                with open(temp_prompt_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        print(f"✅ [{agent_name}] 成功加载更新提示词 (长度: {len(content)} 字符)")
                        # 检查是否包含LLM更新的特征
                        if "Important:" in content or "风险管理" in content or "市场趋势分析" in content:
                            print(f"🔄 [{agent_name}] 检测到LLM更新的提示词特征")
                        return content
                    else:
                        print(f"⚠️ [{agent_name}] 更新提示词文件为空")
            except Exception as e:
                print(f"❌ [{agent_name}] 读取更新提示词时出错: {e}")
        else:
            print(f"📄 [{agent_name}] 更新提示词文件不存在，使用默认提示词")

        return None

    def get_prompt(self) -> str:
        price_window = self.args.price_window
        reflection_window = self.args.reflection_window
        use_tech = self.args.use_tech
        use_txnstat = self.args.use_txnstat
        use_risk = getattr(self.args, 'use_risk', True)
        delim = '\n"""\n'

        # 检查是否有更新的提示词文件
        run_name = getattr(self.args, 'run_name', None)

        # Historical Price Data Analyst
        # 尝试读取更新后的提示词，如果不存在则使用默认提示词
        onchain_prompt = self._load_updated_prompt("onchain_analyst_agent", run_name)
        if onchain_prompt:
            price_s = onchain_prompt
        else:
            price_s = """<optimized_prompt>
<task>Analyze Ethereum price data and technical indicators, recommend trading decisions</task>

<context>
You are an ETH cryptocurrency trading analyst. Your task is to analyze price data and technical indicators, then recommend a trading action.

Your task:
1. Analyze the price data and technical indicators
2. Identify key trends and patterns
3. Propose a specific trading action: 'buy', 'sell', or 'hold'
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: buy AMOUNT: 0.3")

The recent price and auxiliary information is given in chronological order below:
Write one concise paragraph to analyze the recent information and estimate the market trend. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Analyze the provided time-series price data and auxiliary information:
   - Identify key support/resistance levels
   - Calculate major technical indicators (RSI, MACD, Bollinger Bands, etc.)
   - Detect chart patterns (head and shoulders, triangle breakouts, etc.)

2. Evaluate market trend characteristics:
   - Determine short-term (24h) and medium-term (7d) trend directions
   - Analyze volume change patterns
   - Identify abnormal fluctuations or price gaps

3. Generate trading strategy recommendations:
   - Evaluate three potential operations based on risk-reward ratio
   - Consider current market volatility and leverage levels
   - Verify strategy consistency with identified technical signals

4. Determine specific trading parameters:
   - Use decimals in the 0-1 range to represent position ratios
   - Examples:
     - Conservative strategy suggests 0.2-0.4
     - Aggressive strategy suggests 0.5-0.8
   - Adjust amount ratio based on market volatility

5. Structure the final conclusion:
   - Keep analysis paragraph under 5 sentences
   - Ensure recommendation logic is clear and traceable
   - Strictly follow the specified format requirements
</instructions>

<output_format>
Must use the following exact format for output:
`ACTION: [buy|sell|hold] AMOUNT: [decimal between 0-1]`
Examples:
"ACTION: buy AMOUNT: 0.45" or "ACTION: hold AMOUNT: 0.0"
Requirements:
1. Action must be lowercase
2. Amount should have two decimal places
3. Analysis section must maintain logical consistency with the recommended result
</output_format>
</optimized_prompt>""" + delim
        for i, item in enumerate(self._history[-price_window * 3:]):
            if item['label'] == 'state':
                state = item['value']
                state_log = f'Open price: {state["open"]:.2f}'
                if use_txnstat:
                    txnstat_dict = state['txnstat']
                    for k, v in txnstat_dict.items():
                        state_log += f', {k}: {v}'
                if use_tech:
                    tech_dict = state['technical']
                    for k, v in tech_dict.items():
                        state_log += f', {k}: {v}'
                price_s += state_log + '\n'
        price_s += delim + '<output_format>Must use the following exact format for output: `ACTION: [buy|sell|hold] AMOUNT: [decimal between 0-1]` Examples: "ACTION: buy AMOUNT: 0.45" or "ACTION: hold AMOUNT: 0.0" Requirements: 1. Action must be lowercase 2. Amount should have two decimal places 3. Analysis section must maintain logical consistency with the recommended result</output_format></optimized_prompt>'

        # Get current state
        state = self._history[-1]['value']

        # Check if using merged news analyst
        merged_news = getattr(self.args, 'merged_news', 0)

        if merged_news:
            # Merged News Analyst - analyzing both factual and subjective information
            # 尝试加载更新后的提示词
            factual_prompt = self._load_updated_prompt("factual_news_analyst_agent", run_name)
            if factual_prompt:
                factual_news_s = factual_prompt + f"{delim}{state['news']}{delim}"
            else:
                factual_news_s = f"""<optimized_prompt>
<task>Comprehensive analysis of news information and providing ETH cryptocurrency trading recommendations</task>

<context>
You are an ETH cryptocurrency trading analyst specializing in comprehensive news analysis.

Your task:
1. Analyze the news articles and extract BOTH objective facts and subjective elements
2. Consider both factual information (events, data, announcements) and subjective information (sentiment, opinions, predictions)
3. Propose a specific trading action: 'buy', 'sell', or 'hold' based on your comprehensive analysis
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: buy AMOUNT: 0.3")
Write one concise paragraph summarizing BOTH the factual and subjective information from these news articles that could impact ETH price. Consider events, announcements, data, market sentiment, expert opinions, and predictions. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Perform comprehensive news analysis:
   - Identify and record objective facts in the news (events, data, official announcements)
   - Extract subjective elements (market sentiment, expert opinions, price predictions)

2. Evaluate multi-dimensional information impact:
   - Analyze the direct impact of factual information on ETH price
   - Assess the potential impact of market sentiment and public opinion trends
   - Synthesize the timeframe and credibility of expert predictions

3. Develop trading strategy recommendations:
   - Determine trading direction (buy/sell/hold) based on risk-reward ratio
   - Calculate position ratio using fund management models:
      - Consider market volatility indicators
      - Evaluate information certainty level
      - Incorporate current position status

4. Construct structured output:
   - Integrate key facts and subjective judgments in a single paragraph
   - Ensure conclusion contains clear action instructions and quantified parameters
   - Strictly follow the specified format template
</instructions>



{delim}{state['news']}{delim}

<output_format>
Output must be a single paragraph structure, containing:
1. Summary of factual elements (events/data/announcements)
2. Summary of subjective analysis (sentiment/opinions/predictions)
3. Trading recommendation using precise format:
   `ACTION: [buy|sell|hold] AMOUNT: [0-1 decimal]`
Total paragraph length should not exceed 200 words, maintain professional analytical tone, avoid subjective modifiers
</output_format>
</optimized_prompt>"""

            # For code compatibility, assign the same content to subjective news analysis
            subjective_news_s = factual_news_s

        else:
            # Factual News Analyst - focusing on objective factual information
            # 尝试加载更新后的提示词
            factual_prompt = self._load_updated_prompt("factual_news_analyst_agent", run_name)
            if factual_prompt:
                factual_news_s = factual_prompt + f"{delim}{state['news']}{delim}"
            else:
                factual_news_s = f"""<optimized_prompt>
<task>Analyze news articles and propose Ethereum trading recommendations based on objective facts</task>

<context>
You are an ETH cryptocurrency trading analyst specializing in factual information extraction.

Your task:
1. Analyze the news articles and extract ONLY objective facts, events, and data
2. Ignore opinions, predictions, and subjective judgments
3. Propose a specific trading action: 'buy', 'sell', or 'hold' based on factual information
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: buy AMOUNT: 0.3")
Write one concise paragraph summarizing ONLY the factual information from these news articles that could impact ETH price. Focus on events, announcements, data, and verifiable information. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Information extraction process:
   - Comprehensively analyze the provided news content
   - Identify and separate objective facts from subjective content
   - Mark important events, data updates, and official announcements

2. Trading factor evaluation:
   - Verify information relevance to ETH
   - Assess technical upgrades, regulatory changes, and other hard indicators
   - Analyze on-chain transaction volume, gas fees, and other quantitative data

3. Trading recommendation formulation:
   - Choose operation based on factual impact level:
     1. Positive information → buy
     2. Major risks → sell
     3. Neutral/uncertain → hold
   - Calculate transaction amount ratio:
     - Impact intensity corresponds to numerical proportion
     - Market volatility considerations

4. Output format processing:
   - First paragraph contains:
     - Factual summary (3-5 key points)
     - Direct impact explanation
   - Ending strictly uses specified format:
     - `ACTION: [action] AMOUNT: [amount]`
</instructions>



{delim}{state['news']}{delim}

<output_format>
Strictly follow this output structure:
1. Factual summary paragraph (100-150 words):
   - Only include verifiable objective information
   - Use concise professional terminology
   - Arrange in descending order of importance

2. Conclusion format requirements:
   - Separate paragraph after a blank line
   - Must use ALL CAPS labels
   - Values should have three decimal places
   - Example: ACTION: BUY AMOUNT: 0.750

3. Prohibited content:
   - Any explanatory statements
   - Subjective speculation content
   - Format marking symbols
</output_format>
</optimized_prompt>"""

            # Subjective News Analyst - focusing on opinions and sentiment
            # 尝试加载更新后的提示词
            subjective_prompt = self._load_updated_prompt("subjective_news_analyst_agent", run_name)
            if subjective_prompt:
                subjective_news_s = subjective_prompt + f"{delim}{state['news']}{delim}"
            else:
                subjective_news_s = f"""<optimized_prompt>
<task>Perform sentiment analysis on ETH-related news and propose trading recommendations</task>

<context>
You are an ETH cryptocurrency trading analyst specializing in sentiment and opinion analysis.

Your task:
1. Analyze the news articles and extract ONLY subjective elements such as market sentiment, expert opinions, predictions, and emotional reactions
2. Ignore objective facts and data
3. Propose a specific trading action: 'buy', 'sell', or 'hold' based on market sentiment
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: sell AMOUNT: 0.4")
Write one concise paragraph summarizing ONLY the subjective information from these news articles that could impact ETH price. Focus on market sentiment, expert opinions, predictions, and how the news might be perceived by traders. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Sentiment element extraction:
   - Identify market sentiment in news (optimistic/pessimistic/neutral)
   - Record expert opinions and predictive statements
   - Capture emotional reactions of traders

2. Data filtering:
   - Exclude all objective data and factual information
   - Retain only subjective judgments and predictive content

3. Trading decision formulation:
   - Evaluate market direction based on sentiment analysis results
   - Determine corresponding trading action (buy/sell/hold)

4. Trading amount calculation:
   - Determine trading ratio based on market sentiment strength
   - Use decimals from 0-1 to represent fund allocation ratio

5. Result formatting:
   - Condense analysis results into one paragraph
   - Keep paragraph concise and focused on subjective elements
   - Strictly follow specified output format
</instructions>



{delim}{state['news']}{delim}

<output_format>
Output must contain:
1. Summary paragraph with subjective elements (3-5 sentences)
2. Clear trading action recommendation (lowercase)
3. Amount ratio precise to one decimal place
4. Final format: "ACTION: [action] AMOUNT: [amount]"
Example: "ACTION: sell AMOUNT: 0.4"
</output_format>
</optimized_prompt>"""

        # Trading Reflection Analyst
        # 尝试加载更新后的提示词
        reflection_prompt = self._load_updated_prompt("reflection_analyst_agent", run_name)
        if reflection_prompt:
            reflection_s = reflection_prompt + delim
        else:
            reflection_s = """<optimized_prompt>
<task>Generate ETH cryptocurrency trading strategy recommendations</task>

<context>
You are an ETH cryptocurrency trading analyst specializing in performance reflection and strategy improvement.

Your task:
1. Analyze your past trading decisions and their outcomes
2. Identify what worked well and what didn't
3. Propose a specific trading action: 'buy', 'sell', or 'hold' for the current market
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: buy AMOUNT: 0.3")

Your analysis and action history is given in chronological order:
Reflect on your recent performance and instruct your future trades from a high level, identifying key trends and indicators in the market. Consider how factual information and subjective sentiment have influenced your decisions. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Analyze historical trading performance:
   - Categorize successful and failed trading cases
   - Evaluate market factors for each decision (volatility, trading volume, news events)
   - Establish correlation matrix between trading results and market indicators

2. Identify key market trends:
   - Parse ETH price trend patterns
   - Monitor changes in on-chain transaction volume
   - Analyze social media sentiment indicators
   - Evaluate macroeconomic impact factors

3. Develop trading strategy:
   - Synthesize technical and fundamental analysis results
   - Compare with historically similar market situations
   - Consider current market volatility and liquidity conditions

4. Determine trading amount ratio:
   - Assess total available funds
   - Calculate risk exposure thresholds
   - Adjust ratio according to confidence level

5. Format output results:
   - Strictly follow specified format standards
   - Ensure numerical precision to two decimal places
   - Verify operation type conforms to predefined options
</instructions>

""" + delim

        # 添加历史数据
        for i, item in enumerate(self._history[-reflection_window * 3:]):
            if item['label'] == 'final_trader_response':
                reflection_s += f'FINAL REASONING:\n{item["value"]}\n'
            elif item['label'] == 'factual_news_analysis':
                reflection_s += f'FACTUAL NEWS ANALYSIS:\n{item["value"]}\n'
            elif item['label'] == 'subjective_news_analysis':
                reflection_s += f'SUBJECTIVE NEWS ANALYSIS:\n{item["value"]}\n'
            elif item['label'] == 'action':
                reflection_s += f'ACTION:\n{item["value"]}\n'
            elif item['label'] == 'state':
                reflection_s += f'DAILY RETURN:\n{item["value"]["today_roi"]}\n'

        # 如果没有使用更新的提示词，添加默认的输出格式
        if not self._load_updated_prompt("reflection_analyst_agent", run_name):
            reflection_s += delim + '<output_format>Strictly use the following format for the final conclusion: `ACTION: [buy/sell/hold] AMOUNT: [0.0-1.0]` Examples: `ACTION: hold AMOUNT: 0.00` or `ACTION: sell AMOUNT: 0.75` Requirements: - Operation type must be lowercase - Amount must have two decimal places - No explanatory text allowed</output_format></optimized_prompt>'

        # Bullish Trader Section - receives all types of analysis
        # 尝试加载更新后的提示词
        bullish_prompt = self._load_updated_prompt("bullish_trader_agent", run_name)
        if bullish_prompt:
            template_bullish = bullish_prompt
        else:
            template_bullish = f"""<optimized_prompt>
<task>Analyze cryptocurrency market information and generate trading decisions</task>

<context>
You are a bullish ETH cryptocurrency trader. You focus on favorable conditions in the analysts' reports. You receive on-chain data analysis, FACTUAL news analysis, and SUBJECTIVE news analysis.

Your task:
1. Summarize the favorable market conditions based on all available information
2. Propose a specific trading action: 'buy', 'sell', or 'hold'
3. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
4. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: buy AMOUNT: 0.3")
</context>

<instructions>
1. Comprehensively analyze different data sources:
   - Evaluate positive indicators in on-chain data (such as large transactions, position changes)
   - Extract favorable events from factual news (such as regulatory changes, technical upgrades)
   - Analyze market optimism signals in subjective sentiment

2. Assess market conditions:
   - Identify bullish signals in analyst reports
   - Judge consistency and credibility of different information sources
   - Quantify the strength of market positive factors

3. Develop trading strategy:
   - Choose operation type based on analysis results:
   - Recommend 'buy' when favorable factors are significant
   - Recommend 'sell' when risk signals exist
   - Recommend 'hold' in uncertain situations

4. Calculate trading amount:
   - Determine ratio based on market condition strength:
   - Use 0.7-1.0 for strong bullish signals
   - Use 0.3-0.6 for moderate bullish signals
   - Use 0.1-0.2 for slight bullish signals

5. Format output:
   - Strictly follow the `"ACTION: [action] AMOUNT: [amount]"` format
   - Ensure action uses lowercase English
   - Keep amount to three decimal places (e.g., 0.250)
</instructions>



{delim}
On-chain Analysis:
{{0}}

Factual News Analysis:
{{1}}

Subjective News Analysis:
{{2}}

Reflection:
{{3}}
<output_format>
Strictly use the specified format: "ACTION: [action] AMOUNT: [amount]", where:
- [action] must be a lowercase English word: buy/sell/hold
- [amount] must be a three-decimal number between 0 and 1
- Output must not contain other text or explanations
</output_format>
</optimized_prompt>
"""

        # Bearish Trader Section - receives all types of analysis
        # 尝试加载更新后的提示词
        bearish_prompt = self._load_updated_prompt("bearish_trader_agent", run_name)
        if bearish_prompt:
            template_bearish = bearish_prompt
        else:
            template_bearish = f"""<optimized_prompt>
<task>Generate ETH trading recommendations based on unfavorable market conditions</task>

<context>
You are a bearish ETH cryptocurrency trader. You focus on unfavorable conditions in the analysts' reports. You receive on-chain data analysis, FACTUAL news analysis, and SUBJECTIVE news analysis.

Your task:
1. Summarize the unfavorable market conditions based on all available information
2. Propose a specific trading action: 'buy', 'sell', or 'hold'
3. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
4. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: sell AMOUNT: 0.4")
</context>

<instructions>
1. Analyze all available information sources:
   - Process negative indicators in on-chain data
   - Identify bearish information in factual news
   - Evaluate bearish views in subjective analysis

2. Synthesize unfavorable market factors:
   - Integrate negative information from three dimensions
   - Quantify risk level (1-5 levels)
   - Determine main risk drivers

3. Generate trading recommendations:
   - Choose operation type based on risk assessment:
     - High risk: sell
     - Medium risk: hold
     - Low risk: buy
   - Calculate trading ratio by risk level:
     - Level 5 risk: 0.8-1.0
     - Level 4 risk: 0.6-0.79
     - Level 3 risk: 0.3-0.59
     - Level 2 risk: 0.1-0.29
     - Level 1 risk: 0-0.09

4. Verify recommendation reasonability:
   - Check correspondence between trading ratio and risk level
   - Ensure operation type is consistent with market conditions
   - Exclude contradictory conclusions
</instructions>



{delim}
On-chain Analysis:
{{0}}

Factual News Analysis:
{{1}}

Subjective News Analysis:
{{2}}

Reflection:
{{3}}
<output_format>
Strictly use the following format for output:
"ACTION: [sell/hold/buy] AMOUNT: [0-1 decimal]"
Requirements:
1. Must use English action words
2. Amount should have two decimal places
3. Output should not contain other text
4. Example: "ACTION: sell AMOUNT: 0.45"
</output_format>
</optimized_prompt>
"""

        # Risk Management Section
        # 尝试加载更新后的提示词
        risk_prompt = self._load_updated_prompt("risk_manager_agent", run_name)
        if risk_prompt:
            risk_s = risk_prompt + f"{delim}"
        else:
            risk_s = f"""<optimized_prompt>
<task>Assess Ethereum trading risk and provide portfolio protection recommendations</task>

<context>
You are a risk management agent for ETH cryptocurrency trading. Your role is to assess market risk and provide guidance to protect the portfolio.

Your task:
1. Analyze the risk metrics provided below
2. Provide a concise risk assessment
3. Propose a specific trading action: 'buy', 'sell', or 'hold' based on risk considerations
4. Specify a trading amount as a decimal between 0 and 1 (e.g., 0.5 means trade 50% of available funds)
5. Format your conclusion as: "ACTION: [action] AMOUNT: [amount]" (e.g., "ACTION: hold AMOUNT: 0.0")
Based on the risk metrics above, provide a concise risk assessment and recommend appropriate risk management actions. If there are significant risk warnings, emphasize the need for caution. Conclude with your recommended trading action and amount in the format specified above.
</context>

<instructions>
1. Risk indicator analysis:
   - Evaluate volatility indicators (24-hour price fluctuation range)
   - Check leverage ratio (current position leverage multiple)
   - Analyze liquidity indicators (bid-ask spread and order book depth)
   - Monitor market sentiment indicators (fear and greed index)

2. Risk assessment report:
   - Calculate comprehensive risk score based on indicators (1-10 points)
   - Identify potential black swan risk factors
   - Assess liquidation risk level of current positions

3. Trading action recommendations:
   - Recommend selling in high risk (score ≥7)
   - Recommend holding in medium risk (4≤ score <7)
   - Recommend buying in low risk (score <4)
   - Add stop-loss recommendations during extreme volatility

4. Fund management strategy:
   - High risk (7-10 points): Recommended transaction amount 0.7-1.0
   - Medium risk (4-6 points): Recommended transaction amount 0.3-0.6
   - Low risk (1-3 points): Recommended transaction amount 0.0-0.2

5. Result formatting:
   - Strictly use "ACTION: [action] AMOUNT: [amount]" format
   - Amount should have two decimal places (e.g., 0.50)
   - Brief explanation not exceeding 20 words
</instructions>



{delim}"""

        # Add risk information from recent states
        for i, item in enumerate(self._history[-price_window:]):
            if item['label'] == 'state' and 'risk' in item['value']:
                state = item['value']
                risk_info = state['risk']
                date = state['date']
                daily_return = state['today_roi'] * 100

                # Format risk information
                cvar = risk_info.get('cvar')
                cvar_change = risk_info.get('cvar_change')
                warning = risk_info.get('warning')

                risk_log = f"Date: {date}, Daily Return: {daily_return:.2f}%"
                if cvar is not None:
                    risk_log += f", CVaR: {cvar*100:.2f}%"
                if cvar_change is not None:
                    risk_log += f", CVaR Change: {cvar_change*100:.2f}%"
                if warning:
                    risk_log += f"\nWARNING: {warning}"

                risk_s += risk_log + "\n"

        # 如果没有使用更新的提示词，添加默认的输出格式
        if not self._load_updated_prompt("risk_manager_agent", run_name):
            risk_s += delim + "<output_format>Strictly follow these format requirements: 1. Must include ACTION and AMOUNT fields 2. ACTION field only accepts lowercase English: buy/sell/hold 3. AMOUNT must be a two-decimal number between 0 and 1 4. Final conclusion must use the example format: ACTION: hold AMOUNT: 0.00 5. Add brief risk explanation (optional): supplement with parentheses (e.g., high volatility risk)</output_format></optimized_prompt>"

        # Final Trader Section - modified to include risk management
        # 尝试加载更新后的提示词
        final_prompt = self._load_updated_prompt("final_trader_agent", run_name)
        if final_prompt:
            template_s = final_prompt
        else:
            template_s = f"""<optimized_prompt>
<task>Comprehensively analyze ETH cryptocurrency trading signals and make final decisions</task>

<context>
You are the final decision-maker for ETH cryptocurrency trading.

Your task:
1. Consider all inputs: bullish analysis, bearish analysis, and risk management guidance
2. Weigh the evidence and determine the most reasonable market trend
3. Make a final trading decision
4. Provide your decision as a 1-decimal float in the range of [-1,1]:
   - Negative values (-0.1 to -1.0) indicate selling (e.g., -0.5 means sell 50% of holdings)
   - Positive values (0.1 to 1.0) indicate buying (e.g., 0.7 means use 70% of cash to buy)
   - Zero (0.0) indicates holding current position
5. Format your conclusion as: "ACTION: [buy/sell/hold] AMOUNT: [decimal]" (e.g., "ACTION: buy AMOUNT: 0.6")
</context>

<instructions>
1. Comprehensively evaluate input information:
   - Analyze bullish signals (technical/fundamental positives)
   - Analyze bearish signals (technical/fundamental negatives)
   - Assess risk management indicators

2. Conduct integrated judgment:
   - Compare strength of bullish and bearish signals
   - Identify key support/resistance levels
   - Evaluate market sentiment indicators

3. Formulate trading decisions:
   - Determine market trend direction (rising/consolidating/falling)
   - Calculate reasonable position ratio
   - Apply risk management parameters

4. Generate quantified trading instructions:
   - Determine values based on decision strength:
     - Buying range (0.1-1.0)
     - Selling range (-0.1 to -1.0)
     - Holding range (0.0)
   - Ensure values are precise to 1 decimal place

5. Format final output:
   - Use specified label structure
   - Include clear action instructions
   - Display precise position ratio
</instructions>



{delim}
Bullish Analysis (based on factual news):
{{0}}

Bearish Analysis (based on subjective news):
{{1}}

Risk Management Guidance:
{{2}}
<output_format>
Strictly follow this output format:
"ACTION: [buy/sell/hold] AMOUNT: [x.x]"
- Action type must be lowercase
- Value range must be [-1.0,1.0]
- Value must be a floating point number precise to one decimal place
- Examples: `ACTION: buy AMOUNT: 0.6` or `ACTION: sell AMOUNT: -0.3`
</output_format>
</optimized_prompt>
"""

        return price_s, factual_news_s, subjective_news_s, reflection_s, risk_s, template_bullish, template_bearish, template_s
def _get_base_query(base_query: str, memory: List[str]) -> str:
    query = base_query

    # add memory if it exists
    if len(memory) > 0:
        query += '\n\nYour memory for the task below:'
        for i, m in enumerate(memory):
            query += f'\nTrial {i}:\n{m.strip()}'
    query += f"\nHere is the task:\n"
    return query
