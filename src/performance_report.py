"""
性能报告生成器

在实验周期结束后生成详细的性能报告，包括：
1. 性能指标摘要
2. 性能图表
3. 交易数据表格
4. 交易员推理过程
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime
import json
import re
from typing import List, Dict, Any, Tuple, Optional


def recover_data_from_logs(run_name: str) -> Dict[str, Any]:
    """
    从日志文件中恢复交易数据

    Args:
        run_name: 运行名称

    Returns:
        包含恢复数据的字典
    """
    print(f"尝试从日志文件中恢复数据: {run_name}")

    # 初始化数据结构
    recovered_data = {
        'actions': [],
        'daily_returns': [],
        'agent_reasoning': [],
        'decision_reasons': []
    }

    # 日志文件路径
    log_path = os.path.join(run_name, 'experiments.log')

    if not os.path.exists(log_path):
        print(f"日志文件不存在: {log_path}")
        return recovered_data

    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()

        # 提取交易动作
        action_pattern = r"\*\*\* START STEP \d+ - ACTUAL ACTION \*\*\*\n([-\d\.]+)"
        actions = re.findall(action_pattern, log_content)
        recovered_data['actions'] = [float(a) for a in actions]

        # 提取日收益率
        state_pattern = r"\*\*\* START STEP \d+ - STATE \*\*\*\n.*?'today_roi': np\.float64\(([-\d\.]+)\)"
        daily_returns = re.findall(state_pattern, log_content)
        recovered_data['daily_returns'] = [float(r) for r in daily_returns]

        # 提取决策理由
        trader_pattern = r"\*\*\* STEP \d+ - FINAL TRADER RESPONSE \*\*\*\n(.*?)\*\*\* END STEP \d+ - FINAL TRADER \*\*\*"
        reasoning = re.findall(trader_pattern, log_content, re.DOTALL)
        recovered_data['agent_reasoning'] = reasoning

        # 提取日期
        date_pattern = r"'date': Timestamp\('(.*?)'\)"
        dates = re.findall(date_pattern, log_content)

        # 构建决策理由数据
        for i, (action, reason) in enumerate(zip(recovered_data['actions'], recovered_data['agent_reasoning'])):
            date = dates[i] if i < len(dates) else f"Day {i+1}"

            # 提取交易动作和数量
            action_amount_pattern = r"ACTION:\s*(buy|sell|hold)\s*AMOUNT:\s*([0-9]*\.?[0-9]+)"
            match = re.search(action_amount_pattern, reason, re.IGNORECASE)

            if match:
                action_type = match.group(1).lower()
                amount = float(match.group(2))
            else:
                action_type = "hold" if abs(action) < 0.1 else ("buy" if action > 0 else "sell")
                amount = abs(action)

            recovered_data['decision_reasons'].append({
                'date': date,
                'action': action_type,
                'amount': amount,
                'reason': reason
            })

        print(f"从日志中恢复了 {len(recovered_data['actions'])} 个交易日的数据")
        return recovered_data

    except Exception as e:
        print(f"从日志恢复数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return recovered_data

# 尝试导入matplotlib，如果不可用则降级
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    import matplotlib.font_manager as fm
    from matplotlib import rcParams
    MATPLOTLIB_AVAILABLE = True

    # 设置中文字体支持 - 尝试多种方法
    def setup_chinese_font():
        """尝试多种方法设置中文字体"""
        # 方法1：检查项目中是否有内置字体
        font_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts')
        if not os.path.exists(font_dir):
            try:
                os.makedirs(font_dir, exist_ok=True)
            except:
                pass

        # 检查是否有内置的中文字体
        embedded_font_path = os.path.join(font_dir, 'wqy-microhei.ttc')
        if os.path.exists(embedded_font_path):
            try:
                # 注册字体
                font_path = fm.fontManager.addfont(embedded_font_path)
                plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei'] + plt.rcParams['font.sans-serif']
                print(f"使用内置中文字体: WenQuanYi Micro Hei")
                return True
            except:
                print(f"无法加载内置字体: {embedded_font_path}")

        # 方法2：尝试常见的中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'STHeiti', 'AR PL UMing CN',
                         'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                         'Noto Sans CJK TC', 'Noto Sans SC', 'Noto Sans TC',
                         'Source Han Sans CN', 'Source Han Sans TW', 'PingFang SC',
                         'PingFang TC', 'Heiti SC', 'Heiti TC']

        # 获取系统上所有可用的字体
        font_names = [f.name for f in fm.fontManager.ttflist]

        # 尝试找到一个可用的中文字体
        for font in chinese_fonts:
            if font in font_names:
                plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
                print(f"使用系统中文字体: {font}")
                return True

        # 方法3：使用matplotlib的fontconfig模式
        try:
            rcParams['font.family'] = 'sans-serif'
            rcParams['font.sans-serif'] = ['Arial Unicode MS', 'DejaVu Sans'] + rcParams['font.sans-serif']
            return True
        except:
            pass

        # 方法4：使用系统默认字体
        try:
            # 尝试使用系统默认字体
            default_font = fm.findfont(fm.FontProperties(family='sans-serif'))
            plt.rcParams['font.sans-serif'] = ['sans-serif']
            plt.rcParams['font.family'] = 'sans-serif'
            return True
        except:
            pass

        # 方法5：提示用户安装字体
        print("未找到合适的中文字体，请运行 ./install_chinese_font.sh 安装中文字体")

        return False

    # 设置中文字体
    plt._found_chinese_font = setup_chinese_font()
    if not plt._found_chinese_font:
        print("警告: 无法设置中文字体，将使用英文标签替代中文")

    # 禁用负号使用Unicode
    plt.rcParams['axes.unicode_minus'] = False

except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib不可用，将不会生成图表。请安装matplotlib: pip install matplotlib")

class PerformanceReport:
    """性能报告生成器类"""

    def __init__(self, run_name: str, output_dir: Optional[str] = None):
        """
        初始化性能报告生成器

        Args:
            run_name: 运行名称
            output_dir: 输出目录，默认为run_name目录下的reports子目录
        """
        self.run_name = run_name
        self.output_dir = output_dir or os.path.join(run_name, 'reports')

        # 确保报告目录存在
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            # 同时创建charts子目录
            charts_dir = os.path.join(self.output_dir, 'charts')
            os.makedirs(charts_dir, exist_ok=True)
            print(f"已创建报告目录: {self.output_dir}")
        except Exception as e:
            print(f"创建报告目录时出错: {e}")
            # 尝试使用绝对路径
            abs_output_dir = os.path.abspath(self.output_dir)
            print(f"尝试使用绝对路径创建目录: {abs_output_dir}")
            os.makedirs(abs_output_dir, exist_ok=True)
            self.output_dir = abs_output_dir

        # 初始化数据存储
        self.trade_data = []
        self.reasoning_data = []

    def collect_data(self, env_history=None, cycle_data: Optional[Dict[str, Any]] = None) -> None:
        """
        从环境历史和周期数据中收集性能数据

        Args:
            env_history: 环境历史对象，可以为None
            cycle_data: 周期数据字典，可以为None
        """
        # 初始化数据列表
        dates = []
        net_worths = []
        rois = []
        daily_returns = []
        actions = []
        reasoning = []

        # 如果有环境历史，从中提取数据
        if env_history is not None and hasattr(env_history, '_history'):
            # 从环境历史中提取交易数据
            history = env_history._history

            # 遍历历史记录
            for i, item in enumerate(history):
                if item['label'] == 'state':
                    state = item['value']
                    dates.append(state.get('date', f'Day {i}'))
                    net_worths.append(state.get('net_worth', 0))
                    rois.append(state.get('roi', 0))
                    daily_returns.append(state.get('today_roi', 0))

                elif item['label'] == 'action':
                    actions.append(float(item['value']))

                elif item['label'] == 'final_trader_response':
                    reasoning.append(item['value'])

        # 如果有周期数据，从中提取数据
        if cycle_data is not None:
            # 如果环境历史中没有数据，尝试从周期数据中提取
            if not dates and 'daily_returns' in cycle_data:
                # 使用索引作为日期
                dates = [f'Day {i+1}' for i in range(len(cycle_data['daily_returns']))]

                # 计算净资产价值和累计收益率
                daily_returns = cycle_data.get('daily_returns', [])
                actions = cycle_data.get('actions', [])

                # 计算净资产价值和累计收益率
                net_worth = 1.0  # 初始净资产价值
                net_worths = []
                rois = []

                for daily_return in daily_returns:
                    net_worth *= (1 + daily_return)
                    net_worths.append(net_worth)
                    rois.append(net_worth - 1)  # 累计收益率 = 净资产价值 - 1

        # 确保数据长度一致
        if dates and net_worths and rois and daily_returns and actions:
            min_len = min(len(dates), len(net_worths), len(rois), len(daily_returns), len(actions))

            # 创建交易数据表
            self.trade_data = pd.DataFrame({
                '日期': dates[:min_len],
                '净资产价值': net_worths[:min_len],
                '累计收益率': rois[:min_len],
                '日收益率': daily_returns[:min_len],
                '交易动作': actions[:min_len]
            })
        else:
            # 如果没有足够的数据，创建一个空的DataFrame
            print("警告: 没有足够的数据来创建交易数据表")
            self.trade_data = pd.DataFrame(columns=['日期', '净资产价值', '累计收益率', '日收益率', '交易动作'])

        # 创建推理数据表
        # 优先使用cycle_data中的决策理由（如果有）
        if cycle_data is not None and 'decision_reasons' in cycle_data and cycle_data['decision_reasons']:
            decision_reasons = cycle_data['decision_reasons']
            reason_dates = [item.get('date', f'Day {i}') for i, item in enumerate(decision_reasons)]
            reason_actions = [item.get('action', '') for item in decision_reasons]
            reason_amounts = [item.get('amount', 0) for item in decision_reasons]
            reason_texts = [item.get('reason', '') for item in decision_reasons]

            self.reasoning_data = pd.DataFrame({
                '日期': reason_dates,
                '交易动作': reason_actions,
                '交易数量': reason_amounts,
                '决策理由': reason_texts
            })
        elif reasoning and dates and actions:
            # 使用历史记录中的推理数据
            min_len = min(len(dates), len(actions), len(reasoning))
            self.reasoning_data = pd.DataFrame({
                '日期': dates[:min_len],
                '交易动作': actions[:min_len],
                '决策理由': reasoning[:min_len]
            })
        else:
            # 如果没有足够的数据，创建一个空的DataFrame
            print("警告: 没有足够的数据来创建推理数据表")
            self.reasoning_data = pd.DataFrame(columns=['日期', '交易动作', '交易数量', '决策理由'])

    def calculate_metrics(self) -> Dict[str, float]:
        """
        计算性能指标

        Returns:
            包含各种性能指标的字典
        """
        if len(self.trade_data) == 0:
            return {}

        # 提取数据
        net_worths = self.trade_data['净资产价值'].values
        daily_returns = self.trade_data['日收益率'].values
        actions = self.trade_data['交易动作'].values

        # 计算总收益率
        total_return = self.trade_data['累计收益率'].iloc[-1]

        # 计算Sharpe比率
        risk_free_rate = 0
        return_mean = np.mean(daily_returns) * 252  # 年化
        return_std = np.std(daily_returns) * np.sqrt(252)  # 年化
        sharpe_ratio = (return_mean - risk_free_rate) / return_std if return_std > 0 else 0

        # 计算最大回撤
        peak = np.maximum.accumulate(net_worths)
        drawdown = (peak - net_worths) / peak
        max_drawdown = np.max(drawdown)

        # 计算胜率
        winning_days = np.sum(daily_returns > 0)
        total_days = len(daily_returns)
        win_rate = winning_days / total_days if total_days > 0 else 0

        # 计算交易次数
        trade_count = np.sum(np.abs(actions) > 0)

        # 返回指标字典
        return {
            '总收益率': total_return * 100,  # 转为百分比
            'Sharpe比率': sharpe_ratio,
            '日收益率均值': np.mean(daily_returns) * 100,  # 转为百分比
            '日收益率标准差': np.std(daily_returns) * 100,  # 转为百分比
            '最大回撤': max_drawdown * 100,  # 转为百分比
            '胜率': win_rate * 100,  # 转为百分比
            '交易次数': trade_count
        }

    def generate_charts(self) -> None:
        """生成性能图表"""
        if len(self.trade_data) == 0:
            print("警告: 没有交易数据，无法生成图表")
            return

        # 如果matplotlib不可用，则跳过图表生成
        if not MATPLOTLIB_AVAILABLE:
            print("警告: matplotlib不可用，跳过图表生成")
            return

        # 创建图表目录
        charts_dir = os.path.join(self.output_dir, 'charts')
        try:
            os.makedirs(charts_dir, exist_ok=True)
            print(f"已创建图表目录: {charts_dir}")
        except Exception as e:
            print(f"创建图表目录时出错: {e}")
            # 尝试使用绝对路径
            abs_charts_dir = os.path.abspath(charts_dir)
            print(f"尝试使用绝对路径创建图表目录: {abs_charts_dir}")
            os.makedirs(abs_charts_dir, exist_ok=True)
            charts_dir = abs_charts_dir

        # 转换日期格式
        try:
            dates = pd.to_datetime(self.trade_data['日期'])
        except:
            # 如果日期转换失败，使用索引作为x轴
            dates = np.arange(len(self.trade_data))

        # 检查是否找到了中文字体
        use_chinese = hasattr(plt, '_found_chinese_font') and plt._found_chinese_font

        # 准备标题和标签（中英文）
        titles = {
            'net_worth': '净资产价值曲线' if use_chinese else 'Net Worth Curve',
            'cumulative_return': '累计收益率曲线' if use_chinese else 'Cumulative Return Curve',
            'daily_return': '日收益率柱状图' if use_chinese else 'Daily Return Bar Chart',
            'trade_action': '交易动作标记图' if use_chinese else 'Trade Action Markers'
        }

        labels = {
            'date': '日期' if use_chinese else 'Date',
            'net_worth': '净资产价值' if use_chinese else 'Net Worth',
            'cumulative_return': '累计收益率 (%)' if use_chinese else 'Cumulative Return (%)',
            'daily_return': '日收益率 (%)' if use_chinese else 'Daily Return (%)',
            'buy': '买入' if use_chinese else 'Buy',
            'sell': '卖出' if use_chinese else 'Sell'
        }

        # 1. 净资产价值曲线
        plt.figure(figsize=(12, 6))
        plt.plot(dates, self.trade_data['净资产价值'], 'b-', linewidth=2)
        plt.title(titles['net_worth'])
        plt.xlabel(labels['date'])
        plt.ylabel(labels['net_worth'])
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'net_worth_curve.png'))
        plt.close()

        # 2. 累计收益率曲线
        plt.figure(figsize=(12, 6))
        plt.plot(dates, self.trade_data['累计收益率'] * 100, 'g-', linewidth=2)  # 转为百分比
        plt.title(titles['cumulative_return'])
        plt.xlabel(labels['date'])
        plt.ylabel(labels['cumulative_return'])
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'cumulative_return_curve.png'))
        plt.close()

        # 3. 日收益率柱状图
        plt.figure(figsize=(12, 6))
        plt.bar(dates, self.trade_data['日收益率'] * 100, color=['g' if r > 0 else 'r' for r in self.trade_data['日收益率']])  # 转为百分比
        plt.title(titles['daily_return'])
        plt.xlabel(labels['date'])
        plt.ylabel(labels['daily_return'])
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'daily_return_bar.png'))
        plt.close()

        # 4. 交易动作标记图
        plt.figure(figsize=(12, 6))
        plt.plot(dates, self.trade_data['净资产价值'], 'b-', linewidth=2)

        # 标记买入点（正值）
        buy_indices = self.trade_data['交易动作'] > 0
        if np.any(buy_indices):
            plt.scatter(
                dates[buy_indices],
                self.trade_data['净资产价值'][buy_indices],
                color='g',
                marker='^',
                s=100,
                label=labels['buy']
            )

        # 标记卖出点（负值）
        sell_indices = self.trade_data['交易动作'] < 0
        if np.any(sell_indices):
            plt.scatter(
                dates[sell_indices],
                self.trade_data['净资产价值'][sell_indices],
                color='r',
                marker='v',
                s=100,
                label=labels['sell']
            )

        plt.title(titles['trade_action'])
        plt.xlabel(labels['date'])
        plt.ylabel(labels['net_worth'])
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'trade_action_markers.png'))
        plt.close()

    def generate_report(self) -> str:
        """
        生成完整的性能报告

        Returns:
            报告文件路径
        """
        # 计算性能指标
        metrics = self.calculate_metrics()

        # 生成图表
        self.generate_charts()

        # 创建报告文件
        report_path = os.path.join(self.output_dir, 'performance_report.html')

        # 准备图表部分
        charts_dir = os.path.join(self.output_dir, 'charts')
        if MATPLOTLIB_AVAILABLE and os.path.exists(charts_dir):
            charts_section = f'''
                <div class="chart-container">
                    <h3>净资产价值曲线</h3>
                    <img class="chart" src="charts/net_worth_curve.png" alt="净资产价值曲线">
                </div>
                <div class="chart-container">
                    <h3>累计收益率曲线</h3>
                    <img class="chart" src="charts/cumulative_return_curve.png" alt="累计收益率曲线">
                </div>
                <div class="chart-container">
                    <h3>日收益率柱状图</h3>
                    <img class="chart" src="charts/daily_return_bar.png" alt="日收益率柱状图">
                </div>
                <div class="chart-container">
                    <h3>交易动作标记图</h3>
                    <img class="chart" src="charts/trade_action_markers.png" alt="交易动作标记图">
                </div>
            '''
        else:
            charts_section = '<p>图表不可用。请安装matplotlib库以启用图表生成功能。</p>'

        # 生成HTML报告
        try:
            # 准备HTML内容
            html_header = f'''
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>交易性能报告 - {self.run_name}</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1, h2, h3 {{ color: #333; }}
                        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                        th {{ background-color: #f2f2f2; }}
                        tr:nth-child(even) {{ background-color: #f9f9f9; }}
                        .metrics {{ display: flex; flex-wrap: wrap; }}
                        .metric-card {{
                            background-color: #f8f9fa;
                            border-radius: 5px;
                            padding: 15px;
                            margin: 10px;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            flex: 1;
                            min-width: 200px;
                        }}
                        .metric-value {{
                            font-size: 24px;
                            font-weight: bold;
                            margin: 10px 0;
                            color: #0066cc;
                        }}
                        .positive {{ color: green; }}
                        .negative {{ color: red; }}
                        .chart-container {{ margin: 20px 0; }}
                        .chart {{ width: 100%; max-width: 800px; margin: 0 auto; }}
                    </style>
                </head>
                <body>
                    <h1>交易性能报告 - {self.run_name}</h1>
                    <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

                    <h2>1. 性能指标摘要</h2>
                    <div class="metrics">
                        <div class="metric-card">
                            <h3>总收益率</h3>
                            <div class="metric-value {('positive' if metrics.get('总收益率', 0) >= 0 else 'negative')}">{metrics.get('总收益率', 0):.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <h3>Sharpe比率</h3>
                            <div class="metric-value {('positive' if metrics.get('Sharpe比率', 0) >= 0 else 'negative')}">{metrics.get('Sharpe比率', 0):.2f}</div>
                        </div>
                        <div class="metric-card">
                            <h3>最大回撤</h3>
                            <div class="metric-value negative">{metrics.get('最大回撤', 0):.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <h3>胜率</h3>
                            <div class="metric-value">{metrics.get('胜率', 0):.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <h3>交易次数</h3>
                            <div class="metric-value">{metrics.get('交易次数', 0)}</div>
                        </div>
                        <div class="metric-card">
                            <h3>日收益率均值</h3>
                            <div class="metric-value {('positive' if metrics.get('日收益率均值', 0) >= 0 else 'negative')}">{metrics.get('日收益率均值', 0):.2f}%</div>
                        </div>
                        <div class="metric-card">
                            <h3>日收益率标准差</h3>
                            <div class="metric-value">{metrics.get('日收益率标准差', 0):.2f}%</div>
                        </div>
                    </div>

                    <h2>2. 性能图表</h2>
                    {charts_section}

                    <h2>3. 交易数据表格</h2>
                    <table>
                        <tr>
                            <th>日期</th>
                            <th>净资产价值</th>
                            <th>累计收益率</th>
                            <th>日收益率</th>
                            <th>交易动作</th>
                        </tr>
            '''

            # 准备交易数据表格内容
            trade_data_rows = ""
            for _, row in self.trade_data.iterrows():
                trade_data_rows += f'''
                    <tr>
                        <td>{row['日期']}</td>
                        <td>{row['净资产价值']:.2f}</td>
                        <td class="{('positive' if row['累计收益率'] >= 0 else 'negative')}">{row['累计收益率']*100:.2f}%</td>
                        <td class="{('positive' if row['日收益率'] >= 0 else 'negative')}">{row['日收益率']*100:.2f}%</td>
                        <td>{row['交易动作']:.1f}</td>
                    </tr>
                '''

            # 准备表格中间部分
            table_middle = '''
                </table>

                <h2>4. 交易员推理过程</h2>
                <table>
                    <tr>
                        <th>日期</th>
                        <th>交易动作</th>
                        <th>交易数量</th>
                        <th>决策理由</th>
                    </tr>
            '''

            # 准备推理过程表格内容
            reasoning_rows = ""
            for _, row in self.reasoning_data.iterrows():
                # 检查是否有交易数量列
                amount_value = row.get('交易数量', 0) if '交易数量' in row else row['交易动作']

                # 格式化交易动作
                action_value = row['交易动作']
                if isinstance(action_value, (int, float)):
                    action_display = f"{action_value:.1f}"
                else:
                    action_display = action_value

                # 格式化交易数量
                if isinstance(amount_value, (int, float)):
                    amount_display = f"{amount_value:.2f}"
                else:
                    amount_display = amount_value

                reasoning_rows += f'''
                    <tr>
                        <td>{row['日期']}</td>
                        <td>{action_display}</td>
                        <td>{amount_display}</td>
                        <td>{row['决策理由']}</td>
                    </tr>
                '''

            # 准备HTML尾部
            html_footer = '''
                </table>
            </body>
            </html>
            '''

            # 将所有内容合并并写入文件
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(html_header)
                f.write(trade_data_rows)
                f.write(table_middle)
                f.write(reasoning_rows)
                f.write(html_footer)
        except Exception as e:
            print(f"生成HTML报告时出错: {e}")
            import traceback
            traceback.print_exc()
            # 尝试创建一个简单的报告
            try:
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(f'''
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>交易性能报告 - {self.run_name}</title>
                    </head>
                    <body>
                        <h1>交易性能报告 - {self.run_name}</h1>
                        <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                        <p>生成详细报告时出错，请检查日志。</p>
                    </body>
                    </html>
                    ''')
            except Exception as e2:
                print(f"生成简单报告时也出错: {e2}")

        # 同时生成CSV文件
        try:
            self.trade_data.to_csv(os.path.join(self.output_dir, 'trade_data.csv'), index=False, encoding='utf-8-sig')
            self.reasoning_data.to_csv(os.path.join(self.output_dir, 'reasoning_data.csv'), index=False, encoding='utf-8-sig')
        except Exception as e:
            print(f"生成CSV文件时出错: {e}")

        try:
            print(f"性能报告已生成: {report_path}")
            return report_path
        except Exception as e:
            print(f"返回报告路径时出错: {e}")
            return os.path.join(self.run_name, 'reports', 'performance_report.html')
