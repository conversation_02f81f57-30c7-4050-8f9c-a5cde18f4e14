import json
import os

from dotenv import load_dotenv
from eth_account import Account
from web3 import Web3
import web3
from Ipfs import IPFS

class Agent_events:
    def __init__(self, agent_name):
        """
        初始化Agent_events类，用于与区块链交互。

        Args:
            agent_name: 代理名称，用于加载对应的.env文件
        """
        # 构建.env文件路径
        dotenv_path = os.path.join(os.getcwd(), f"{agent_name}/.env")
        print(f"Loading .env file from: {dotenv_path}")

        # 尝试加载.env文件
        try:
            load_dotenv(dotenv_path=dotenv_path, override=True)
        except Exception as e:
            print(f"Warning: Failed to load .env file: {e}")
            # 创建agent目录（如果不存在）
            os.makedirs(os.path.dirname(dotenv_path), exist_ok=True)
            # 创建默认.env文件
            self._create_default_env_file(agent_name, dotenv_path)
            # 重新加载.env文件
            load_dotenv(dotenv_path=dotenv_path, override=True)

        # 连接到以太坊节点
        eth_rpc = os.environ.get("ETH_RPC", 'HTTP://127.0.0.1:7545')
        self.w3 = web3.Web3(web3.Web3.HTTPProvider(eth_rpc))
        if not self.w3.is_connected():
            print(f"Warning: Failed to connect to Ethereum node at {eth_rpc}. Make sure Ganache is running.")
        else:
            print(f"成功连接到以太坊节点: {eth_rpc}")

        # 获取私钥
        self.primary_account = os.getenv('PRIVATE_KEY')
        if not self.primary_account:
            print("Warning: PRIVATE_KEY not found in .env file. Using default key.")
            self.primary_account = '0x3c7c6823d1875cd64b5785c941d7a8e69ec5a2fe773dda1b544c2484b2c6b149'

        # 获取合约地址
        self.EAAC_ADDR = os.getenv('EAAC_ADDR') or os.environ.get("CONTRACT_ADDRESS")
        if not self.EAAC_ADDR:
            print("Warning: Contract address not found in .env file or environment variables.")
            self.EAAC_ADDR = '******************************************'  # 默认地址

        # 加载合约ABI
        abi_path = os.getenv('EAAC_ABI_PATH')
        if abi_path and os.path.exists(abi_path):
            with open(abi_path, 'r') as f:
                self.EAAC_ABI = json.load(f)
        else:
            print(f"Warning: EAAC_ABI_PATH not found or invalid: {abi_path}")
            # 使用默认ABI
            self.EAAC_ABI = self._get_default_abi()

    def _create_default_env_file(self, agent_name, dotenv_path):
        """创建默认的.env文件"""
        # 确保目录存在
        os.makedirs(os.path.dirname(dotenv_path), exist_ok=True)

        # 默认配置
        default_config = f"""# {agent_name} 配置
PRIVATE_KEY=0x3c7c6823d1875cd64b5785c941d7a8e69ec5a2fe773dda1b544c2484b2c6b149
EAAC_ADDR=******************************************
EAAC_ABI_PATH=contract/EAAC.json
RPC_PROVIDER=HTTP://127.0.0.1:7545
"""

        # 写入.env文件
        with open(dotenv_path, 'w') as f:
            f.write(default_config)

        print(f"Created default .env file for {agent_name}")

        # 确保ABI文件目录存在
        os.makedirs("contract", exist_ok=True)

        # 创建默认ABI文件
        abi_path = "contract/EAAC.json"
        if not os.path.exists(abi_path):
            with open(abi_path, 'w') as f:
                json.dump(self._get_default_abi(), f, indent=2)
            print(f"Created default ABI file at {abi_path}")

    def _get_default_abi(self):
        """获取默认的ABI"""
        return [
            {
                "anonymous": False,
                "inputs": [
                    {
                        "indexed": True,
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    }
                ],
                "name": "Register",
                "type": "event"
            },
            {
                "anonymous": False,
                "inputs": [
                    {
                        "indexed": True,
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    },
                    {
                        "indexed": False,
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "Report",
                "type": "event"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    }
                ],
                "name": "register_agent",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    }
                ],
                "name": "register_agent_generic",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "identifier",
                        "type": "string"
                    },
                    {
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "report_activity",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [
                    {
                        "internalType": "address",
                        "name": "operator",
                        "type": "address"
                    },
                    {
                        "internalType": "string",
                        "name": "report_hash",
                        "type": "string"
                    }
                ],
                "name": "report_activity_generic",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]

    def build_tx(self, fn_with_input, account):
        """
        构建一个以太坊交易。

        Args:
            fn_with_input: 一个函数对象，代表智能合约中要调用的函数，并且已经设置了输入参数。
            account: 一个账户对象，包含了发送交易的以太坊地址和私钥。

        Returns:
            一个未签名的交易对象。
        """
        # 使用提供的参数构建交易
        tx = fn_with_input.build_transaction({
            'gas': 500000,  # 降低 gas 限制
            'gasPrice': self.w3.to_wei('20', 'gwei'),  # 降低 gas 价格
            'nonce': self.w3.eth.get_transaction_count(account),  # 设置 nonce 值
        })

        return tx  # 返回构建好的交易对象

    def sign_send_tx(self, tx, account):
        """
        使用提供的账户私钥对交易进行签名并发送到区块链。

        Args:
            tx: 要签名的交易字典。
            account: 用于签名的账户对象。

        Returns:
            交易哈希值。
        """
        signed_tx = self.w3.eth.account.sign_transaction(tx, self.primary_account)
        tx_hash = self.w3.eth.send_raw_transaction(signed_tx.raw_transaction)
        print(f'Transaction hash: {tx_hash.hex()}')
        return tx_hash

    def get_tx_receipt(self, tx_hash):
        """
        获取交易回执。

        Args:
            tx_hash: 交易哈希值。

        Returns:
            交易回执。
        """
        tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
        print(f'Transaction receipt: {tx_receipt}')
        return tx_receipt

    def register_to_EAAC(self, identifier: str = None):
        """
        将代理注册到链上。

        Args:
            identifier: 代理标识符。
        """
        contract = self.w3.eth.contract(address=self.EAAC_ADDR, abi=self.EAAC_ABI)
        account = Account.from_key(self.primary_account)._address
        print(f"Registering agent {identifier} with account {account}")

        # 调用函数
        fn = contract.functions.register_agent(account, identifier)
        # 构建交易
        tx = self.build_tx(fn, account)
        # 签名并发送交易
        tx_hash = self.sign_send_tx(tx, account)
        # 获取交易回执
        tx_receipt = self.get_tx_receipt(tx_hash)
        print(f"Registered agent {identifier} to EAAC")
        return tx_receipt

    def report_to_EAAC(self, ipfs_hash: str, identifier: str = None):
        """
        将代理活动上报到链上。

        Args:
            ipfs_hash: IPFS哈希值。
            identifier: 代理标识符。

        Returns:
            交易回执。
        """
        # 创建与 EAAC 合约的连接
        contract = self.w3.eth.contract(address=self.EAAC_ADDR, abi=self.EAAC_ABI)
        # 从环境变量中获取私钥并创建账户对象
        account = Account.from_key(self.primary_account)._address
        print(f"Reporting activity for agent {identifier} with account {account}")

        # 调用函数
        fn = contract.functions.report_activity(account, identifier, ipfs_hash)
        # 构建交易
        tx = self.build_tx(fn, account)
        # 签名并发送交易
        tx_hash = self.sign_send_tx(tx, account)
        # 获取交易回执
        tx_receipt = self.get_tx_receipt(tx_hash)

        print(f"Reported activity of the agent ({identifier}) to EAAC")  # 打印报告成功信息
        return tx_receipt  # 返回交易回执

    def Upload_json_to_ipfs(self, json_data):
        """
        上传 JSON 数据到 IPFS，并返回其 CID。

        Args:
            json_data: 要上传的 JSON 数据。

        Returns:
            上传成功后返回的 CID。
        """
        ipfs = IPFS()
        cid = ipfs.upload_json_to_ipfs(json_data)
        return cid

# 示例用法
if __name__ == "__main__":
    # 测试代码
    agent = Agent_events("test_agent")

    # 测试注册
    # agent.register_to_EAAC("test_agent")

    # 测试上传和上报
    test_json = {"test": "test", "data": {"key1": "value1", "key2": "value2"}}
    cid = agent.Upload_json_to_ipfs(test_json)
    print(f"Uploaded JSON to IPFS with CID: {cid}")

    # 上报到链上
    # tx_receipt = agent.report_to_EAAC(cid, "test_agent")
    # print(f"Transaction receipt: {tx_receipt}")
