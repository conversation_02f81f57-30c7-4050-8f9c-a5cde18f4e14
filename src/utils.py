import os
# 禁用代理（仅对当前 Python 脚本）
os.environ.pop("http_proxy", None)
os.environ.pop("https_proxy", None)
os.environ.pop("HTTP_PROXY", None)
os.environ.pop("HTTPS_PROXY", None)
import sys
import requests
import json
from openai import OpenAI
from dotenv import load_dotenv

# 添加智谱AI支持
try:
    from zhipuai import ZhipuAI
    ZHIPUAI_AVAILABLE = True
except ImportError:
    ZHIPUAI_AVAILABLE = False
    print("警告: 未安装zhipuai库，无法使用智谱AI API。请安装: pip install zhipuai")

# LM Studio local API settings
LM_STUDIO_API_BASE = "http://127.0.0.1:1234/v1"

# 环境变量设置
USE_ZHIPUAI = os.getenv('USE_ZHIPUAI', 'false').lower() == 'true'
USE_DEEPSEEK_API = True  # 强制使用DeepSeek API

# 根据命令行参数检查模型类型
if len(sys.argv) > 1:
    for i, arg in enumerate(sys.argv):
        if arg == "--model" and i + 1 < len(sys.argv):
            model_arg = sys.argv[i + 1]
            if model_arg == "deepseek-chat" or model_arg == "deepseek-reasoner":
                USE_DEEPSEEK_API = True
                USE_ZHIPUAI = False
            elif model_arg == "zhipu-glm4":
                print("警告: 已强制使用DeepSeek API，忽略zhipu-glm4模型设置")
                USE_ZHIPUAI = False
                USE_DEEPSEEK_API = True

# 初始化客户端
if USE_ZHIPUAI and ZHIPUAI_AVAILABLE:
    # 使用智谱AI API
    ZHIPUAI_API_KEY = "bb41ff907cda4ac298fd2b7f6e71510f.aK1vZ1rV2rsR0pb0"  # 您的API密钥
    zhipuai_client = ZhipuAI(api_key=ZHIPUAI_API_KEY)
    print("使用智谱AI API")
elif USE_DEEPSEEK_API:
    # 使用DeepSeek API
    load_dotenv()  # 加载.env文件
    api_key = os.getenv('DEEPSEEK_API_KEY')

    # 如果环境变量中没有设置API密钥，使用默认密钥
    if not api_key:
        # 设置默认的DeepSeek API密钥
        api_key = "YOUR_DEEPSEEK_API_KEY"  # 请替换为您的实际DeepSeek API密钥
        print(f"警告: 未设置 DEEPSEEK_API_KEY 环境变量，使用默认密钥")

        # 将密钥设置到环境变量中，以便后续使用
        os.environ['DEEPSEEK_API_KEY'] = api_key

    api_base = "https://api.deepseek.com/v1"
    client = OpenAI(api_key=api_key, base_url=api_base)
    print("使用DeepSeek API")
else:
    # 使用本地LM Studio
    client = OpenAI(
        base_url=LM_STUDIO_API_BASE,
        api_key="not-needed",
        timeout=180.0,  # 增加超时时间到180秒
        max_retries=3   # 增加重试次数
    )
    print("使用本地LM Studio")

from tenacity import (
    retry,
    stop_after_attempt, # type: ignore
    wait_random_exponential, # type: ignore
)

from typing import Optional, List
if sys.version_info >= (3, 8):
    from typing import Literal
else:
    from typing_extensions import Literal

# 支持的模型
Model = Literal["gemma-local", "deepseek-chat", "deepseek-reasoner", "zhipu-glm4"]

# 模型映射，将简化名称映射到实际模型名称
MODEL_MAPPING = {
    "gemma-local": "gemma-3-1b-it",  # LM Studio中加载的Gemma模型
    "deepseek-chat": "deepseek-chat",
    "deepseek-reasoner": "deepseek-reasoner",
    "zhipu-glm4": "glm-4-plus"  # 智谱AI的GLM-4模型
}

@retry(wait=wait_random_exponential(min=1, max=60), stop=stop_after_attempt(6))
def get_chat(prompt, model, seed=None, temperature=0.0, max_tokens=256, stop_strs=None, is_batched=False, debug=False):
    messages = [
        {
            "role": "user",
            "content": prompt
        }
    ]

    # 强制使用DeepSeek模型
    if model != "deepseek-chat" and model != "deepseek-reasoner":
        print(f"警告: 已强制使用DeepSeek模型，将{model}替换为deepseek-chat")
        model = "deepseek-chat"

    # Map the model name to the actual model identifier
    actual_model = MODEL_MAPPING.get(model, model)

    # 检查环境变量中是否有token限制
    env_max_tokens = os.getenv('MAX_TOKENS_LIMIT')
    if env_max_tokens:
        try:
            max_tokens = min(int(env_max_tokens), max_tokens)
            if debug:
                print(f"使用环境变量中的token限制: {max_tokens}")
        except:
            pass

    # 使用智谱AI API
    if USE_ZHIPUAI and ZHIPUAI_AVAILABLE and model == "zhipu-glm4":
        try:
            # 添加调试信息
            if debug:
                print(f"使用智谱AI API发送请求到模型: {actual_model}")

            # 调用智谱AI API
            response = zhipuai_client.chat.completions.create(
                model=actual_model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            if debug:
                print(f"成功使用智谱AI模型: {actual_model}")

            return response.choices[0].message.content
        except Exception as e:
            error_msg = str(e)
            print(f"Error calling ZhipuAI API: {error_msg}")

            # 尝试重试
            print("尝试重新连接智谱AI API...")
            try:
                # 再次尝试调用，使用新的客户端实例
                retry_client = ZhipuAI(api_key=ZHIPUAI_API_KEY)
                response = retry_client.chat.completions.create(
                    model=actual_model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                print("重试成功!")
                return response.choices[0].message.content
            except Exception as retry_e:
                print(f"重试失败: {str(retry_e)}")
                return f"Error: Unable to generate response from ZhipuAI API. Error: {error_msg}"

    # 使用OpenAI兼容API（LM Studio或DeepSeek）
    # Prepare parameters based on whether we're using local or remote API
    params = {
        "model": actual_model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }

    # Add stop sequences if provided
    if stop_strs:
        params["stop"] = stop_strs

    # Add seed if provided (local LM Studio might not support this)
    if seed is not None and USE_DEEPSEEK_API:
        params["seed"] = seed

    try:
        # 添加调试信息
        if debug:
            print(f"发送请求到模型: {actual_model}")
            print(f"请求参数: {json.dumps(params, ensure_ascii=False, default=str)[:500]}...")

        # Make the API call
        response = client.chat.completions.create(**params)

        if debug:
            if hasattr(response, 'system_fingerprint'):
                print(f"系统指纹: {response.system_fingerprint}")
            print(f"成功使用模型: {actual_model}")

        return response.choices[0].message.content
    except Exception as e:
        error_msg = str(e)
        print(f"Error calling LLM API: {error_msg}")

        # 提供更详细的错误信息和解决方案
        if "502" in error_msg:
            print("\n可能的原因和解决方案:")
            print("1. LM Studio 服务器可能过载或未正确响应")
            print("2. 模型可能需要更多内存或资源")
            print("3. 请求可能太大或太复杂")
            print("\n建议:")
            print("- 重启 LM Studio")
            print("- 确认 Gemma 模型已正确加载")
            print("- 尝试减小 max_tokens 参数")
            print("- 检查系统资源使用情况")

            # 尝试检查 LM Studio 是否仍然可用
            try:
                available = check_lm_studio_availability()
                if not available:
                    print("LM Studio 服务器似乎不可用，请重启服务器")
            except:
                pass

        # 如果是DeepSeek API，尝试重试
        if USE_DEEPSEEK_API:
            print("尝试重新连接DeepSeek API...")
            try:
                # 创建新的客户端实例
                api_key = os.getenv('DEEPSEEK_API_KEY')
                api_base = "https://api.deepseek.com/v1"
                retry_client = OpenAI(api_key=api_key, base_url=api_base)

                # 再次尝试调用
                response = retry_client.chat.completions.create(**params)

                print("重试成功!")
                return response.choices[0].message.content
            except Exception as retry_e:
                print(f"重试失败: {str(retry_e)}")

        # 如果是提示词更新相关的调用，返回一个特殊的错误消息
        if "提示词" in prompt or "prompt" in prompt.lower():
            print("检测到这是提示词更新相关的调用，返回一个特殊的错误消息")
            return "ERROR_IN_PROMPT_UPDATE: 无法连接到LLM API，请使用原始提示词继续实验"

        # Fallback to a simple error message
        return f"Error: Unable to generate response. Please check if LLM API is available."


def check_lm_studio_availability():
    """Check if LM Studio is available at the specified URL"""
    try:
        # 检查服务器是否在线
        print("正在检查 LM Studio 服务器状态...")
        response = requests.get(f"{LM_STUDIO_API_BASE}/models", timeout=10)

        if response.status_code == 200:
            models = response.json()
            print("LM Studio 服务器可用。可用模型:")

            # 检查是否有可用模型
            available_models = models.get('data', [])
            if not available_models:
                print("警告: 没有找到可用模型，请在 LM Studio 中加载模型")
                return False

            # 检查 Gemma 模型是否可用
            gemma_model_id = MODEL_MAPPING["gemma-local"]
            gemma_available = False

            for model in available_models:
                model_id = model['id']
                print(f" - {model_id}")
                if model_id.lower() == gemma_model_id.lower() or gemma_model_id.lower() in model_id.lower():
                    gemma_available = True

            if not gemma_available:
                print(f"警告: 未找到 {gemma_model_id} 模型，请在 LM Studio 中加载该模型")
                print(f"可用模型: {[m['id'] for m in available_models]}")

            # 测试模型是否可以处理简单请求
            if gemma_available:
                print("正在测试模型响应能力...")
                try:
                    test_response = client.chat.completions.create(
                        model=gemma_model_id,
                        messages=[{"role": "user", "content": "Hello"}],
                        max_tokens=10,
                        temperature=0.0
                    )
                    print("模型测试成功!")
                except Exception as e:
                    print(f"模型测试失败: {str(e)}")
                    print("请检查 LM Studio 中的模型设置")
                    return False

            return True
        else:
            print(f"LM Studio 返回状态码: {response.status_code}")
            if response.status_code == 502:
                print("502 错误通常表示服务器过载或未正确响应")
                print("建议重启 LM Studio 并确保系统有足够资源")
            return False
    except requests.exceptions.Timeout:
        print("连接 LM Studio 超时，服务器可能响应缓慢")
        return False
    except requests.exceptions.ConnectionError:
        print("无法连接到 LM Studio，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"连接 LM Studio 时出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试提示
    test_prompt = "You are a poetic assistant, skilled in explaining complex programming concepts with creative flair. Compose a poem that explains the concept of recursion in programming."

    # 测试智谱AI API
    if USE_ZHIPUAI and ZHIPUAI_AVAILABLE:
        print("\n测试智谱AI API:")
        print(f"使用API密钥: {ZHIPUAI_API_KEY[:8]}...{ZHIPUAI_API_KEY[-8:]}")

        response = get_chat(
            test_prompt,
            "zhipu-glm4",
            debug=True
        )
        print("智谱AI响应:")
        print(response)
    # 测试DeepSeek API
    elif USE_DEEPSEEK_API:
        print("\n测试DeepSeek API:")
        print(f"使用API密钥: {client.api_key[-4:] if client.api_key else 'None'}")

        response = get_chat(
            test_prompt,
            "deepseek-chat",
            6216,
            debug=True
        )
        print("DeepSeek响应:")
        print(response)
    # 测试本地LM Studio
    else:
        # Check if LM Studio is available
        lm_studio_available = check_lm_studio_availability()

        if lm_studio_available:
            # Test with local Gemma model
            print("\n测试本地Gemma模型:")
            response = get_chat(
                test_prompt,
                "gemma-local",
                debug=True
            )
            print("LM Studio响应:")
            print(response)
        else:
            print("\n没有可用的API或本地模型。请检查配置。")