"""
Risk Management Module for Trading Agents
Implements Conditional Value at Risk (CVaR) and risk warning generation
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional

class RiskManager:
    def __init__(self, window_size: int = 5, confidence_level: float = 0.95, 
                 risk_threshold: float = 0.1, pnl_threshold: float = 0):
        """
        Initialize the Risk Manager
        
        Args:
            window_size: Number of days to look back for CVaR calculation
            confidence_level: Confidence level for CVaR (default 95%)
            risk_threshold: Threshold for CVaR change to trigger warning
            pnl_threshold: Threshold for daily PnL to contribute to warning
        """
        self.window_size = window_size
        self.confidence_level = confidence_level
        self.risk_threshold = risk_threshold
        self.pnl_threshold = pnl_threshold
        self.daily_returns = []
        self.cvar_history = []
        
    def update(self, daily_return: float) -> None:
        """
        Update the risk manager with a new daily return
        
        Args:
            daily_return: The return for the current day
        """
        self.daily_returns.append(daily_return)
        
        # Calculate CVaR if we have enough data
        if len(self.daily_returns) >= self.window_size:
            cvar = self._calculate_cvar(self.daily_returns[-self.window_size:])
            self.cvar_history.append(cvar)
    
    def _calculate_cvar(self, returns: List[float]) -> float:
        """
        Calculate the Conditional Value at Risk (CVaR)
        
        Args:
            returns: List of returns to calculate CVaR from
            
        Returns:
            The CVaR value
        """
        # Sort returns in ascending order (worst to best)
        sorted_returns = sorted(returns)
        
        # Calculate the VaR index (the cutoff point for the worst (1-confidence_level)% of returns)
        var_index = int(len(sorted_returns) * (1 - self.confidence_level))
        
        # If var_index is 0, use the worst return
        if var_index == 0:
            var_index = 1
            
        # Calculate CVaR as the average of returns below VaR
        cvar = np.mean(sorted_returns[:var_index])
        return cvar
    
    def generate_warning(self) -> Optional[str]:
        """
        Generate a risk warning if necessary
        
        Returns:
            A warning message if risk thresholds are exceeded, None otherwise
        """
        # Need at least two CVaR values to compare
        if len(self.cvar_history) < 2:
            return None
            
        # Get the current and previous CVaR
        current_cvar = self.cvar_history[-1]
        previous_cvar = self.cvar_history[-2]
        
        # Calculate the change in CVaR
        cvar_change = (current_cvar - previous_cvar) / abs(previous_cvar) if previous_cvar != 0 else 0
        
        # Check if the current return is negative
        current_return = self.daily_returns[-1]
        is_negative_return = current_return < self.pnl_threshold
        
        # Generate warning if CVaR has worsened significantly
        if cvar_change < -self.risk_threshold:
            warning_severity = "significant" if cvar_change < -2 * self.risk_threshold else "moderate"
            
            warning = (
                f"RISK WARNING: {warning_severity.upper()} increase in tail risk detected. "
                f"The CVaR (95%) has decreased by {abs(cvar_change)*100:.1f}% over the past {self.window_size} days. "
            )
            
            if is_negative_return:
                warning += f"Today's negative return of {current_return*100:.2f}% further indicates increased market risk. "
                
            warning += "Consider adjusting your position sizes and implementing more conservative trading strategies."
            
            return warning
            
        return None
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """
        Get the current risk metrics
        
        Returns:
            Dictionary of risk metrics
        """
        metrics = {
            "cvar": self.cvar_history[-1] if self.cvar_history else None,
            "cvar_change": None,
            "window_size": self.window_size,
            "confidence_level": self.confidence_level
        }
        
        if len(self.cvar_history) >= 2:
            current_cvar = self.cvar_history[-1]
            previous_cvar = self.cvar_history[-2]
            metrics["cvar_change"] = (current_cvar - previous_cvar) / abs(previous_cvar) if previous_cvar != 0 else 0
            
        return metrics
