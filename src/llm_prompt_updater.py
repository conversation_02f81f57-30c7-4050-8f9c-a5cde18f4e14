"""
LLM提示词更新模块

实现使用LLM进行提示词更新的功能，包括：
- 周期性评估交易表现
- 使用LLM分析表现并生成更新的提示词
- 保存原始提示词和更新后的提示词，以及更新原因
- 支持回退到原始提示词的功能
"""

import os
import json
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

class LLMPromptUpdater:
    def __init__(self,
                 cycle_length: int = 7,
                 performance_threshold: float = 0.0,
                 sharpe_threshold: float = 0.5,
                 consecutive_days_threshold: int = 3,
                 base_prompts: Optional[Dict[str, str]] = None,
                 run_name: str = "experiment"):
        """
        初始化LLM提示词更新器

        Args:
            cycle_length: 一个周期的天数（例如，7天为一周）
            performance_threshold: 性能阈值，低于此值触发更新
            sharpe_threshold: 夏普率阈值，低于此值触发更新
            consecutive_days_threshold: 连续不理想天数阈值
            base_prompts: 基础提示词字典，键为代理名称，值为提示词
            run_name: 运行名称，用于保存更新历史
        """
        self.cycle_length = cycle_length
        self.performance_threshold = performance_threshold
        self.sharpe_threshold = sharpe_threshold
        self.consecutive_days_threshold = consecutive_days_threshold

        # 运行名称
        self.run_name = run_name

        # 初始化基础提示词（保存原始提示词，不会被修改）
        self.base_prompts = base_prompts or {}

        # 当前提示词（会在实验过程中被更新）
        self.current_prompts = self.base_prompts.copy()

        # 创建提示词历史目录
        self.history_dir = os.path.join(run_name, 'prompt_history')
        os.makedirs(self.history_dir, exist_ok=True)

        # 保存原始提示词到文件
        self._save_original_prompts()

        # 更新历史
        self.update_history = []

        # 当前周期数据
        self.current_cycle_data = {
            'daily_returns': [],
            'sharpe_ratios': [],
            'actions': [],
            'agent_reasoning': [],
            'cycle_day': 0
        }

        # 周期计数
        self.cycle_count = 0

    def record_daily_data(self,
                         daily_return: float,
                         action: float,
                         reasoning: str) -> None:
        """
        记录每日交易数据

        Args:
            daily_return: 当日收益率
            action: 当日交易动作
            reasoning: 当日交易理由
        """
        self.current_cycle_data['daily_returns'].append(daily_return)
        self.current_cycle_data['actions'].append(action)
        self.current_cycle_data['agent_reasoning'].append(reasoning)

        # 计算当前夏普率
        if len(self.current_cycle_data['daily_returns']) > 1:
            returns = np.array(self.current_cycle_data['daily_returns'])
            sharpe = (np.mean(returns) / np.std(returns)) if np.std(returns) > 0 else 0
            self.current_cycle_data['sharpe_ratios'].append(sharpe)
        else:
            self.current_cycle_data['sharpe_ratios'].append(0)

        self.current_cycle_data['cycle_day'] += 1

    def should_update_prompts(self) -> bool:
        """
        判断是否应该更新提示词

        Returns:
            是否应该更新提示词
        """
        # 记录判断时间
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        print(f"\n{'='*60}")
        print(f"[{timestamp}] 检查是否需要更新提示词...")
        print(f"当前周期天数: {self.current_cycle_data['cycle_day']}")
        print(f"周期长度: {self.cycle_length}")
        print(f"当前周期: {self.cycle_count}")
        print(f"{'='*60}")

        # 检查是否到达周期结束
        if self.current_cycle_data['cycle_day'] < self.cycle_length:
            print(f"❌ 当前周期天数({self.current_cycle_data['cycle_day']})未达到周期长度({self.cycle_length})，不更新提示词")
            return False

        print(f"✅ 周期已完成，检查性能指标...")

        # 检查性能是否不理想
        recent_returns = self.current_cycle_data['daily_returns'][-self.consecutive_days_threshold:]
        recent_sharpe = self.current_cycle_data['sharpe_ratios'][-self.consecutive_days_threshold:]

        print(f"最近{self.consecutive_days_threshold}天收益率: {recent_returns}")
        print(f"最近{self.consecutive_days_threshold}天夏普率: {recent_sharpe}")

        # 检查连续几天收益率不理想
        low_returns_days = sum(1 for r in recent_returns if r < self.performance_threshold)

        # 检查连续几天夏普率不理想
        low_sharpe_days = sum(1 for s in recent_sharpe if s < self.sharpe_threshold)

        # 记录判断过程
        update_decision = {
            "timestamp": timestamp,
            "cycle_day": self.current_cycle_data['cycle_day'],
            "cycle_length": self.cycle_length,
            "recent_returns": recent_returns,
            "recent_sharpe": recent_sharpe,
            "performance_threshold": self.performance_threshold,
            "sharpe_threshold": self.sharpe_threshold,
            "consecutive_days_threshold": self.consecutive_days_threshold,
            "low_returns_days": low_returns_days,
            "low_sharpe_days": low_sharpe_days
        }

        # 判断是否需要更新
        should_update = low_returns_days >= self.consecutive_days_threshold or low_sharpe_days >= self.consecutive_days_threshold

        # 添加判断结果
        update_decision["should_update"] = should_update
        update_decision["update_reason"] = []

        if low_returns_days >= self.consecutive_days_threshold:
            reason = f"连续{low_returns_days}天收益率低于阈值{self.performance_threshold}"
            update_decision["update_reason"].append(reason)
            print(f"[{timestamp}] {reason}")

        if low_sharpe_days >= self.consecutive_days_threshold:
            reason = f"连续{low_sharpe_days}天夏普率低于阈值{self.sharpe_threshold}"
            update_decision["update_reason"].append(reason)
            print(f"[{timestamp}] {reason}")

        if not should_update:
            print(f"[{timestamp}] 性能指标良好，不需要更新提示词")

        # 保存判断记录
        update_decision_dir = os.path.join(self.run_name, 'update_decisions')
        os.makedirs(update_decision_dir, exist_ok=True)

        decision_file = os.path.join(update_decision_dir, f"update_decision_{timestamp}.json")
        with open(decision_file, 'w', encoding='utf-8') as f:
            json.dump(update_decision, f, ensure_ascii=False, indent=2)

        print(f"更新决策详细信息已保存到: {decision_file}")

        # 如果连续几天表现不理想，则更新提示词
        return should_update

    def generate_updated_prompts(self, llm_function) -> Dict[str, str]:
        """
        使用LLM生成更新的提示词

        Args:
            llm_function: 调用LLM的函数

        Returns:
            更新后的提示词字典
        """
        updated_prompts = {}

        # 为每个代理生成更新的提示词
        for agent_name, original_prompt in self.current_prompts.items():
            try:
                # 准备LLM提示
                prompt = self._prepare_update_prompt(agent_name, original_prompt)

                # 调用LLM生成更新的提示词
                print(f"正在为 {agent_name} 生成更新的提示词...")
                response = llm_function(prompt)

                # 检查响应是否包含错误信息
                if response.startswith("Error:") or "Unable to generate response" in response:
                    print(f"警告: LLM响应包含错误信息: {response[:100]}...")
                    print(f"使用原始提示词作为 {agent_name} 的更新提示词")
                    updated_prompt = original_prompt
                    update_reason = "LLM调用失败，使用原始提示词"
                else:
                    # 解析LLM响应
                    updated_prompt, update_reason = self._parse_llm_response(response, original_prompt)

                # 保存更新的提示词和更新原因
                updated_prompts[agent_name] = updated_prompt

                # 记录更新历史
                self._record_update(agent_name, original_prompt, updated_prompt, update_reason)
            except Exception as e:
                print(f"为 {agent_name} 生成更新提示词时出错: {e}")
                import traceback
                traceback.print_exc()

                # 使用原始提示词
                print(f"使用原始提示词作为 {agent_name} 的更新提示词")
                updated_prompts[agent_name] = original_prompt

                try:
                    # 尝试记录更新历史
                    self._record_update(agent_name, original_prompt, original_prompt, f"生成更新提示词时出错: {e}")
                except:
                    pass

        return updated_prompts

    def _prepare_update_prompt(self, agent_name: str, original_prompt: str) -> str:
        """
        准备用于LLM的提示词更新提示

        Args:
            agent_name: 代理名称
            original_prompt: 原始提示词

        Returns:
            用于LLM的提示
        """
        # 计算周期性能指标
        avg_return = np.mean(self.current_cycle_data['daily_returns']) * 100
        total_return = sum(self.current_cycle_data['daily_returns']) * 100

        if len(self.current_cycle_data['daily_returns']) > 1:
            returns = np.array(self.current_cycle_data['daily_returns'])
            sharpe = (np.mean(returns) / np.std(returns)) if np.std(returns) > 0 else 0
        else:
            sharpe = 0

        # 提取当前的instructions内容
        current_instructions = self._extract_instructions_content(original_prompt)

        # 准备提示
        prompt = f"""
你是一位专业的加密货币交易提示词优化专家。你的任务是分析当前交易策略的表现，并优化提示词中的指令部分以提高交易性能。

**重要约束**: 你只需要优化<instructions></instructions>标签内的内容，不要修改提示词的其他部分。

当前代理: {agent_name}
周期天数: {self.cycle_length}
平均日收益率: {avg_return:.2f}%
总收益率: {total_return:.2f}%
夏普比率: {sharpe:.2f}

最近的交易表现不理想，需要优化指令内容以提高性能。以下是当前<instructions></instructions>标签内的内容:

```
{current_instructions}
```

请分析当前指令内容，并提供以下内容:
1. 对当前指令的分析，指出可能导致表现不佳的问题
2. 优化后的指令内容（只需要<instructions></instructions>标签内的内容）
3. 简要说明你做出的更改及其理由

请确保优化后的指令:
- 保持原有的指令结构和编号格式
- 不改变代理的基本角色和任务
- 针对性地解决可能导致交易表现不佳的问题
- 更加强调风险管理和市场趋势分析
- 提供更具体和可操作的指导

请使用以下格式回答:

分析:
[你对当前指令的分析]

优化后的指令内容:
[只包含<instructions></instructions>标签内的优化内容，不包含标签本身]

更新理由:
[更新的主要理由和预期效果]
"""
        return prompt

    def _extract_instructions_content(self, prompt: str) -> str:
        """
        从提示词中提取<instructions></instructions>标签内的内容

        Args:
            prompt: 完整的提示词

        Returns:
            instructions标签内的内容，如果没有找到则返回空字符串
        """
        try:
            # 查找<instructions>和</instructions>标签
            start_tag = "<instructions>"
            end_tag = "</instructions>"

            start_index = prompt.find(start_tag)
            end_index = prompt.find(end_tag)

            if start_index != -1 and end_index != -1 and end_index > start_index:
                # 提取标签内的内容
                content_start = start_index + len(start_tag)
                instructions_content = prompt[content_start:end_index].strip()
                return instructions_content
            else:
                print(f"警告: 在提示词中未找到<instructions></instructions>标签")
                return ""
        except Exception as e:
            print(f"提取instructions内容时出错: {e}")
            return ""

    def _replace_instructions_content(self, original_prompt: str, new_instructions: str) -> str:
        """
        替换提示词中<instructions></instructions>标签内的内容

        Args:
            original_prompt: 原始提示词
            new_instructions: 新的指令内容

        Returns:
            更新后的完整提示词
        """
        try:
            # 查找<instructions>和</instructions>标签
            start_tag = "<instructions>"
            end_tag = "</instructions>"

            start_index = original_prompt.find(start_tag)
            end_index = original_prompt.find(end_tag)

            if start_index != -1 and end_index != -1 and end_index > start_index:
                # 构建新的提示词
                before_instructions = original_prompt[:start_index + len(start_tag)]
                after_instructions = original_prompt[end_index:]

                # 组合新的提示词
                updated_prompt = before_instructions + "\n" + new_instructions + "\n" + after_instructions
                return updated_prompt
            else:
                print(f"警告: 在提示词中未找到<instructions></instructions>标签，返回原始提示词")
                return original_prompt
        except Exception as e:
            print(f"替换instructions内容时出错: {e}")
            return original_prompt

    def _parse_llm_response(self, response: str, original_prompt: str) -> Tuple[str, str]:
        """
        解析LLM响应，提取更新的指令内容并替换到原始提示词中

        Args:
            response: LLM响应
            original_prompt: 原始提示词，用于回退

        Returns:
            更新的提示词和更新原因
        """
        # 默认值
        updated_prompt = original_prompt
        update_reason = "无法解析LLM响应，使用原始提示词"

        # 检查响应是否为空或包含错误信息
        if not response or response.startswith("Error:") or "Unable to generate response" in response:
            print(f"警告: LLM响应无效: {response[:100]}...")
            return updated_prompt, f"LLM响应无效: {response[:100]}..."

        try:
            # 尝试提取优化后的指令内容
            new_instructions = None

            if "优化后的指令内容:" in response:
                parts = response.split("优化后的指令内容:")
                if len(parts) > 1:
                    instructions_part = parts[1].strip()

                    # 如果有"更新理由:"，则提取指令部分
                    if "更新理由:" in instructions_part:
                        instructions_part = instructions_part.split("更新理由:")[0].strip()

                    # 检查提取的指令是否为空
                    if instructions_part and len(instructions_part) > 10:  # 确保指令有足够的长度
                        new_instructions = instructions_part
                    else:
                        print("警告: 提取的指令内容太短或为空")
            else:
                # 尝试其他可能的格式
                # 有些模型可能使用英文标记
                if "Optimized Instructions:" in response:
                    parts = response.split("Optimized Instructions:")
                    if len(parts) > 1:
                        instructions_part = parts[1].strip()
                        if "Update Reason:" in instructions_part:
                            instructions_part = instructions_part.split("Update Reason:")[0].strip()
                        if instructions_part and len(instructions_part) > 10:
                            new_instructions = instructions_part
                elif "Updated Instructions:" in response:
                    parts = response.split("Updated Instructions:")
                    if len(parts) > 1:
                        instructions_part = parts[1].strip()
                        if "Update Reason:" in instructions_part:
                            instructions_part = instructions_part.split("Update Reason:")[0].strip()
                        if instructions_part and len(instructions_part) > 10:
                            new_instructions = instructions_part

            # 如果成功提取到新的指令内容，则替换到原始提示词中
            if new_instructions:
                updated_prompt = self._replace_instructions_content(original_prompt, new_instructions)
                print(f"成功提取并替换指令内容，新指令长度: {len(new_instructions)} 字符")
            else:
                print("警告: 无法从LLM响应中提取有效的指令内容，使用原始提示词")

            # 尝试提取更新原因
            if "更新理由:" in response:
                parts = response.split("更新理由:")
                if len(parts) > 1:
                    update_reason = parts[1].strip()
            elif "Update Reason:" in response:
                parts = response.split("Update Reason:")
                if len(parts) > 1:
                    update_reason = parts[1].strip()
            elif "分析:" in response:
                parts = response.split("分析:")
                if len(parts) > 1:
                    analysis_part = parts[1].strip()
                    if "优化后的指令内容:" in analysis_part:
                        analysis_part = analysis_part.split("优化后的指令内容:")[0].strip()
                    update_reason = analysis_part
            elif "Analysis:" in response:
                parts = response.split("Analysis:")
                if len(parts) > 1:
                    analysis_part = parts[1].strip()
                    if "Optimized Instructions:" in analysis_part:
                        analysis_part = analysis_part.split("Optimized Instructions:")[0].strip()
                    update_reason = analysis_part
        except Exception as e:
            print(f"解析LLM响应时出错: {e}")
            import traceback
            traceback.print_exc()

        # 最后检查一下提取的提示词是否有效
        if updated_prompt == original_prompt:
            print("警告: 无法从LLM响应中提取有效的指令内容，使用原始提示词")
        else:
            print(f"成功更新提示词，保持原有结构，只更新了instructions部分")

        return updated_prompt, update_reason

    def _record_update(self, agent_name: str, original_prompt: str,
                      updated_prompt: str, update_reason: str) -> None:
        """
        记录提示词更新历史

        Args:
            agent_name: 代理名称
            original_prompt: 原始提示词
            updated_prompt: 更新后的提示词
            update_reason: 更新原因
        """
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        cycle_id = self.cycle_count

        update_record = {
            "agent_name": agent_name,
            "cycle_id": cycle_id,
            "timestamp": timestamp,
            "original_prompt": original_prompt,
            "updated_prompt": updated_prompt,
            "update_reason": update_reason,
            "performance_metrics": {
                "daily_returns": self.current_cycle_data['daily_returns'],
                "sharpe_ratios": self.current_cycle_data['sharpe_ratios'],
                "avg_return": np.mean(self.current_cycle_data['daily_returns']) * 100,
                "total_return": sum(self.current_cycle_data['daily_returns']) * 100
            }
        }

        # 添加到更新历史
        self.update_history.append(update_record)

        # 保存到文件
        filename = f"{agent_name}_cycle_{cycle_id}_{timestamp}.json"
        filepath = os.path.join(self.history_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(update_record, f, ensure_ascii=False, indent=2)

    def update_prompts(self, llm_function) -> Dict[str, str]:
        """
        更新提示词

        Args:
            llm_function: 调用LLM的函数

        Returns:
            更新后的提示词字典
        """
        if self.should_update_prompts():
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            print(f"\n{'🔄'*20} LLM提示词更新开始 {'🔄'*20}")
            print(f"[{timestamp}] 周期 {self.cycle_count} 结束，开始更新提示词...")
            print(f"更新的代理数量: {len(self.current_prompts)}")
            print(f"代理列表: {list(self.current_prompts.keys())}")

            try:
                # 生成更新的提示词
                print(f"[{timestamp}] 调用LLM生成更新的提示词...")
                updated_prompts = self.generate_updated_prompts(llm_function)

                # 检查更新的提示词是否有效
                if not updated_prompts:
                    print(f"❌ [{timestamp}] 警告: 生成的提示词为空，使用原始提示词")
                    updated_prompts = self.current_prompts.copy()

                # 检查每个提示词是否包含错误信息
                valid_updates = 0
                for agent_name, prompt in updated_prompts.items():
                    if prompt.startswith("Error:") or "Unable to generate response" in prompt:
                        print(f"❌ [{timestamp}] 警告: {agent_name} 的提示词包含错误信息，使用原始提示词")
                        updated_prompts[agent_name] = self.current_prompts.get(agent_name, self.base_prompts.get(agent_name, ""))
                    else:
                        valid_updates += 1
                        print(f"✅ [{timestamp}] {agent_name} 的提示词更新成功")

                print(f"[{timestamp}] 有效更新数量: {valid_updates}/{len(updated_prompts)}")

                # 更新当前提示词（但不修改base_prompts）
                self.current_prompts = updated_prompts

                # 保存更新后的提示词到单独的文件
                print(f"[{timestamp}] 保存更新后的提示词...")
                self._save_updated_prompts(updated_prompts)

                # 重置周期数据
                self.current_cycle_data = {
                    'daily_returns': [],
                    'sharpe_ratios': [],
                    'actions': [],
                    'agent_reasoning': [],
                    'cycle_day': 0
                }

                # 增加周期计数
                self.cycle_count += 1

                print(f"✅ [{timestamp}] LLM提示词更新完成！周期计数: {self.cycle_count}")
                print(f"{'🔄'*60}\n")

                return updated_prompts
            except Exception as e:
                print(f"更新提示词时出错: {e}")
                import traceback
                traceback.print_exc()

                print("使用原始提示词继续实验")

                # 重置周期数据，确保实验可以继续
                self.current_cycle_data = {
                    'daily_returns': [],
                    'sharpe_ratios': [],
                    'actions': [],
                    'agent_reasoning': [],
                    'cycle_day': 0
                }

                # 增加周期计数
                self.cycle_count += 1

                return self.current_prompts
        else:
            return self.current_prompts

    def _save_updated_prompts(self, updated_prompts: Dict[str, str]) -> None:
        """
        保存更新后的提示词到单独的文件

        Args:
            updated_prompts: 更新后的提示词字典
        """
        # 创建保存目录
        updated_prompts_dir = os.path.join(self.run_name, 'updated_prompts')
        os.makedirs(updated_prompts_dir, exist_ok=True)

        # 保存所有更新后的提示词到一个文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        all_prompts_file = os.path.join(updated_prompts_dir, f"all_updated_prompts_cycle_{self.cycle_count}_{timestamp}.json")

        with open(all_prompts_file, 'w', encoding='utf-8') as f:
            json.dump({
                "cycle_id": self.cycle_count,
                "timestamp": timestamp,
                "prompts": updated_prompts
            }, f, ensure_ascii=False, indent=2)

        print(f"所有更新后的提示词已保存到: {all_prompts_file}")

        # 为每个代理单独保存更新后的提示词
        for agent_name, prompt in updated_prompts.items():
            agent_prompt_file = os.path.join(updated_prompts_dir, f"{agent_name}_cycle_{self.cycle_count}_{timestamp}.txt")

            with open(agent_prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)

            print(f"{agent_name} 的更新后提示词已保存到: {agent_prompt_file}")

        # 【关键修复】保存temp_前缀文件，与env_history.py的加载逻辑匹配
        print(f"保存temp_前缀文件以供env_history.py加载...")
        for agent_name, prompt in updated_prompts.items():
            temp_prompt_path = os.path.join(self.run_name, f'temp_{agent_name}_prompt.txt')

            with open(temp_prompt_path, 'w', encoding='utf-8') as f:
                f.write(prompt)

            print(f"✅ {agent_name} 的temp提示词文件已保存到: {temp_prompt_path}")

    def rollback_prompts(self) -> Dict[str, str]:
        """
        回退到原始提示词

        Returns:
            原始提示词字典
        """
        # 回退到原始提示词（但不修改base_prompts）
        self.current_prompts = self.base_prompts.copy()

        # 记录回退操作
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # 创建保存目录
        rollback_dir = os.path.join(self.run_name, 'rollback_prompts')
        os.makedirs(rollback_dir, exist_ok=True)

        # 保存所有回退的提示词到一个文件
        all_prompts_file = os.path.join(rollback_dir, f"all_rollback_prompts_cycle_{self.cycle_count}_{timestamp}.json")

        with open(all_prompts_file, 'w', encoding='utf-8') as f:
            json.dump({
                "cycle_id": self.cycle_count,
                "timestamp": timestamp,
                "prompts": self.current_prompts,
                "reason": "回退到原始提示词，因为更新后的提示词表现不佳"
            }, f, ensure_ascii=False, indent=2)

        print(f"所有回退的提示词已保存到: {all_prompts_file}")

        for agent_name in self.current_prompts.keys():
            rollback_record = {
                "agent_name": agent_name,
                "cycle_id": self.cycle_count,
                "timestamp": timestamp,
                "original_prompt": self.current_prompts[agent_name],
                "updated_prompt": self.current_prompts[agent_name],
                "update_reason": "回退到原始提示词，因为更新后的提示词表现不佳",
                "performance_metrics": {
                    "daily_returns": self.current_cycle_data['daily_returns'],
                    "sharpe_ratios": self.current_cycle_data['sharpe_ratios'],
                    "avg_return": np.mean(self.current_cycle_data['daily_returns']) * 100 if self.current_cycle_data['daily_returns'] else 0,
                    "total_return": sum(self.current_cycle_data['daily_returns']) * 100 if self.current_cycle_data['daily_returns'] else 0
                }
            }

            # 添加到更新历史
            self.update_history.append(rollback_record)

            # 保存到历史文件
            filename = f"{agent_name}_cycle_{self.cycle_count}_rollback_{timestamp}.json"
            filepath = os.path.join(self.history_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(rollback_record, f, ensure_ascii=False, indent=2)

            # 为每个代理单独保存回退的提示词
            agent_prompt_file = os.path.join(rollback_dir, f"{agent_name}_rollback_cycle_{self.cycle_count}_{timestamp}.txt")

            with open(agent_prompt_file, 'w', encoding='utf-8') as f:
                f.write(self.current_prompts[agent_name])

            print(f"{agent_name} 的回退提示词已保存到: {agent_prompt_file}")

        return self.current_prompts

    def _save_original_prompts(self) -> None:
        """
        保存原始提示词到文件
        """
        # 创建保存目录
        original_prompts_dir = os.path.join(self.run_name, 'original_prompts')
        os.makedirs(original_prompts_dir, exist_ok=True)

        # 保存所有原始提示词到一个文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        all_prompts_file = os.path.join(original_prompts_dir, f"all_original_prompts_{timestamp}.json")

        with open(all_prompts_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": timestamp,
                "prompts": self.base_prompts
            }, f, ensure_ascii=False, indent=2)

        print(f"所有原始提示词已保存到: {all_prompts_file}")

        # 为每个代理单独保存原始提示词
        for agent_name, prompt in self.base_prompts.items():
            agent_prompt_file = os.path.join(original_prompts_dir, f"{agent_name}_original_{timestamp}.txt")

            with open(agent_prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)

            print(f"{agent_name} 的原始提示词已保存到: {agent_prompt_file}")

    def save_history(self) -> str:
        """
        保存完整的更新历史

        Returns:
            保存的文件路径
        """
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"prompt_update_history_{timestamp}.json"
        filepath = os.path.join(self.history_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.update_history, f, ensure_ascii=False, indent=2)

        return filepath
