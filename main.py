#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多智能体交易系统主程序入口

这是一个基于多智能体的加密货币交易系统，集成了以下功能：
- 多种交易策略（看涨/看跌）
- 概念学习框架（CVRF）
- LLM提示词动态更新
- 风险管理
- 区块链集成
- 性能分析和报告生成

使用方法：
    python main.py --help  # 查看所有可用参数
    python main.py --model gpt-4 --starting_date "2023-08-01" --ending_date "2023-09-01"
"""

import sys
import os
import argparse
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def setup_argument_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="多智能体交易系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本运行
  python main.py --model gpt-4 --starting_date "2023-08-01" --ending_date "2023-09-01"

  # 启用CVRF概念学习
  python main.py --model gpt-4 --use_cvrf 1 --cycle_length 7

  # 启用LLM提示词更新
  python main.py --model gpt-4 --use_llm_update 1 --performance_threshold 0.0

  # 启用风险管理
  python main.py --model gpt-4 --use_risk 1 --risk_threshold 0.1

  # 运行多个试验
  python main.py --model gpt-4 --num_trials 3
        """
    )

    # 基本参数
    parser.add_argument("--model", type=str, default="gpt-4",
                       help="LLM模型名称 (默认: gpt-4)")
    parser.add_argument("--starting_date", type=str, default='2023-08-01',
                       help="开始日期 (默认: 2023-08-01)")
    parser.add_argument("--ending_date", type=str, default='2023-09-01',
                       help="结束日期 (默认: 2023-09-01)")
    parser.add_argument("--num_trials", type=int, default=1,
                       help="试验次数 (默认: 1)")
    parser.add_argument("--run_name", type=str, default=None,
                       help="运行名称，用于保存结果")

    # 交易参数
    parser.add_argument("--price_window", type=int, default=7,
                       help="价格回看窗口大小 (默认: 7)")
    parser.add_argument("--reflection_window", type=int, default=3,
                       help="反思回看窗口大小 (默认: 3)")
    parser.add_argument("--seed", type=int, default=6216,
                       help="随机种子 (默认: 6216)")
    parser.add_argument("--use_memory", action='store_true',
                       help="启用智能体记忆功能")
    parser.add_argument("--use_tech", type=int, default=1,
                       help="启用技术分析信号 (默认: 1)")

    # CVRF概念学习参数
    parser.add_argument("--use_cvrf", type=int, default=0,
                       help="启用概念价值强化框架 (默认: 0)")
    parser.add_argument("--cycle_length", type=int, default=3,
                       help="交易周期长度（天） (默认: 3)")
    parser.add_argument("--learning_rate", type=float, default=0.2,
                       help="文本梯度下降学习率 (默认: 0.2)")

    # LLM提示词更新参数
    parser.add_argument("--use_llm_update", type=int, default=0,
                       help="启用LLM提示词更新 (默认: 0)")
    parser.add_argument("--performance_threshold", type=float, default=0.0,
                       help="性能阈值，低于此值触发更新 (默认: 0.0)")
    parser.add_argument("--sharpe_threshold", type=float, default=0.5,
                       help="夏普率阈值，低于此值触发更新 (默认: 0.5)")
    parser.add_argument("--consecutive_days_threshold", type=int, default=3,
                       help="连续不理想天数阈值 (默认: 3)")

    # 风险管理参数
    parser.add_argument("--use_risk", type=int, default=0,
                       help="启用风险管理 (默认: 0)")
    parser.add_argument("--risk_window_size", type=int, default=5,
                       help="风险计算窗口大小 (默认: 5)")
    parser.add_argument("--risk_confidence", type=float, default=0.95,
                       help="风险置信度 (默认: 0.95)")
    parser.add_argument("--risk_threshold", type=float, default=0.1,
                       help="风险阈值 (默认: 0.1)")

    # 区块链参数
    parser.add_argument("--use_blockchain", action='store_true',
                       help="启用区块链功能")
    parser.add_argument("--eth_rpc", type=str, default="HTTP://127.0.0.1:7545",
                       help="以太坊RPC地址 (默认: HTTP://127.0.0.1:7545)")
    parser.add_argument("--contract_address", type=str,
                       default="******************************************",
                       help="智能合约地址")

    # 数据和输出参数
    parser.add_argument("--data_dir", type=str, default="data",
                       help="数据目录 (默认: data)")
    parser.add_argument("--output_dir", type=str, default="results",
                       help="输出目录 (默认: results)")
    parser.add_argument("--verbose", action='store_true',
                       help="详细输出模式")

    return parser

def validate_arguments(args):
    """验证命令行参数"""
    errors = []

    # 验证日期格式
    try:
        datetime.strptime(args.starting_date, '%Y-%m-%d')
        datetime.strptime(args.ending_date, '%Y-%m-%d')
    except ValueError:
        errors.append("日期格式应为 YYYY-MM-DD")

    # 验证日期逻辑
    if args.starting_date >= args.ending_date:
        errors.append("开始日期必须早于结束日期")

    # 验证数值参数
    if args.num_trials < 1:
        errors.append("试验次数必须大于0")

    if args.price_window < 1:
        errors.append("价格窗口大小必须大于0")

    if args.cycle_length < 1:
        errors.append("周期长度必须大于0")

    if not 0 <= args.learning_rate <= 1:
        errors.append("学习率必须在0-1之间")

    if not 0 <= args.risk_confidence <= 1:
        errors.append("风险置信度必须在0-1之间")

    if args.risk_threshold < 0:
        errors.append("风险阈值必须大于等于0")

    return errors

def main():
    """主函数"""
    parser = setup_argument_parser()
    args = parser.parse_args()

    # 验证参数
    errors = validate_arguments(args)
    if errors:
        print("❌ 参数验证失败:")
        for error in errors:
            print(f"  - {error}")
        sys.exit(1)

    # 设置运行名称
    if args.run_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.run_name = f"multiagent_trade_{timestamp}"

    # 显示配置信息
    print("🚀 多智能体交易系统启动")
    print("=" * 50)
    print(f"模型: {args.model}")
    print(f"时间范围: {args.starting_date} 到 {args.ending_date}")
    print(f"试验次数: {args.num_trials}")
    print(f"运行名称: {args.run_name}")
    print(f"CVRF概念学习: {'启用' if args.use_cvrf else '禁用'}")
    print(f"LLM提示词更新: {'启用' if args.use_llm_update else '禁用'}")
    print(f"风险管理: {'启用' if args.use_risk else '禁用'}")
    print(f"区块链功能: {'启用' if args.use_blockchain else '禁用'}")
    print("=" * 50)

    try:
        # 导入并运行主要的交易系统
        import run_agent

        # 将参数传递给原始的run_agent函数
        sys.argv = ['run_agent.py'] + [
            f'--{k}' if v is True else f'--{k}={v}' if v is not False else ''
            for k, v in vars(args).items() if v is not False
        ]
        sys.argv = [arg for arg in sys.argv if arg]  # 移除空字符串

        # 运行交易系统
        run_agent.main()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
