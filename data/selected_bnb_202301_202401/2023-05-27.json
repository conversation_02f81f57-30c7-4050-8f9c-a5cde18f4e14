[{"id": 9, "url": "https://news.google.com/rss/articles/CBMiXmh0dHBzOi8vdGhlbGluY29sbml0ZS5jby51ay8yMDIzLzA1L2FpcmJuYi1zdHlsZS1hY2NvbW1vZGF0aW9uLWNvbWluZy10by1saW5jb2xucy1oaWdoLXN0cmVldC_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 27 May 2023 07:00:00 GMT", "title": "Airbnb-style accommodation coming to Lincoln's High Street - The Lincolnite", "content": "This story is over 12 months old\n\nThis story is over 12 months old\n\nPlans for Air BnB-style accommodation on Lincoln’s High Street have been approved by council officers.\n\nSeven short-term serviced apartments will be created in the former Superfi store, close to the foot of Steep Hill.\n\nDevelopers had originally gained approval from the City of Lincoln Council to use the first, second and third floors as HMOs, however this wasn’t carried out.\n\nInstead, owner Mr <PERSON><PERSON> has secured permission for the new use.\n\nAirBnB offers a marketplace for owners to rent out their properties to visitors, and is often used by those on holiday.\n\nGuests will have no shortage of restaurants, bars and other tourist attractions in the area.\n\nRecommending the project be given the go ahead, a report said: “The provision of tourist facilities is appropriate in this location and would complement the mix of uses in the area, which in turn would not detract from the vitality and viability of the city centre.”\n\nThey said it would not impact neighbouring properties such as Pizza Express and Craft and that previously agreed noise mitigation measures would safeguard the amenities of future users.\n\n“Matters in relation to levels of traffic and highways have been appropriately considered and there are no issues in this respect,” they said.\n\n“The alterations to the shopfront are welcomed and are sympathetic to the building and the character of the streetscene.”\n\nOfficers also approved works to repaint and reinstate some traditional features to the shop front following works which “go beyond” what was originally agreed.\n\nMyLocal Lincolnshire is the new home of The Lincolnite. Download the app now."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiLWh0dHBzOi8vbGl2ZWFuZGxldHNmbHkuY29tL2FpcmJuYi1hbGl4LWVhcmxlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 27 May 2023 07:00:00 GMT", "title": "Airbnb Saves The Day After <PERSON>y Influencer \"Scammed\" In Italy - Live and Let's Fly", "content": "After a TikTok star with millions of followers was purportedly scammed out of a seaside villa in Positano, Italy, Airbnb came rushing to the rescue. If I was writing this script, it could not have worked out better….for Airbnb’s marketing team.\n\nAirbnb Saves Positano Trip For Alix Earle And Friends…Or Did We Just Get Played?\n\nTikTok star <PERSON><PERSON> has a very large following on various social media platforms, including nearly six million on TikTok. She just graduated from college and is on a girls’ trip to Europe. The first stop was Ibiza and now they are in Positano, Italy.\n\nIn Ibiza, they stayed at an Airbnb. They then flew EasyJet to Italy and that’s when things went sideways, at least according <PERSON>.\n\n“We’re stranded in Italy. The house we were supposed to stay at doesn’t exist. Our car service canceled. It’s midnight. “We literally don’t know where to go. 11 girls stranded in Positano. The girls’ trip took a turn.”\n\nSeems a bit dramatic, doesn’t it? No car service? A house that doesn’t exist? I mean, I can understand the driver not showing up, but a fake house? I’m having a hard time buying that or at least that both things happened at once. <PERSON> never reveals the source of the “scam” Positano booking, but if she was staying at an AirBnB house in Ibiza, wouldn’t you think she would also rent an Airbnb house in Positano too?\n\nIn any case, in a follow-up video <PERSON> and her friends heap praise upon Airbnb for saving the day while giving a tour of their palatial villa. The implication is that Airbnb saw her predicament and reached out to offer her a villa. But her language remains very vague: did she book originally through Airbnb? Did Airbnb mess up or simply rush in to help the damsels in distress?\n\nhttps://www.tiktok.com/@alixearle/video/7236783525163257134\n\nBecause if she did book the scam villa through Airbnb, then I’m not sure Airbnb did her any favor causing the hassle in the first place. Unless that “hassle” was part of the plan…\n\nI don’t believe anything I see on TikTok, so my theory (and it is just a theory) is that this entire thing was a set-up engineered by Earle and the marketing team at Airbnb. First, you make it look like they are scammed and without a place to stay. Then Airbnb rushes in for the rescue and is heaped with great praise. That’s a decent exchange considering her videos garner millions of views and the UK Daily Mail reports she earns $70,000 per post (not a bad day’s work…).\n\nCONCLUSION\n\nI may be too cynical, but I’ve covered so many travel-related stories on TikTok that turned out to be fake. Here, the lack of driver and lack of villa and Airbnb immediately coming to the rescue all seems too coincidental and too convenient for me. But it makes for good clickbait in more than one way…\n\n(H/T: View From The Wing)"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiNmh0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3QvYnJjLTIwLXRoZS1nb29kLWJhZC1hbmQtdWdsedIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 27 May 2023 07:00:00 GMT", "title": "BRC-20: The Good, Bad, And Ugly - BSC NEWS", "content": "BTC\n\nThe BRC-20 token standard brings promise and challenges to the Bitcoin blockchain after emerging as a top player in the crypto space.\n\nTL;DR:\n\nBRC-20 tokens on the Bitcoin blockchain bring enhanced tokenization, simplifying the creation and management of digital assets.\n\nInteroperability with existing Bitcoin infrastructure enables seamless integration and easy trading of BRC-20 tokens.\n\nWhile BRC-20 tokens inherit the security and immutability of the Bitcoin blockchain, they have limited smart contract functionality compared to other token standards.\n\nScalability challenges, regulatory uncertainties, and a lack of standardization obstruct the widespread adoption of BRC-20 tokens on Bitcoin.\n\nExploring the Advantages and Challenges of BRC-20 Tokens on Bitcoin\n\nThe BRC-20 token standard has emerged as a noteworthy player within the Bitcoin ecosystem. Developed using the Ordinals and Inscriptions protocol, the BRC-20 standard has brought a new dimension to the world of tokenization on the Bitcoin blockchain.\n\nHowever, like any technological innovation, it has advantages, drawbacks, and potential pitfalls. In this article, we will look at the good, the bad, and the ugly aspects of BRC-20.\n\nThe Good\n\nEnhanced Tokenization\n\nBRC-20 provides a means for users to tokenize assets on the Bitcoin blockchain. This opens up new possibilities for creating digital representations of various real-world assets. In addition, the standardization of tokens simplifies creating, transferring, and managing these assets.\n\nInteroperability\n\nBRC-20 tokens are designed to be compatible with existing Bitcoin infrastructure, including wallets and exchanges. This compatibility fosters interoperability, seamlessly integrating BRC-20 tokens into the broader Bitcoin ecosystem. In addition, it enables users to trade and transfer tokens without the need for complex bridges or additional layers of technology.\n\nWhile this is an outstanding feature, its interoperability is limited to just the Bitcoin ecosystem.\n\nSecurity and Immutability\n\nLeveraging the security and immutability of the Bitcoin blockchain, BRC-20 tokens inherit these robust features. Transactions and token ownership are transparently recorded on the blockchain, reducing the risk of fraud or manipulation. Further, the decentralized nature of Bitcoin ensures that token data remains tamper-proof, providing increased trust and reliability.\n\nThe Bad\n\nLimited Smart Contract Functionality\n\nAlthough BRC-20 tokens allow for basic tokenization, their smart contract capabilities are limited compared to other token standards, such as ERC-20 on Ethereum.\n\nThe Bitcoin blockchain was primarily designed for secure and efficient value transfer rather than complex programmability. As a result, BRC-20 tokens lack advanced features for Decentralized Finance (DeFi) functionalities or complex logic execution.\n\nScalability Challenges\n\nThe Bitcoin blockchain has faced scalability challenges due to its limited block size and transaction throughput. BRC-20 tokens inherit these limitations, potentially leading to increased transaction fees and longer confirmation times during periods of high network congestion. This can hinder the usability and adoption of BRC-20 tokens, particularly for applications requiring fast and cost-effective transactions.\n\nThe Ugly\n\nRegulatory Uncertainty\n\nGenerally, tokenized assets and cryptocurrencies have faced regulatory scrutiny worldwide. BRC-20 tokens, integral to the Bitcoin blockchain, are not exempt from these uncertainties.\n\nJurisdictions have different approaches to cryptocurrency regulation, and compliance requirements can be complex and burdensome. This regulatory landscape can create barriers to widespread adoption and hinder the growth of BRC-20 tokens.\n\nLack of Standardization\n\nBRC-20 is not a widely adopted token standard compared to ERC-20 tokens on Ethereum. The lack of standardization can lead to fragmentation in the token ecosystem, making it challenging for developers and users to navigate and interact with BRC-20 implementations.\n\nAdditionally, the absence of well-established tooling and developer resources can slow the innovation and adoption of BRC-20 tokens.\n\nIn conclusion, the BRC-20 token standard brings promise and challenges to the Bitcoin blockchain. However, while it enables tokenization and enhances interoperability, it also faces smart contract functionality and scalability limitations.\n\nFurthermore, regulatory uncertainties and the lack of standardization pose significant hurdles to the widespread adoption of BRC-20 tokens. As the blockchain ecosystem evolves, it will be intriguing to observe how BRC-20 on Bitcoin evolves to address these challenges and unlock new opportunities for users and developers.\n\n‍"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiY2h0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvc29sYXJpcy1uZXR3b3JrLXRoZS1maXJzdC1vbi1jaGFpbi1zeW50aGV0aWMtYXNzZXRzLXNvbHV0aW9uLW9uLWJuYi1jaGFpbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 27 May 2023 07:00:00 GMT", "title": "Solaris Network: The First On-Chain Synthetic Assets Solution on BNB Chain - BSC NEWS", "content": "BNB\n\nBy providing a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market, Solaris aims to bridge the gap between blockchain and traditional finance.\n\nSolaris Network is a groundbreaking project that offers on-chain synthetic asset solutions on BNB Chain. Solaris lays the groundwork for a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market by building an exceptional bridge between blockchain and the actual financial sector.\n\nThis technical analysis explores Solaris Network's synthetic assets, including their significance, minting process, and use cases. It also discusses staking, one of the most common financial forms in the blockchain application field.\n\nWhat is DeFi Derivatives Market?\n\nDecentralized finance (DeFi) has become one of the most dynamic sectors in the crypto space. Derivatives trading is an area of DeFi that has been attracting attention recently. Derivatives are financial contracts whose value comes from an underlying asset like stocks, commodities, or cryptocurrencies. In traditional finance, derivatives are traded on centralized exchanges to hedge risks or speculate on price movements. In the DeFi space, derivatives are being used to create new financial instruments that were not possible before.\n\nSynthetic Assets\n\nBlockchain synthetic assets are financial instruments issued and circulated on the blockchain, composed of one or several assets (or derivatives).\n\nSolaris Network's synthetic assets mint corresponding synthetic assets on the blockchain by simulating cryptocurrency, NFTs, and real-world financial asset-related products. These synthetic assets cover the global financial ecosystem on and off the blockchain, providing investors with more diverse, convenient, and secure asset allocation strategies.\n\nSolaris explores the unlimited potential of various assets in the DeFi field and enhances asset allocation's complexity and innovative attributes.\n\nSolaris Network: The First On-Chain Synthetic Assets Solution\n\nSolaris Network offers on-chain synthetic asset solutions on BNB Chain, bridging the gap between blockchain and the traditional financial world. Its platform provides a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market, enabling users to trade a wide range of synthetic assets.\n\nSolaris' approach to synthetic assets is unique because it allows users to create custom assets, giving them unparalleled flexibility and control over their investments. The platform also features advanced trading tools, such as limit orders and stop losses, as well as low transaction fees and fast settlement times, making it a powerful tool for retail and institutional investors.\n\nMinting Synthetic Assets on Solaris Network\n\nTo mint sla synthetic assets, participants need to stake mainstream assets. Users collateralize mainstream crypto assets and mint sla synthetic assets according to the real-time price of the initial assets obtained by the oracle. The maximum number of synthetic assets that can be minted is calculated using the formula:\n\nmaxAm=maxVmPA\n\nHere, maxAm represents the maximum number of synthetic assets that can be minted; PA represents the synthetic asset price obtained from the oracle; maxAm represents the total value of synthetic assets that can be minted.\n\nSolaris Synthetic Assets (so) Use Cases:\n\n\"so\" synthetic assets offer investors a broader range of options, such as inverse crypto assets, index-based synthetic assets, NFT assets, and fractionalized NFT assets. These allow investors to short cryptocurrencies, focus on specific cryptocurrency sectors, and profit from NFT asset price fluctuations with smaller capital scales.\n\n\"so\" synthetic assets provide cryptocurrency enthusiasts access to traditional financial assets, allowing them to invest quickly and conveniently.\n\nTraditional investors can use \"so\" synthetic assets to hedge against short-term downside risks, such as holding gold futures in the real-world financial market.\n\nUsers can add \"so\" synthetic assets to the Solaris DEX to provide liquidity and earn dividends from trading fees.\n\n\"so\" Synthetic Assets Advantages:\n\nValue protection: \"so\" synthetic assets are minted with the backing of mainstream asset value, using oracles to obtain the price of the underlying assets, ensuring the value of synthetic assets.\n\nDiversification: \"so\" synthetic assets provide investors with a comprehensive, one-stop decentralized investment platform for participation in the on-chain ecosystem and traditional finance.\n\nCross-chain Support: Solaris supports circulation on all mainstream public chains (including Binance Smart Chain, Ethereum, Polygon, Arbitrum, Optimism, Cosmos, etc.) and allows them to participate in project services.\n\nDecentralized, transparent, anonymous, and convenient: Minting and trading synthetic assets on the blockchain eliminates centralized intervention, achieve on-chain data visibility, and allow anonymous trading without KYC.\n\nThe Takeaway — Enjoy Barrier-Free DeFi Trading\n\nThe Solaris Network is revolutionizing the DeFi market by providing a unique platform for trading synthetic assets. It gives instant access to the market without intermediaries and utilizes real-time price feeds obtained through oracles.\n\nUsers can easily trade mainstream assets without fearing liquidity and price slippage. Solaris is well-positioned to play a pivotal role in determining the future of decentralized finance as the usage of DeFi and blockchain technology continues to grow. The network's focus on innovation, transparency, and security indicates its commitment to providing its users with the best experience possible.\n\nYou can check out the following Media for more information and updates about our platform:\n\nTwitter | Telegram | Discord | Gitbook"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiY2h0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvc29sYXJpcy1uZXR3b3JrLXRoZS1maXJzdC1vbi1jaGFpbi1zeW50aGV0aWMtYXNzZXRzLXNvbHV0aW9uLW9uLWJuYi1jaGFpbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 27 May 2023 07:00:00 GMT", "title": "Solaris Network: The First On-Chain Synthetic Assets Solution on BNB Chain - BSC NEWS", "content": "BNB\n\nBy providing a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market, Solaris aims to bridge the gap between blockchain and traditional finance.\n\nSolaris Network is a groundbreaking project that offers on-chain synthetic asset solutions on BNB Chain. Solaris lays the groundwork for a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market by building an exceptional bridge between blockchain and the actual financial sector.\n\nThis technical analysis explores Solaris Network's synthetic assets, including their significance, minting process, and use cases. It also discusses staking, one of the most common financial forms in the blockchain application field.\n\nWhat is DeFi Derivatives Market?\n\nDecentralized finance (DeFi) has become one of the most dynamic sectors in the crypto space. Derivatives trading is an area of DeFi that has been attracting attention recently. Derivatives are financial contracts whose value comes from an underlying asset like stocks, commodities, or cryptocurrencies. In traditional finance, derivatives are traded on centralized exchanges to hedge risks or speculate on price movements. In the DeFi space, derivatives are being used to create new financial instruments that were not possible before.\n\nSynthetic Assets\n\nBlockchain synthetic assets are financial instruments issued and circulated on the blockchain, composed of one or several assets (or derivatives).\n\nSolaris Network's synthetic assets mint corresponding synthetic assets on the blockchain by simulating cryptocurrency, NFTs, and real-world financial asset-related products. These synthetic assets cover the global financial ecosystem on and off the blockchain, providing investors with more diverse, convenient, and secure asset allocation strategies.\n\nSolaris explores the unlimited potential of various assets in the DeFi field and enhances asset allocation's complexity and innovative attributes.\n\nSolaris Network: The First On-Chain Synthetic Assets Solution\n\nSolaris Network offers on-chain synthetic asset solutions on BNB Chain, bridging the gap between blockchain and the traditional financial world. Its platform provides a comprehensive infrastructure for the multi-chain Web 3.0 financial derivatives market, enabling users to trade a wide range of synthetic assets.\n\nSolaris' approach to synthetic assets is unique because it allows users to create custom assets, giving them unparalleled flexibility and control over their investments. The platform also features advanced trading tools, such as limit orders and stop losses, as well as low transaction fees and fast settlement times, making it a powerful tool for retail and institutional investors.\n\nMinting Synthetic Assets on Solaris Network\n\nTo mint sla synthetic assets, participants need to stake mainstream assets. Users collateralize mainstream crypto assets and mint sla synthetic assets according to the real-time price of the initial assets obtained by the oracle. The maximum number of synthetic assets that can be minted is calculated using the formula:\n\nmaxAm=maxVmPA\n\nHere, maxAm represents the maximum number of synthetic assets that can be minted; PA represents the synthetic asset price obtained from the oracle; maxAm represents the total value of synthetic assets that can be minted.\n\nSolaris Synthetic Assets (so) Use Cases:\n\n\"so\" synthetic assets offer investors a broader range of options, such as inverse crypto assets, index-based synthetic assets, NFT assets, and fractionalized NFT assets. These allow investors to short cryptocurrencies, focus on specific cryptocurrency sectors, and profit from NFT asset price fluctuations with smaller capital scales.\n\n\"so\" synthetic assets provide cryptocurrency enthusiasts access to traditional financial assets, allowing them to invest quickly and conveniently.\n\nTraditional investors can use \"so\" synthetic assets to hedge against short-term downside risks, such as holding gold futures in the real-world financial market.\n\nUsers can add \"so\" synthetic assets to the Solaris DEX to provide liquidity and earn dividends from trading fees.\n\n\"so\" Synthetic Assets Advantages:\n\nValue protection: \"so\" synthetic assets are minted with the backing of mainstream asset value, using oracles to obtain the price of the underlying assets, ensuring the value of synthetic assets.\n\nDiversification: \"so\" synthetic assets provide investors with a comprehensive, one-stop decentralized investment platform for participation in the on-chain ecosystem and traditional finance.\n\nCross-chain Support: Solaris supports circulation on all mainstream public chains (including Binance Smart Chain, Ethereum, Polygon, Arbitrum, Optimism, Cosmos, etc.) and allows them to participate in project services.\n\nDecentralized, transparent, anonymous, and convenient: Minting and trading synthetic assets on the blockchain eliminates centralized intervention, achieve on-chain data visibility, and allow anonymous trading without KYC.\n\nThe Takeaway — Enjoy Barrier-Free DeFi Trading\n\nThe Solaris Network is revolutionizing the DeFi market by providing a unique platform for trading synthetic assets. It gives instant access to the market without intermediaries and utilizes real-time price feeds obtained through oracles.\n\nUsers can easily trade mainstream assets without fearing liquidity and price slippage. Solaris is well-positioned to play a pivotal role in determining the future of decentralized finance as the usage of DeFi and blockchain technology continues to grow. The network's focus on innovation, transparency, and security indicates its commitment to providing its users with the best experience possible.\n\nYou can check out the following Media for more information and updates about our platform:\n\nTwitter | Telegram | Discord | Gitbook"}]