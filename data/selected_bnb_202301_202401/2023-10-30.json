[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ibmItY2hhaW4tbGF1bmNoZXMtc2VjdXJlLW11bHRpLXNpZ25hdHVyZS13YWxsZXQtc2VydmljZS_SAVZodHRwczovL2NyeXB0b3BvdGF0by5jb20vYm5iLWNoYWluLWxhdW5jaGVzLXNlY3VyZS1tdWx0aS1zaWduYXR1cmUtd2FsbGV0LXNlcnZpY2UvP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Oct 2023 07:00:00 GMT", "title": "BNB Chain Launches Secure Multi-Signature Wallet Service - CryptoPotato", "content": "In an effort to boost security, the BNB Chain has recently introduced a secure multi-signature wallet service known as BNB Safe{Wallet}.\n\nThis service is built upon the Gnosis Safe protocol and is now accessible on both the Binance Smart Chain (BSC) and opBNB networks.\n\nThe Gnosis Safe Multisig Wallet\n\nThe BNB Chain has recently launched a multi-signature wallet service called BNB Safe{Wallet}, built on the Gnosis Safe protocol. The latter is a smart contract wallet known for its high-level security features, robust access control, and sophisticated execution logic.\n\nOne of its standout features is the ability to allow multiple wallets to be controlled by one or more owners, adding an extra layer of security.\n\nToday, we’re launching the BNB Safe{Wallet} 🔒 Our multi-sig wallet is based on the Gnosis Safe protocol and now live on BSC + opBNB. It offers a secure way to manage digital assets! Create BNB Safe Account:https://t.co/7nJiV3vcqD Documentation:https://t.co/yFzeupHeb2 pic.twitter.com/6h6gGyA2ZX — BNB Chain (@BNBCHAIN) October 28, 2023\n\nBNBChain’s Safe multi-signature wallet service offers digital asset storage with user-centric security measures. Users can customize their security preferences, selecting owner accounts and specifying the minimum number of confirmations required for transactions.\n\nTo get started with the BNB Chain multi-signature wallet service, users are required to create a Safe. Gnosis Safe is a protocol and platform that enables decentralized custody and management of assets across several networks, including Ethereum (ETH), zkSync, Arbitrum, BNB Smart Chain, EVM, and Ethereum Mainnet.\n\nIts web3-enabled tool, known as Safe Wallet, simplifies interaction with the DeFi and web3 ecosystem, thereby enhancing the security of assets and promoting cooperative asset management.\n\nSecurity Breaches on the BNB Chain\n\nRecent years have witnessed numerous security breaches and attacks targeting the BNBChain network, raising concerns about the safety of users’ assets. Notable incidents include the July 2023 Vyper Copycat Exploit on BSC, where vulnerabilities in the Vyper programming language resulted in cryptocurrency theft.\n\nAdditionally, a major attack in October 2022 on Binance (BNB) saw hackers exploiting weaknesses in the BNB network, leading to substantial financial losses.\n\nIn September 2023, hackers who previously targeted the Stake casino for $41 million made off with approximately $328,000 million worth of BNB (BNB) and Polygon (MATIC) tokens, further highlighting the need for robust security measures within the ecosystem.\n\nAccording to a blog post by BNB Chain, the introduction of the BNB Safe{Wallet}, BNB Chain aims to provide users with a secure and dependable solution for managing their digital assets, offering peace of mind in an increasingly complex and challenging crypto landscape."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ibmItY2hhaW4tbGF1bmNoZXMtc2VjdXJlLW11bHRpLXNpZ25hdHVyZS13YWxsZXQtc2VydmljZS_SAVZodHRwczovL2NyeXB0b3BvdGF0by5jb20vYm5iLWNoYWluLWxhdW5jaGVzLXNlY3VyZS1tdWx0aS1zaWduYXR1cmUtd2FsbGV0LXNlcnZpY2UvP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Oct 2023 07:00:00 GMT", "title": "BNB Chain Launches Secure Multi-Signature Wallet Service - CryptoPotato", "content": "In an effort to boost security, the BNB Chain has recently introduced a secure multi-signature wallet service known as BNB Safe{Wallet}.\n\nThis service is built upon the Gnosis Safe protocol and is now accessible on both the Binance Smart Chain (BSC) and opBNB networks.\n\nThe Gnosis Safe Multisig Wallet\n\nThe BNB Chain has recently launched a multi-signature wallet service called BNB Safe{Wallet}, built on the Gnosis Safe protocol. The latter is a smart contract wallet known for its high-level security features, robust access control, and sophisticated execution logic.\n\nOne of its standout features is the ability to allow multiple wallets to be controlled by one or more owners, adding an extra layer of security.\n\nToday, we’re launching the BNB Safe{Wallet} 🔒 Our multi-sig wallet is based on the Gnosis Safe protocol and now live on BSC + opBNB. It offers a secure way to manage digital assets! Create BNB Safe Account:https://t.co/7nJiV3vcqD Documentation:https://t.co/yFzeupHeb2 pic.twitter.com/6h6gGyA2ZX — BNB Chain (@BNBCHAIN) October 28, 2023\n\nBNBChain’s Safe multi-signature wallet service offers digital asset storage with user-centric security measures. Users can customize their security preferences, selecting owner accounts and specifying the minimum number of confirmations required for transactions.\n\nTo get started with the BNB Chain multi-signature wallet service, users are required to create a Safe. Gnosis Safe is a protocol and platform that enables decentralized custody and management of assets across several networks, including Ethereum (ETH), zkSync, Arbitrum, BNB Smart Chain, EVM, and Ethereum Mainnet.\n\nIts web3-enabled tool, known as Safe Wallet, simplifies interaction with the DeFi and web3 ecosystem, thereby enhancing the security of assets and promoting cooperative asset management.\n\nSecurity Breaches on the BNB Chain\n\nRecent years have witnessed numerous security breaches and attacks targeting the BNBChain network, raising concerns about the safety of users’ assets. Notable incidents include the July 2023 Vyper Copycat Exploit on BSC, where vulnerabilities in the Vyper programming language resulted in cryptocurrency theft.\n\nAdditionally, a major attack in October 2022 on Binance (BNB) saw hackers exploiting weaknesses in the BNB network, leading to substantial financial losses.\n\nIn September 2023, hackers who previously targeted the Stake casino for $41 million made off with approximately $328,000 million worth of BNB (BNB) and Polygon (MATIC) tokens, further highlighting the need for robust security measures within the ecosystem.\n\nAccording to a blog post by BNB Chain, the introduction of the BNB Safe{Wallet}, BNB Chain aims to provide users with a secure and dependable solution for managing their digital assets, offering peace of mind in an increasingly complex and challenging crypto landscape."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiNGh0dHBzOi8vY29pbmRvby5jb20vaW1wb3J0LXRydXN0LXdhbGxldC10by1tZXRhbWFzay_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 30 Oct 2023 07:00:00 GMT", "title": "How to Import Trust Wallet to MetaMask? - Latest Cryptocurrency Prices & Articles", "content": "Simply buying some crypto coins or tokens is not everything you should do. You also have to find the best ways to store crypto, as you cannot put it in your pocket, like fiat coins. While the industry is sometimes affected by various hacks and thefts, searching for the safest crypto storage options might seem energy-draining and time-consuming.\n\nStill, finding a safe crypto wallet is not hard, as the best options are often among the most widely used. So, while you research the best crypto wallets on the market, you might notice that Trust Wallet and MetaMask are 2 of the most popular wallets at the moment. And if you, too, use them, this article will show you how to import your Trust Wallet account to MetaMask.\n\nAbout Trust Wallet\n\nLaunched in 2017 by <PERSON>, Trust Wallet is a storage option many crypto investors use. In 2018, the project was acquired by Binance, making it even more trustworthy than before. Currently, the Trust Wallet community gathers over 60 million users from all over the world.\n\nTrust Wallet is a non-custodial software blockchain wallet, meaning users have complete control over their digital assets. The wallet is available as a mobile app for iOS and Android devices. Furthermore, the company recently developed the Trust Wallet extension, currently available on Chrome, Edge, Opera, Brave, and more.\n\nSupported Cryptocurrencies\n\nCurrently, Trust Wallet supports over 4.5 million digital assets. However, we all know that there are not that many cryptocurrencies listed. So, where’s the catch? Well, the wallet actually supports around 61 cryptocurrencies, the others being NFTs (Non-Fungible Tokens). Currently, the wallet supports NFTs built on Ethereum and Binance Smart Chain.\n\nStill, the number of digital assets supported by Trust Wallet is quite surprising, and users have many opportunities to store their funds on the platform. Some of the cryptocurrencies supported by Trust Wallet include:\n\nBitcoin (BTC);\n\nEthereum (ETH);\n\nBinance Coin (BNB);\n\nLitecoin (LTC);\n\nRipple (XRP);\n\nDogecoin (DOGE);\n\nSolana (SOL);\n\nPolygon (MATIC);\n\nPolkadot (DOT);\n\nTron (TRX).\n\nMain Features\n\nTrust Wallet allows users to sell, buy, and swap crypto, as well as collect NFTs and interact with dApps (Decentralized Applications) built on Ethereum and Binance Smart Chain. Furthermore, if needed, users can add custom tokens to their wallets.\n\nBesides, Trust Wallet also supports staking for multiple cryptocurrencies, including Binance Coin (BNB), Tezos (XTZ), Tron (TRX), VeChain (VET), Osmosis (OSMO), and Algorand (ALGO). Users can find the estimated earnings for each cryptocurrency on the official website. Also, the platform offers a staking calculator that can predict how much you will earn.\n\nOne of the most intriguing features of Trust Wallet is its dApp browser, a concept not so many crypto wallets provide. The dApp browser allows users to interact with various dApps, such as Aave or Uniswap. Besides, through the dApp browser, users can access NFT marketplaces like OpenSea to purchase and exchange digital collectibles. Unfortunately, the dApp browser is only available on Android devices, as Trust Wallet had to remove it from iOS due to Apple’s App Store requirements.\n\nSecurity Features\n\nAs mentioned, Trust Wallet is a non-custodial storage solution, meaning users have full control over their private keys (also called recovery phrases). So, investors should keep their private keys as safe as possible, as their loss might lead to losing their funds for good.\n\nIf you need to secure your wallet even more, you can enable Touch ID or Face ID for the app. This way, you can ensure that only you can unlock the app. Besides, you can set up an auto-lock time so that even if you use the app and leave the phone unlocked, users cannot access it.\n\nFees\n\nThe good thing about Trust Wallet is that it is free to download and set up. You don’t have to pay a monthly or annual subscription, and storing your funds in this wallet is entirely free.\n\nHowever, the platform charges some fees when buying and selling crypto within the Trust Wallet app. Users who want to purchase crypto on the wallet will have to pay a 1% fee on top of those charged by third-party exchanges.\n\nWhat Is MetaMask?\n\nCurrently operated by ConsenSys and based in San Francisco, MetaMask is a non-custodial multi-coin wallet launched by Aaron Davis in 2016. The wallet allows users to store, purchase, sell, swap, and convert many crypto assets.\n\nJust like Trust Wallet, MetaMask is available as a mobile app for iOS and Android devices and as a web browser extension. However, in MetaMask’s case, first, there was the browser extension, then the mobile app.\n\nMetaMask is ETH-focused, meaning that it supports ETH and any ETH-based token (ERC-20, ERC-721, and many more). So, if it supports ERC-721 tokens, you can store NFTs (Non-Fungible Tokens) on your MetaMask account. While MetaMask used to support NFTs only on MetaMask Mobile, the feature is now available on the web, too. Furthermore, concerning cryptocurrencies, you may be required to import specific tokens to your account manually. However, the process is pretty simple, and we discussed it for TRX and WETH.\n\nHow to Import Trust Wallet to MetaMask – Step-by-Step Guide\n\nImporting Trust Wallet to MetaMask is relatively easy and quick. First, you should have a working MetaMask account. If you don’t have one yet, you can create it by following these steps:\n\nAccess MetaMask on the desktop or download the mobile app;\n\nDownload the MetaMask browser extension (in case you chose to use your computer);\n\nCreate a new MetaMask wallet;\n\nCreate a strong password and confirm it;\n\nSecure your wallet to keep your funds safe;\n\nReveal the recovery phrase and copy it to a safe place that can be accessed only by you;\n\nConfirm the seed phrase by filling the empty spaces with the words required;\n\nYour MetaMask account is all set up.\n\nAfter you set up your MetaMask account, it’s time to connect Trust Wallet to it. To do that, the easiest way is to use MetaMask Mobile.\n\nHow to Import Trust Wallet to MetaMask – Method 1\n\nThe first and simplest way to import Trust Wallet to MetaMask is by using your Secret Recovery Phrase.\n\nStep 1: Install MetaMask\n\nGo to Google Play or Apple Store and download the MetaMask app.\n\nStep 2: Open the MetaMask app and click on “Get started”\n\nOpen the MetaMask app. There, you will see a “Get started” button. Click on that to find the options to create or import wallets.\n\nStep 3: Click on “Import using Secret Recovery Phrase”\n\nYou will see 2 options: “Import using Secret Recovery Phrase” and “Create a new wallet.” Click on the first one to start importing your Trust Wallet.\n\nThen, you will be required to agree to MetaMask’s policy. Read the information thoroughly and click on “I agree.”\n\nAfterward, you can review MetaMask’s latest Terms of Use. After reading them, check the “I agree” section and click “Accept.”\n\nStep 4: Import the Secret Recovery Phrase\n\nThe next step is to add the Secret Recovery Phrase from Trust Wallet.\n\nTo do that, go to the Trust Wallet app.\n\nClick on the “Settings” icon from the lower right corner. Click on “Wallets,” the first option from the settings.\n\nClick on the “Information” icon beside the name of the cryptocurrency wallet you want to import.\n\nThere, you will be able to find the Secret Recovery Phrase. Copy it and go back to MetaMask. You can also write the Secret Recovery Phrase somewhere safe and copy it from there.\n\nStep 5: Paste the Secret Recovery Phrase to MetaMask\n\nWhen you return to MetaMask, you will find a place where your Secret Recovery Phrase is required. Paste the secret words there.\n\nStep 6: Create a password\n\nAfterward, enter a strong password and confirm it. Remember that the password will secure your wallet and, therefore, your funds, so make it as strong as possible.\n\nStep 7: Click on “Import”\n\nAfter you enter the Secret Recovery Phrase and create a password, you will also have the possibility to enable FaceID to enter the wallet. Then, your Trust Wallet is imported to MetaMask.\n\nAfterward, you successfully imported Trust Wallet to MetaMask.\n\nHow to Import Trust Wallet to MetaMask – Method 2\n\nThe 2nd method to import Trust Wallet to your MetaMask account is by using your private key. However, you will not be able to find the private key on Trust Wallet, so you will have to use a tool to convert a mnemonic phrase to a private key.\n\nStep 1: Look for a mnemonic code converter\n\nFirst, you will have to find a trustworthy mnemonic code converter. In this guide, we will use iancoleman.io.\n\nHowever, it’s better to be paranoic when it comes to your secret phrase and private key, so we recommend you access the converter offline. To do that, you can go to GitHub, where you will find a downloadable HTML version.\n\nStep 2: Open the HTML version of the mnemonic converter\n\nAfter you download the .html, open it. On that page, you can enter your Trust Wallet Recovery Phrase.\n\nFind the Trust Wallet Secret Recovery Phrase and type it in the “BIP39 Mnemonic” section. After you finish adding it, the other fields will be automatically updated. However, you will have to select the coin manually.\n\nStep 3: Copy the private key\n\nGo to the “Derived Addresses” section and copy the top private key.\n\nStep 4: Go to MetaMask and import wallet\n\nReturn to MetaMask and click the “Account” button under the MetaMask logo.\n\nClick on “Import account.” There, you can paste the private key you copied from the generator. Then, click on “Import.”\n\nThis way, your Trust Wallet was imported to MetaMask.\n\nFinal Thoughts\n\nTrust Wallet and MetaMask are 2 of the most popular and widely used non-custodial wallets in the crypto industry. If you want to keep your funds safe, you’ve probably considered using them.\n\nIf you want to import your Trust Wallet to MetaMask, there are 2 main methods: by using your Secret Recovery Phrase or by generating a private key.\n\nRegardless of the method you use, always keep in mind that you must protect your secret phrases and private keys as much as possible.\n\nShare this Post Twitter\n\nLinkedin\n\nFacebook\n\nReddit\n\n* The information in this article and the links provided are for general information purposes only and should not constitute any financial or investment advice. We advise you to do your own research or consult a professional before making financial decisions. Please acknowledge that we are not responsible for any loss caused by any information present on this website."}]