[{"id": 0, "url": "https://news.google.com/rss/articles/CBMiMGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3hycC1mbGlwcy1iaW5hbmNlLWJuYtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 14 Jul 2023 07:00:00 GMT", "title": "XRP flips BNB for first time in 2 years after court ruling - Blockworks", "content": "XRP has flipped Binance’s BNB for the first time in over two years, jumping into fourth spot in crypto market cap rankings behind tether (USDT), ether (ETH) and bitcoin (BTC).\n\nBNB was worth double XRP at the beginning of the year — $44 billion to $17.4 billion. Despite coming under repeated fire from regulators globally, Binance’s flagship token had, throughout all of 2022 and 2023, held its ground.\n\nAll that changed when the markets digested a New York judge’s ruling that XRP sales were not securities through secondary markets (crypto exchanges and the like).\n\nBNB now commands a $40.1 billion valuation, 4.7% shy of XRP’s $42 billion, per CoinGecko.\n\nThe judge also ruled institutional sales may well be securities, leaving room for further clarity as the case moves on.\n\nDerivatives markets immediately went into overdrive, swelling open interest for XRP futures by 99%. The figure currently stands at $1.02 billion in outstanding contracts, Coinglass shows.\n\nXRP was last seen trading at $0.79, up more than double from its previous daily closing price of $0.47. The last time XRP’s market cap was higher than BNB’s was in April 2021 (briefly, for one day), after it was flipped for the first time two months earlier.\n\nRegulatory pressure against global crypto exchange Binance has brought BNB considerable sell-side activity. BNB is up about 5% year to date compared with BTC’s 70%. XRP has now gained more than 130% this year.\n\nAll while Ripple has been embroiled in a three-year-long legal battle with the SEC over whether it — along with executives Brad Garlinghouse and Christian Larsen — illegally offered securities in the form of XRP.\n\nThursday’s ruling has shed some light on the situation, with Coinbase and Kraken quickly pledging to relist XRP after dropping it in light of the SEC’s case.\n\nStill, some legal experts have warned the ruling is far from definitive.\n\nUpdated Jul. 14, 2023 at 2:00 am ET: Open interest amounts.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vd3d3LmNvaW50cnVzdC5jb20vbWFya2V0LW5ld3MvYm5iLWJlYWNvbi1jaGFpbi1tYWlubmV0LWludHJvZHVjZXMtbmV3LWZlYXR1cmUtaW4tdXBjb21pbmctaGFyZC1mb3Jr0gFvaHR0cHM6Ly93d3cuY29pbnRydXN0LmNvbS9tYXJrZXQtbmV3cy9ibmItYmVhY29uLWNoYWluLW1haW5uZXQtaW50cm9kdWNlcy1uZXctZmVhdHVyZS1pbi11cGNvbWluZy1oYXJkLWZvcmsvYW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 14 Jul 2023 07:00:00 GMT", "title": "BNB Beacon Chain Mainnet Introduces New Feature in Upcoming Hard Fork - CoinTrust", "content": "The BNB Beacon Chain mainnet, powered by Binance, is set to incorporate a new feature during its upcoming hard fork. This update will enable the blockchain to temporarily pause block production under specific conditions. BNB Chain announced the “Zhangheng” update, expected to occur at block 328,088,888 on July 19th.\n\nThe primary objective of the hard fork is to implement Binance Evolution Proposition BEP-255, which aims to establish an on-chain asset solution. By introducing this proposal, Binance aims to mitigate the potential risks associated with cross-chain bridging exploits, such as the BNB Smart Chain exploit that occurred on October 7, 2022.\n\nThe GitHub post on BEP-255 emphasizes the importance of ensuring the security of assets on the BNB Beacon Chain, especially in light of previous bridge exploits. It mentions the implementation of BEP171 as a measure to improve cross-chain security. Under BEP-255, the balance changes of users will be closely monitored and analyzed in each block to identify any anomalies.\n\nIn the event of reconciliation errors, the blockchain will automatically halt the generation of new blocks as a precautionary measure. Binance acknowledges that this action will impact downstream services like exchange bridging, deposits, and withdrawals. Nevertheless, they assert that such drastic action is necessary to safeguard the integrity of the chain and protect its users.\n\nTo rectify any errors and bring the blockchain back online, a hard fork and solution error resolution process will be initiated. The concerned accounts involved in any exploitation will be blacklisted or corrected. Once the blockchain restarts, downstream services can resume normal functioning.\n\nBinance Implements “Zhangheng” Update to Enhance Security and Scalability\n\nThe upcoming hard fork will also address bug fixes related to preventing fake password attacks, which occur when an unauthorized writer produces a valid composite signature for a transaction. As part of this fix, all existing voting addresses will be deleted at the peak of the hard fork, and validators will need to re-add the polling addresses.\n\nAdditionally, the hard fork aims to enhance the chain’s capability to handle more complex business rules and logic. However, to avoid complications, two-thirds of validators must update their software version to v0.10.16 before the hard fork. Failure to do so will result in non-upgraded full nodes being unable to execute any further blocks after the hard fork block height.\n\nBNB Chain has provided comprehensive instructions for node operators to comply with the hard fork update. However, BNB token holders using Binance.com, centralized exchanges, or offline wallets are not required to take any action at the moment.\n\nAs the blockchain technology landscape continues to evolve, scalability remains a significant challenge. To address this, BNB Chain has deployed OPBNB, a layer 2 scalability solution designed to overcome the limitations of layer 1 (L1) networks. On June 19th, the BNB Chain launched OPBNB, which is a new Ethereum virtual machine-compatible Layer 2 scaling solution based on Optimism’s OP stack."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8venljcnlwdG8uY29tL3NlY3VyaXR5LWFuZC1zdGFraW5nLWFsZ29yYW5kLWN0by1yZXZlYWxzLWZ1dHVyZS1vZi10aGUtcG9wdWxhci1ibG9ja2NoYWluLWFuZC1uZXctZmVhdHVyZXMv0gF1aHR0cHM6Ly96eWNyeXB0by5jb20vc2VjdXJpdHktYW5kLXN0YWtpbmctYWxnb3JhbmQtY3RvLXJldmVhbHMtZnV0dXJlLW9mLXRoZS1wb3B1bGFyLWJsb2NrY2hhaW4tYW5kLW5ldy1mZWF0dXJlcy8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 14 Jul 2023 07:00:00 GMT", "title": "Security and Staking: Algorand CTO Reveals Future of The Popular Blockchain And New Features - ZyCrypto", "content": "Advertisement\n\nAlgorand, a state-of-the-art pure proof of stake network, is introducing innovative features that differentiate it from traditional proof of stake networks like Ethereum or delegated proof of stake networks like Cardano, according to <PERSON>, Chief Technical Officer (CTO) at Algorand. In a recent Twitter Video-call, he explained that Algorand’s pure proof of stake mechanism incentivizes individual participation in consensus and decentralizes the network by disincentivizing pooling.\n\nIn pure proof of stake, an individual is randomly and deterministically selected from the entire set of stakers to validate and propose the next block, creating a more decentralized and secure network. <PERSON> emphasizes the importance of security in blockchains, as the technology’s consensus mechanism is the heart. Algorand’s security model relies on having at least 80% of the declared stake online during block production to prevent network disruptions.\n\nTo ensure decentralization, Algorand encourages a diverse set of stakers geographically and computationally. The concentration of stakes on a single machine increases the risk of network failure, so large stakeholders are advised not to run staking nodes with more than 50 million ALGO.\n\nHe said: “Now for most of us, including myself, 50 million ALGO, is a hell of a lot of cash and we don’t have to worry about that. But for very large companies or international players or venture capitalists or other people who may have very large positions in ALGO, of course they have to be careful that they don’t centralize too much ALGO or centralize over 20% of the stake on a single machine.”\n\nWorking Towards Incentivizing Stakers\n\nTo enhance network security, the Algorand Foundation temporarily brought more of its custodial funds online. Despite the operational burden, this move increased the amount of ALGO staked, representing around 23% of all the Algorand in circulation. However, Woods acknowledges the need to prioritize organic stake from the community and reduce the foundation’s stake to achieve a more decentralized network.\n\nAdvertisement\n\nWoods discusses the plan to incentivize consensus participation in Algorand, similar to how stakers earn rewards in other blockchain networks. The foundation is actively exploring ways to incentivize consensus and working on easy-to-run nodes and potential participant earnings. Woods believes incentivizing consensus will encourage organic stake growth, diversify stakers, and enhance network security.\n\nThe Elephant in the Room: Algofi\n\nIn closing, Woods mentions the retirement of Algofi, a popular venue running on Algorand, expressing sadness but hoping that the funds there will be redistributed to other venues on the chain.\n\nRegarding Algorand’s network security, the CTO emphasizes the importance of growing stakes in the network, making it easier for individuals to participate, and rewarding them for their contributions.\n\nAs Algorand continues to evolve, Woods assures that more information on incentivizing consensus will be shared in the coming weeks, alongside improvements to Algorand’s one-click nodes. The aim is to create a secure and decentralized network that benefits individual stakers and the entire ecosystem.\n\nWith its innovative approach to pure proof of stake and plans for incentivizing consensus, Algorand’s future looks promising as it strives to secure and enhance its popular blockchain network.\n\nHowever, the competition it seems, was also listening in. Cardano founder Charles Hoskinson, known not to shy away from a quip with another blockchain, immediately responded with a tweet, he said: “It might be prudent for Algorand to consider becoming a sidechain of Cardano. Always here to help.”\n\nWhile it is most like meant in jest, commentators were quick to state that Hoskinson is friendly with the team at Algorand. Nonetheless, collaboration rather than working against each other seems the best way to move the industry forward."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiMGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3hycC1mbGlwcy1iaW5hbmNlLWJuYtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 14 Jul 2023 07:00:00 GMT", "title": "XRP flips BNB for first time in 2 years after court ruling - Blockworks", "content": "XRP has flipped Binance’s BNB for the first time in over two years, jumping into fourth spot in crypto market cap rankings behind tether (USDT), ether (ETH) and bitcoin (BTC).\n\nBNB was worth double XRP at the beginning of the year — $44 billion to $17.4 billion. Despite coming under repeated fire from regulators globally, Binance’s flagship token had, throughout all of 2022 and 2023, held its ground.\n\nAll that changed when the markets digested a New York judge’s ruling that XRP sales were not securities through secondary markets (crypto exchanges and the like).\n\nBNB now commands a $40.1 billion valuation, 4.7% shy of XRP’s $42 billion, per CoinGecko.\n\nThe judge also ruled institutional sales may well be securities, leaving room for further clarity as the case moves on.\n\nDerivatives markets immediately went into overdrive, swelling open interest for XRP futures by 99%. The figure currently stands at $1.02 billion in outstanding contracts, Coinglass shows.\n\nXRP was last seen trading at $0.79, up more than double from its previous daily closing price of $0.47. The last time XRP’s market cap was higher than BNB’s was in April 2021 (briefly, for one day), after it was flipped for the first time two months earlier.\n\nRegulatory pressure against global crypto exchange Binance has brought BNB considerable sell-side activity. BNB is up about 5% year to date compared with BTC’s 70%. XRP has now gained more than 130% this year.\n\nAll while Ripple has been embroiled in a three-year-long legal battle with the SEC over whether it — along with executives Brad Garlinghouse and Christian Larsen — illegally offered securities in the form of XRP.\n\nThursday’s ruling has shed some light on the situation, with Coinbase and Kraken quickly pledging to relist XRP after dropping it in light of the SEC’s case.\n\nStill, some legal experts have warned the ruling is far from definitive.\n\nUpdated Jul. 14, 2023 at 2:00 am ET: Open interest amounts.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vd3d3LmNvaW50cnVzdC5jb20vbWFya2V0LW5ld3MvYm5iLWJlYWNvbi1jaGFpbi1tYWlubmV0LWludHJvZHVjZXMtbmV3LWZlYXR1cmUtaW4tdXBjb21pbmctaGFyZC1mb3Jr0gFvaHR0cHM6Ly93d3cuY29pbnRydXN0LmNvbS9tYXJrZXQtbmV3cy9ibmItYmVhY29uLWNoYWluLW1haW5uZXQtaW50cm9kdWNlcy1uZXctZmVhdHVyZS1pbi11cGNvbWluZy1oYXJkLWZvcmsvYW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 14 Jul 2023 07:00:00 GMT", "title": "BNB Beacon Chain Mainnet Introduces New Feature in Upcoming Hard Fork - CoinTrust", "content": "The BNB Beacon Chain mainnet, powered by Binance, is set to incorporate a new feature during its upcoming hard fork. This update will enable the blockchain to temporarily pause block production under specific conditions. BNB Chain announced the “Zhangheng” update, expected to occur at block 328,088,888 on July 19th.\n\nThe primary objective of the hard fork is to implement Binance Evolution Proposition BEP-255, which aims to establish an on-chain asset solution. By introducing this proposal, Binance aims to mitigate the potential risks associated with cross-chain bridging exploits, such as the BNB Smart Chain exploit that occurred on October 7, 2022.\n\nThe GitHub post on BEP-255 emphasizes the importance of ensuring the security of assets on the BNB Beacon Chain, especially in light of previous bridge exploits. It mentions the implementation of BEP171 as a measure to improve cross-chain security. Under BEP-255, the balance changes of users will be closely monitored and analyzed in each block to identify any anomalies.\n\nIn the event of reconciliation errors, the blockchain will automatically halt the generation of new blocks as a precautionary measure. Binance acknowledges that this action will impact downstream services like exchange bridging, deposits, and withdrawals. Nevertheless, they assert that such drastic action is necessary to safeguard the integrity of the chain and protect its users.\n\nTo rectify any errors and bring the blockchain back online, a hard fork and solution error resolution process will be initiated. The concerned accounts involved in any exploitation will be blacklisted or corrected. Once the blockchain restarts, downstream services can resume normal functioning.\n\nBinance Implements “Zhangheng” Update to Enhance Security and Scalability\n\nThe upcoming hard fork will also address bug fixes related to preventing fake password attacks, which occur when an unauthorized writer produces a valid composite signature for a transaction. As part of this fix, all existing voting addresses will be deleted at the peak of the hard fork, and validators will need to re-add the polling addresses.\n\nAdditionally, the hard fork aims to enhance the chain’s capability to handle more complex business rules and logic. However, to avoid complications, two-thirds of validators must update their software version to v0.10.16 before the hard fork. Failure to do so will result in non-upgraded full nodes being unable to execute any further blocks after the hard fork block height.\n\nBNB Chain has provided comprehensive instructions for node operators to comply with the hard fork update. However, BNB token holders using Binance.com, centralized exchanges, or offline wallets are not required to take any action at the moment.\n\nAs the blockchain technology landscape continues to evolve, scalability remains a significant challenge. To address this, BNB Chain has deployed OPBNB, a layer 2 scalability solution designed to overcome the limitations of layer 1 (L1) networks. On June 19th, the BNB Chain launched OPBNB, which is a new Ethereum virtual machine-compatible Layer 2 scaling solution based on Optimism’s OP stack."}]