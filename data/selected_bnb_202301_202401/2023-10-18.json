[{"id": 0, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vdGhlY3J5cHRvYmFzaWMuY29tLzIwMjMvMTAvMTgvYmluYW5jZS1jb2luLWJuYi1pbi1jb25zb2xpZGF0aW9uLW1vZGUtaXMtYS1icmVha291dC1pbW1pbmVudC_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Binance Coin (BNB) in Consolidation Mode: Is a Breakout Imminent? - The Crypto Basic", "content": "Binance Coin (BNB) might be set for a surprising surge if it breaks out from its ongoing consolidation phase.\n\nTop crypto analyst Crypto Rand marked out this consolidation phase in a post on X. According to Crypto Rand, BNB has been consolidating nicely following the breakout of the primary downtrend resistance.\n\nBNB Rebuilding Tempo\n\nBinance Coin has recorded steep slumps for the better part of this year. Despite its 0.38% growth in the past 24 hours, BNB is down 14.20% Year-to-Date (YTD) and more than 35% over the past six months.\n\n- Advertisement -\n\nCrypto Rand maintains an optimistic outlook for BNB despite this bearish historical performance. The chart accompanying his analysis shows three distinct downtrend resistance phases and the ensuing consolidation movements.\n\nFrom August 2022 to June this year, BNB hovered between a low price of $260 and a peak of $340. Marked by the upper blueish bands, BNB oscillated differently and even breached the $260 support zone around January.\n\nAs uncertainties surrounding Binance’s legal status in the United States emerged, BNB nosedived below $260 in June, turning that price point into new resistance.\n\nThis downward move ushered in its second consolidation phase from June to August. Currently, BNB has once again flipped the support at $230, dropping below $210 earlier this month.\n\n- Advertisement -\n\nThe immediate mission is to regain the $230 resistance. Should the optimism from Crypto Rand materialize into actual growth, it can usher in a rally that might return the coin to the $260 range soon.\n\nBNB trades at $212.14, with its market capitalization pegged at $32.18 billion. Crypto Rand confirmed that BNB recently witnessed an impressive increase in volume not recorded months ago.\n\nPotential Collective Revival\n\nOne major challenge of Binance Coin is the impact of the crackdown from the U.S. SEC. Damning as the case appears, BNB is not alone in the fight as the trading of ADA, SOL, FIL and others on secondary exchanges constitutes investment contracts per the SEC.\n\nAs Binance and Coinbase fight the SEC in this regard, a good precedent is set in XRP’s ruling. This presents a long-term bullish outlook if all assumptions turn out as experts predict."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ibmItY2hhaW5zLWdyZWVuZmllbGQtbWFpbm5ldC1kZWJ1dHMtZm9yLWRlY2VudHJhbGl6ZWQtZGF0YS1zdG9yYWdlL9IBYWh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ibmItY2hhaW5zLWdyZWVuZmllbGQtbWFpbm5ldC1kZWJ1dHMtZm9yLWRlY2VudHJhbGl6ZWQtZGF0YS1zdG9yYWdlLz9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "BNB Chain’s Greenfield Mainnet Debuts for Decentralized Data Storage - CryptoPotato", "content": "BNB Chain – the community-driven blockchain ecosystem – announced the public launch of the Greenfield Mainnet.\n\nAccording to the official blog post, Greenfield is a decentralized storage-centric network designed to cater to data-intensive decentralized applications within the BNB ecosystem. This network aims to offer customizable storage solutions that seamlessly integrate with smart contracts on the BNB Smart Chain.\n\nBNB Doubles Down on Decentralized Data Storage\n\nDuring several rounds of stress testing, the uplink bandwidth of Greenfield hit 30M/sec while the downlink bandwidth reached 300M/sec, with each SP able to upload 30 files/sec.\n\nGreenfield is also tasked with offering integrated access control that will allow users to upload data and set specific access permissions before service providers store it off-chain with extensive redundancy and backups.\n\nAdditionally, the blockchain’s cross-chain capabilities enable BNB Chain users to develop their complementary data-related products and services. Users can further capitalize on their data by establishing data marketplaces through Greenfield, where they have the freedom to create, list, trade, and sell their data.\n\nIn the testing phase, the network successfully handled over 200,000 on-chain transactions and connected with 150,000 distinct wallet addresses.\n\nBeyond its primary function of storing substantial data volumes, Greenfield offers a diverse range of applications for users, developers, data managers, business proprietors, and creators, which encompass decentralized hosting, a knowledge economy platform, IP infrastructure for AIGC content, data management solutions, etc.\n\n<PERSON><PERSON><PERSON>, Senior Solution Architect at BNB Chain, highlighted that BNB Greenfield is a “transformative development” in Web3 data ownership and data economy while pointing out its ability to provide users with a decentralized alternative to conventional Cloud services. Bauer added,\n\n“Distinct from centralized offerings, BNB Greenfield empowers users with control over their data, mitigating the risk of breaches and data loss. During the Greenfield Testnet phase and recent BNB Chain Hackathon, it has been encouraging to see so many innovative dApps showcase the potential of decentralized file storage with a user-centric focus.”\n\nFlash Loan Attack\n\nThe latest development comes a week after a flash loan attack on the BNB Chain resulted in the largest single arbitrage profit in its history. As reported by CryptoPotato, the perpetrator used a bot to borrow a large amount of USDT from a lending platform and subsequently manipulated the price of BH on PancakeSwap, a popular decentralized exchange on the BNB Chain.\n\nThe bot executed a swap of USDT for BH at a lower price and then strategically withdrew liquidity from the BH/USDT pair at a higher rate, resulting in a substantial profit. The attack only cost a mere $4.16 in fees, with all profits channeled to the crypto mixing service Tornado Cash."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMihgFodHRwczovL3d3dy5nYWRnZXRzMzYwLmNvbS9jcnlwdG9jdXJyZW5jeS9uZXdzL2JpdGNvaW4tcHJpY2UtdG9kYXktb2N0b2Jlci0xOC11c2QtMjgwMDAtbG9zc2VzLWhpdC1ldGhlci1iaW5hbmNlLWNvaW4tZG9nZWNvaW4tNDQ5MTYwMtIBigFodHRwczovL3d3dy5nYWRnZXRzMzYwLmNvbS9jcnlwdG9jdXJyZW5jeS9uZXdzL2JpdGNvaW4tcHJpY2UtdG9kYXktb2N0b2Jlci0xOC11c2QtMjgwMDAtbG9zc2VzLWhpdC1ldGhlci1iaW5hbmNlLWNvaW4tZG9nZWNvaW4tNDQ5MTYwMi9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Crypto Price Today: Bitcoin Retains Value Above $28,000 Despite Losses Hitting ETH, BNB - Gadgets 360", "content": "Bitcoin on Wednesday, October 18 minted a profit of 0.55 percent. The value of Bitcoin, at the time of writing, stood at $28,433 (roughly Rs. 23.6 lakh). In the last 24 hours, the value of Bitcoin spiked substantially by $258 (roughly Rs. 21,470). Surprisingly, BTC managed to retain its value above the mark of $28,000 (roughly Rs. 23 lakh) even after the fake news about a spot BTC ETF fizzled as quickly as it bubbled. Despite BTC seeing profits, several altcoins including Ether reflected losses.\n\n“BTC has successfully reclaimed the 200-day Simple Moving Average (SMA) and is currently holding above it. However, for BTC to continue its upward momentum, it needs to clear and sustain the crucial resistance level at $28,600 (roughly Rs. 25,02,000),” the CoinDCX team told Gadgets 360.\n\nIn a rather unconventional price movement, Ether did not follow Bitcoin's price trajectory today. Incurring a loss of 1.45 percent, the value of ETH currently stands at $1,566 (roughly Rs. 1.30 lakh).\n\n“ETH is displaying signs of weakness, and it's crucial for ETH to maintain its position above the major support level at $1,530 (roughly Rs. 1.34 lakh) as a drop below this price could lead to further declines,” the CoinDCX team added.\n\nWith small gains, Solana, Bitcoin Cash, Leo, Bitcoin SV, Nem, and Status followed Bitcoin on the price ladder.\n\nApart from these, all other cryptocurrencies are trading in losses today.\n\nThese include Tether, USD Coin, Ripple, Cardano, Dogecoin, Polygon, Tron, and Litecoin.\n\nShiba Inu, Chainlink, Avalanche, Stellar, and Monero also recorded price dips.\n\nThe valuation of the overall crypto market went up just a little by 0.04 percent in the last 24 hours. The market cap of the crypto sector however, remained changed over the last day and continues to stand on the mark of $1.09 trillion (roughly Rs. 90,69,617 crore) as per CoinMarketCap.\n\n“In another altcoin news, the famous layer-1 blockchain, Fantom (FTM, -4.7 percent) is making headlines for its exploit. Two Fantom Foundation wallets seem to have experienced a breach due to the exploit of the multi-chain bridge. The resultant loss is said to have been of around $650k. This incident serves as a good reminder to all crypto traders that even though self-custody does seem like a good approach to hold your funds, any small mistake can lead to a complete loss of funds as well,” Shubham Hudda, Senior Manager, CoinSwitch Markets Desk told Gadgets360.\n\nGaana, JioSaavn, Google Podcasts, Apple Podcasts,\n\nIs the Samsung Galaxy Z Flip 5 the best foldable phone you can buy in India right now? We discuss the company's new clamshell-style foldable handset on the latest episode of Orbital , the Gadgets 360 podcast. Orbital is available on Spotify Amazon Music and wherever you get your podcasts.\n\nCryptocurrency is an unregulated digital currency, not a legal tender and subject to market risks. The information provided in the article is not intended to be and does not constitute financial advice, trading advice or any other advice or recommendation of any sort offered or endorsed by NDTV. NDTV shall not be responsible for any loss arising from any investment based on any perceived recommendation, forecast or any other information contained in the article.\n\nAffiliate links may be automatically generated - see our ethics statement for details."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vY2twZ3RvZGF5LmNhLzIwMjMvMTAvMTgvaG93LXdpbGwtcHJvdmluY2VzLWNyYWNrZG93bi1vbi1haXItYm5iLWltcGFjdC1wcmluY2UtZ2VvcmdlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "How will province's crackdown on Air BNB impact <PERSON>? - CKPGToday.ca", "content": "While this could have big implications for several cities in the province, Managing Broker at Team PowerHouse Realty <PERSON> believe Prince George and other communities in North B.C won’t be impacted too heavily, if at all.\n\n“When there are these kinds of policies coming down provincially or federally, one size fits all generally doesnt work, because B.C is different from the rest of Canada, and Northern B.C is a lot different from the rest of B.C even,” she said.\n\nWith only around 200 Air BNB listings in Prince George, <PERSON> believes even if <PERSON> were to feel the effects of less short-term rentals, it would be minimal. However, unlike the provincial government, <PERSON> believes these short-term rentals serve an important role in our community.\n\n“Short term rentals are often summertime based and are rented in a longer term to students when school is in season as well. So they have students in over the school year and then just rent it out in little blips over the summer when the students are gone,” she said.\n\nTourism Prince <PERSON>’s CEO <PERSON> also believes the impacts of the provincial legislation won’t be massive for <PERSON>, but if <PERSON> does follow through it could provide a boost to our local economy.\n\n“I think this is a pretty great stance for the provincial government to take. What we focus on here at Tourism Prince George is we want to be supporting all of our stakeholders, and hoteliers are a really important part of our industry. So this legislation really protects them and makes sure that it’s more of a level playing field,<PERSON> <PERSON> said.\n\n“Hoteliers are huge employers in Prince George, the amount of staff they have to have, whether that’s just to have the rooms themselves or if they have food and beverage services in the hotels, these types of protections in place for them just makes sure that we can have their employees busy and that they’re consistently working all the time,” he continued.\n\nBoth Phillips and <PERSON> highlighted the fact that <PERSON> George may be exempt from this legislation, should city council choose to move forward that way. <PERSON> says this is because the legislation allows an exemption to municipalities with a vacancy rate of 3 per cent or higher, which <PERSON> currently has.\n\nThis short-term rental legislation is targeted towards the housing crisis felt across the province, but <PERSON> wanted to point out that Prince George isn’t quite in a crisis the same way the rest of the province is. She acknowledged inflation and rising prices have made things more challenging, but Prince George was voted Zolo’s most affordable city in B.C for 2023.\n\n“Our market, we have a historical steady growth rate, we aren’t as volatile as some of the other areas of the province. So looking at it in the past 10-20 years, we’re in a really good place, it’s helping people build equity and a lot of people are using that for their retirement, helping their kids go through school, things like that. So when people are able to get into the housing market it’s a really good investment and it helps them hedge their funds against inflation as well, so once people are in it it really can help serve as a starting point,” Phillips said.\n\nWith the cyclical nature of the housing market, Phillips expects prices could eventually work their way back down, especially if we see an eventual end to inflation."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiPWh0dHBzOi8vY3J5cHRvLm5ld3MvY3J5cHRvLWdyYW50cy10by1sb29rLW91dC1mb3ItaW4tcTQtMjAyMy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Oct 2023 07:00:00 GMT", "title": "Crypto grants to look out for in Q4 2023 - crypto.news", "content": "With VC funding for crypto projects declining, here’s your guide to promising grant opportunities for emerging projects in the web3 space.\n\nEarlier in the year, Fortune Business Insight predicted the worldwide value of blockchain technology would surge to at least $17.57 billion by the end of 2023 and as much as $469 billion by 2030.\n\nThere are currently over 1,000 blockchain networks in operation, and with so much money at stake, it’s no wonder each unique ecosystem is striving diligently to further its growth by creating new use cases catering to people’s everyday needs.\n\nMany of them have adopted community-driven development strategies, including grant programs designed to reward contributors and innovators who have promising new ideas and applications that may help grow the network.\n\nWhat are crypto grants?\n\nCrypto grants are invaluable financial incentives given to individuals, teams, or organizations to encourage the development and advancement of projects within the blockchain and digital currency ecosystem.\n\nThey serve as a form of funding for those who want to build projects in areas such as crypto protocols, blockchains, non-fungible tokens (NFTs), the metaverse, and more in the web3 space.\n\nWith the industry trying to shake off the effects of a prolonged bear market that affected it for most of 2022, leading to a marked decline in venture capital funding for new projects, crypto grants have become a godsend for fledgling innovators.\n\nYou might also like: NFT market witnesses lowest transaction volume in two years\n\nWhat to know about crypto grants\n\nHundreds of crypto grants support developers with anything from small sums to large amounts in the millions of dollars. Some grants specifically target projects and developers with proven track records, while others are open to any crypto enthusiast looking to get into the space.\n\nGrants in the crypto and web3 ecosystems can be categorized into several types based on their purpose:\n\nDevelopment grants : Given to projects that contribute to the development of blockchain platforms.\n\n: Given to projects that contribute to the development of blockchain platforms. Research grants : Awarded to individuals and organizations conducting research that can potentially enhance the blockchain or crypto space.\n\n: Awarded to individuals and organizations conducting research that can potentially enhance the blockchain or crypto space. Infrastructure grants : Targeted at projects aiming to improve or build necessary tools and infrastructure for blockchain platforms.\n\n: Targeted at projects aiming to improve or build necessary tools and infrastructure for blockchain platforms. Community building grants : Intended to foster community engagement and growth.\n\n: Intended to foster community engagement and growth. Education and application grants : Provided to projects that focus on educating the public about blockchain and crypto or developing apps that facilitate the use of these technologies.\n\n: Provided to projects that focus on educating the public about blockchain and crypto or developing apps that facilitate the use of these technologies. Ecosystem grants: Intended to nurture the overall ecosystem of a specific platform.\n\nHowever, it’s important to note that these categories can overlap, and one grant can serve multiple purposes.\n\nCrypto grants can also be categorized by their source of funds. Specific blockchain networks and their treasuries fund some, while others use crowdfunding to source funds distributed to developers.\n\nHere, we’ll look at several grants falling within these two funding practices and how they stimulate new project developments and allow members of the web3 community to contribute to the advancement of the technology.\n\nYou might also like: Global crypto venture funding hits lowest level since 2020\n\nTreasury-funded crypto grants\n\nThese crypto grant programs get their funds from specific blockchain networks or project treasuries.\n\nThey are usually established by the creators of a specific cryptocurrency or blockchain project, and they use a portion of the project’s treasury or reserves to fund development and innovation within their ecosystem.\n\nBelow are a few examples:\n\nBNB Chain Developer Programs\n\nThe BNB Chain Developer Program is an initiative by BNB Chain to support developers and builders within the blockchain ecosystem. The program offers various forms of grants, incentives, and programs to drive innovation and contribute to the long-term utility of the BNB Chain.\n\nOne of the grants available is the Builder Grant, which provides funding to open-source developers, enabling them to make contributions to the BNB Chain’s long-term utility. This grant empowers developers working on open-source projects within the BNB Chain ecosystem.\n\nAnother grant is the Gas Grant, where selected projects can receive up to $15,000 in monthly rewards to accelerate their dapp’s growth. This grant program supports early-stage projects and helps them expand within the BNB Chain ecosystem.\n\nThe BNB Chain grants program also includes the Most Valuable Builder (MVB) Program, which is a regional grant-based program designed for web3 startups globally. Winners of these Innovation Incubators receive support and funding for their projects.\n\nHow to apply\n\nTo apply for the BNB Chain grants program, developers can fill an application on the BNB Chain website within the first seven days of every month. The program is dedicated to investing in innovative projects and teams building on or contributing to the BNB Chain.\n\nEthereum Foundation ESP Grants\n\nAs the Ethereum Foundation’s (EF) public-facing grant distributor, the Ecosystem Support Program (ESP) offers funding and other types of support to projects that contribute to the growth and improvement of Ethereum (ETH).\n\nThe grant program is designed for projects that enhance Ethereum’s foundational elements, including building developer tools, conducting research, fostering community growth, strengthening infrastructure, and promoting open standards.\n\nThe funds act as financial catalysts for developer teams working on innovations to make Ethereum more efficient, secure, and user-friendly. They are awarded every quarter to grantees from various backgrounds, disciplines, and levels of experience stretching across the globe.\n\nPer the ESP, Ethereum network grants are given following a thorough application and review process.\n\nThose interested must keep an eye on the Ethereum Foundation’s blog and social media channels to stay updated about grant opportunities and submit their proposals when ESP announces open rounds.\n\nHow to apply\n\nIdentify your project : Determine if your project aligns with the EF’s mission of supporting the Ethereum ecosystem. Projects that typically receive funding provide solutions for scalability, community building, and other critical areas of Ethereum’s development.\n\n: Determine if your project aligns with the EF’s mission of supporting the Ethereum ecosystem. Projects that typically receive funding provide solutions for scalability, community building, and other critical areas of Ethereum’s development. Choose the appropriate grant program : ESP offers different types of grants. For instance, they have a small grant program capped at $30,000 with a streamlined application process, and a decision is usually delivered two weeks after submission. They also offer project grants for more extensive projects.\n\n: ESP offers different types of grants. For instance, they have a small grant program capped at $30,000 with a streamlined application process, and a decision is usually delivered two weeks after submission. They also offer project grants for more extensive projects. Prepare your application : Once you’ve identified your project and the suitable grant program, prepare your application. While the specific requirements may vary depending on the grant type, you will need to provide details about your project, its goals, and how it contributes to the Ethereum ecosystem.\n\n: Once you’ve identified your project and the suitable grant program, prepare your application. While the specific requirements may vary depending on the grant type, you will need to provide details about your project, its goals, and how it contributes to the Ethereum ecosystem. Submit your application : Applications should be submitted through the appropriate channels. Be sure to check the specific grant program’s page for instructions on how to submit your application.\n\n: Applications should be submitted through the appropriate channels. Be sure to check the specific grant program’s page for instructions on how to submit your application. Await evaluation: After submission, your application will be evaluated. You can expect a decision for the small grants program around two weeks after submission.\n\nAvalanche Multiverse and Blizzard\n\nThe Avalanche Multiverse is an ongoing initiative with a dedicated $290 million budget, currently equivalent to about 4 million of the platform’s native AVAX tokens. The initiative’s primary aim is to expedite the adoption and growth of Avalanche’s “subnet” feature, which is designed to allow an ecosystem of app-specific blockchains with the ability to scale effectively.\n\nTo encourage the growth of subnets, #Avalanche announced a $3 million incentive for the @dexalot #Subnet as part of the #Avalanche Multiverse.\n\n\n\n👉@CoinDesk '' A subnet is a sovereign network that defines its own rules for membership and tokeneomics.''\n\n\n\n✨@dexalot is a #dEX… https://t.co/EJqPuC6Joz pic.twitter.com/gO89aS7GvT — citizenthree.avax 🔺 (@turkcrazy07) August 23, 2023\n\nThe program’s initial focus is to support the development of new ecosystems in several fields, including blockchain-based gaming, decentralized finance (defi) protocols, NFTs, and even institutional use cases.\n\nParallel to the Avalanche Multiverse, the Avalanche Foundation also launched the Blizzard Fund. With a hefty $200 million-plus budget, Blizzard is about accelerating the development, growth, and innovation within the Avalanche public blockchain ecosystem and beyond.\n\nIt is a collaborative effort with contributions from the Avalanche Foundation, Ava Labs, Polychain Capital, Dragonfly Capital, and CMS Holdings, among others.\n\nThe two incentives are opportunities for those working in the development of new ecosystems, especially those focused on web3 decentralized application (dapp) scaling solutions.\n\nHow to apply\n\nInterested parties can get involved by applying through their dedicated platforms.\n\nChainlink Grants Program\n\nThe Chainlink grant program is designed to stimulate the development of essential tools for developers, foster the provision of high-quality data, and support the introduction of essential services within the Chainlink network.\n\nThe developer grants aim to promote the creation of smart contract tools and enable education opportunities for both experienced and budding developers. Ultimately, the program strives to bolster the rapid development of interconnected smart contracts and oracle node networks.\n\nChainlink also has a bug bounty program that backs developers and security engineers who scrutinize its core code, strengthening it against potential future threats.\n\nGrants range from $5,000 to $100,000 and are open to startups and founders in the blockchain, cryptocurrency, NFT, and extended web3 sectors. They are not just limited to technical projects but also include social impact initiatives.\n\nFor example, in 2022, an Estonian startup named Coorest received a social impact grant from Chainlink. In another case, Open Earth, a research and deployment non-profit working to increase global resilience, received a Chainlink grant for developing an integrated assessment model (IAM) oracle.\n\nHow to apply\n\nThose interested can fill out an application form on the grant program’s dedicated page.\n\ndYdX grants\n\ndYdX operates as an autonomous derivatives exchange built on Ethereum, facilitating the trading of perpetual contracts, margin tokens, and spot markets.\n\nIt offers a grant program designed to support the work of developers, researchers, and content creators looking to contribute to the dYdX ecosystem.\n\nThe grants program is back in business! 🦔🚀\n\n\n\nAfter extending the program another six months, we are now ready to accept applications and issue new grants.\n\n\n\nThis extension includes a few exciting changes, find out more below 🧵 — dYdX Ecosystem Development Program (@dydx_grants) September 13, 2023\n\nWith amounts ranging from a minimum of $1,000 to a maximum of $50,000 and application deadlines tailored to specific projects, the dYdX grants provide an interesting opportunity for those keen on researching, innovating, or educating in the defi space.\n\nAccording to information on the dYdX website, the program has had 27 funding rounds since inception, giving 122 grants with an overall value of more than $4 million.\n\nPotential grantees must complete an application form, which the grants lead and committee members will review. dYdX is working on enabling payments to U.S.-based grantees, even as it navigates the country’s regulatory complexities.\n\nIf approved, the grant lead will collaborate with a successful grantee to establish potential milestones. When one is achieved, the developer will receive payment. However, if milestones can’t be determined, the project will receive a portion of the grant upfront to cover costs, typically around 25%, with the balance awarded upon successful completion.\n\nThe grant project considers anything that will positively impact the dYdX product and community, from external trading tools and governance dashboards to translating documentation or third-party integrations. The complexity of the project will be reflected in the funding amount.\n\nHow to apply\n\nThe dYdX grants program has a dedicated page for application submissions. It usually takes up to a week from the initial application to hear from the dYdX team. A faster response may depend on the review requirements and complexity of the application.\n\nTo increase their chances of getting funded, developers must show a strong record of open-source contributions, previous involvement in the dYdX community, and past project examples.\n\nFor technical contributions, a prior history of technological projects is essential. Lastly, the dYdX grants project approves new funding rounds weekly, so there are ample opportunities for participation in the grants program.\n\nYou might also like: Germany’s blockchain funding increased by 3%, report says\n\nSolana Foundation Grants\n\nThe Solana Foundation offers grants for open-source ventures from individuals, autonomous groups, government entities, businesses, and educational institutions.\n\nThe grants are particularly aimed at projects exploring areas like censorship resistance, developer tooling, education, financial inclusion, and academic research among others.\n\nSolana has outlined elements it wishes to see in every grantee:\n\nOpen source : Projects should be developed with the intent to share findings openly.\n\n: Projects should be developed with the intent to share findings openly. Public good : There should be a direct link between a proposal and the value it generates for the wider Solana or global community.\n\n: There should be a direct link between a proposal and the value it generates for the wider Solana or global community. Execution capability : Grantees should demonstrate their team possesses the skills and potential to actualize their proposal.\n\n: Grantees should demonstrate their team possesses the skills and potential to actualize their proposal. Unique contribution : Projects should strive to venture into relatively new areas.\n\n: Projects should strive to venture into relatively new areas. Clear use of funds: Grantees should be careful in determining the amount they are requesting and provide a clear plan for how the funds will be used to achieve their objectives.\n\nAside from the Solana Foundation, there are several other funding sources available within the Solana ecosystem, including Metaplex, Solana Ventures, hackathons, and ecosystem funds, each with their own objectives and requirements.\n\nHow to apply\n\nAn application form can be found on the Solana platform. The Foundation advises grant applicants to ensure their application highlights how their project will enhance the public good for the Solana network, such as by promoting decentralization, open-sourcing code, or contributing to the network’s security.\n\nWith Solana grants being milestone-dependent, it is also recommended that applicants include suggested milestones in their proposal.\n\nThe grants team regularly evaluates applications, and if needed, ask for more information or schedule a meeting with potential grantees.\n\nGiven the large volume of applications received, the team usually strives to process them as rapidly as possible, with the aim of contacting applicants within a two-week timeframe.\n\nCrowdfunded crypto grants\n\nThese grant programs get their funds from crowdfunding activities. They rely on the collective effort of individuals who pool their resources to support initiatives. This method is often used to democratize funding and to support projects that might not have access to treasury-funded grants or other traditional forms of financing. Additionally, they allow members of the public to participate in the development of web3 by supporting their favorite projects.\n\nSome crowdfunded crypto grants actually use a “mixed” funding system, where funds raised by the public are matched by money from established project treasuries or venture capitalists in the crypto space.\n\nBelow are a few examples of crowdfunded crypto grants:\n\nGitcoin Grants Program\n\nThe Gitcoin Grants Program is an initiative of Gitcoin DAO (decentralized autonomous organization), and it conducts a quarterly event that invites web3 enthusiasts to steer their funding toward projects they deem necessary.\n\nWHAT THE GITCOIN TEAM GOT UP TO IN SEPTEMBER 2023 (AND WHAT’S AHEAD) 🔥\n\n\n\ngm supporters, builders and champions of public goods\n\n\n\nWe’re back with our monthly update for all things 📰@Gitcoin: Gitcoin Grants, @GrantsStack, @GitcoinPassport, and @AlloProtocol 🧵 pic.twitter.com/lbDxVhcaZj — Gitcoin (@gitcoin) September 29, 2023\n\nThe program uses a unique distribution method known as quadratic funding (QF), a concept introduced by Vitalik Buterin, Zoe Hitzig, and Glen Weyl in 2018. QF ensures a more democratic allocation of funds as it takes into account not only the sum of money a project receives but also the number of individual contributors.\n\nDuring quadratic funding rounds, members of the Gitcoin community contribute to projects they believe in, while matching partners offer additional funds. However, the matching funds are not dollar-for-dollar but are more in line with community sentiment, emphasizing the number of contributors rather than the funds raised.\n\nThe program started in 2017 with a mission to incentivize funding for open-source developers but eventually evolved towards building and maintaining digital public goods.\n\nIt has several rounds each year, with the latest being GG18, held from Aug. 15 to Aug. 29, where $1 million in matching funds were made available.\n\nIn addition to the primary grants program, Gitcoin also provides a “Grants Stack” service, which allows crypto communities to start and manage their own grants program. They allow people to financially support open-source projects they believe in, contributing to their ongoing development and maintenance.\n\nFurthermore, by enabling matching funds, Gitcoin boosts the total funding for a given project.\n\nHow to apply\n\nIf you’re interested in applying, you can create a grant on the Gitcoin platform and participate in their quarterly matching rounds.\n\nCreate a project profile : This is done on the Gitcoin platform, where you can showcase your project and its objectives.\n\n: This is done on the Gitcoin platform, where you can showcase your project and its objectives. Apply to the grant round : On the Gitcoin platform, there will be an option to “Apply to the Grant Round.” This will open up an application form where you can detail your project and how it aligns with the grant’s objectives.\n\n: On the Gitcoin platform, there will be an option to “Apply to the Grant Round.” This will open up an application form where you can detail your project and how it aligns with the grant’s objectives. Use the explorer : The Gitcoin Explorer allows you to browse, discover, and apply to different grant programs. You can also use it to support projects within a grant round.\n\n: The Gitcoin Explorer allows you to browse, discover, and apply to different grant programs. You can also use it to support projects within a grant round. Manage your application: After applying, you can track the status of your application, respond to any queries, and provide updates.\n\nOpen Grants program\n\nThe Open Grants project on Ethereum is a cooperative initiative designed to financially support open-source projects that contribute positively to the Ethereum ecosystem.\n\nIt is a combined effort of the Ethereum Foundation and the online community, focusing on backing developers who are working on projects within their mission and scope. Simply put, it’s a smart contract on Ethereum that automatically forwards its received funds to a specified list of recipients. This contract identifies the recipients, determines the allocation amounts, and determines the currency type, even though it currently only supports ETH.\n\nTo fund a grant, one clicks the “fund” button on their chosen grant, which will offer them the choice to either set up a stream or pay in full immediately. Streaming is a straightforward vesting contract that allows funds to accrue over time.\n\nIf a contributor opts for streaming, they’ll need to state the total amount they wish to stream and the duration. The funds will then be vested gradually over time and can only be released by invoking the “withdraw” function on the contract.\n\nA stream creator can halt the stream at any point and retrieve the unvested part of their funds if they decide not to fund the grant further. They also have the freedom to cancel their streams whenever they wish. In such cases, the vested funds will be transferred to the recipients, and any unvested funds will be reverted to the stream creator’s address.\n\nThese grants are created to support anyone contributing to the Ethereum ecosystem, such as open-source developers, designers, researchers, and writers.\n\nGrantees can receive funds through one-time donations or extended streams. They can check the balance of any grant they’ve received on the “My Grants” page. The vested funds can be distributed to grant recipients by anyone, either by clicking the “distribute” button or by activating the withdraw function on a stream.\n\nHow to apply\n\nTo create a grant, potential recipients must log into Open Grants, open the menu, and click “Create Grant.” They’ll then have to fill out the form, remembering that the details cannot be altered once a grant is established.\n\nFor their project description, they must provide a high-level summary of the project, specifically mentioning how the grant will assist the Ethereum ecosystem. They also need to link it to a webpage, blog post, or document detailing the project’s scope, goals, timeline, and funding objectives. When ready, they’ll click “Create Grant,” initiating a transaction to complete the process.\n\nClear Fund (Clr.fund)\n\nClr.fund is a protocol that aims to efficiently allocate funds to public goods that benefit the Ethereum Network based on the preferences of the Ethereum Community.\n\n🦸A new QF funding round has started in @Giveth and @clrfund is part of it!\n\n\n\n🔴 The round is on @optimismFND and @Giveth 's platform makes us really discoverable\n\n\n\nWe would appreciate your help so ecosystems and communities can run their own QF!https://t.co/IMbuVySMyc pic.twitter.com/wCDcMUIzxD — clr.fund (@clrfund) October 10, 2023\n\nThe process is both a fundraising mechanism and a kind of public vote. When a community member makes a donation to a project, it also counts as a vote for the project. The more contributions a project gets, the more votes it accumulates, which influences the funding it receives from a matching pool.\n\nClr. fund’s funding process isn’t an open-ended affair. It has a specific timeframe, divided into phases. Projects are first invited to participate in the round, then the contribution phase follows, where community members can browse projects and make their donations. But potential grantees can’t procrastinate—there’s a cut-off after a day, beyond which newcomers can’t join the round.\n\nNext up is the reallocation phase, which allows contributors to revise their decisions—adjust donation amounts, add or remove projects—though the total donation can’t exceed the original amount.\n\nFollowing this, it’s time for tallying, when all votes are counted, and each project’s share of the matching pool is calculated. Finally, the round concludes, and project owners can claim funding.\n\nThe process employs tech tools like MACI (to protect against bribery and count round results) and BrightID (to protect against Sybil attacks). This is done to ensure fairness and protect against nefarious activities.\n\nAs a contributor, you’ll need to prepare by setting up some aETH on Arbitrum to submit transactions to the clr.fund smart contracts. You’ll also need to verify your human uniqueness to help fend off Sybil attacks. Remember, you can only contribute once, but you can reallocate your contribution during a particular phase.\n\nContributing to the matching pool is a good option if you’re unsure which projects to support. Any contributions to the pool will be distributed to all projects based on the results of the quadratic funding calculations.\n\nHow to apply\n\nIf you’re a project owner, you’ll need to register your project, which involves an application process and a review by the registry administrator.\n\nOnce approved, your project can participate in the funding round. Upon the conclusion of the round, claim your project’s share of funding with a simple click of a button.\n\nMolochDAO\n\nMoloch is an open-source DAO framework that focuses on funding Ethereum projects. Ameen Soleimani, the CEO of SpankChain, launched it in February 2019. “Moloch” refers to a Canaanite god associated with child sacrifice, symbolizing the sacrifices and coordination needed to build public goods.\n\nThe platform has a “ragequit” feature, which allows members to leave the DAO and exchange their shares for a portion of the treasury’s assets. It offers an optimized treasury, a variety of new proposal types, and the ability to hold different ERC20 tokens. Furthermore, anyone can propose to the DAO, even non-shareholders.\n\nTo become a part of MolochDAO, one can pledge 10-100 wETH, share a proposal and request sponsorship, earn membership via a grant, or contribute work to the DAO.\n\nUpon becoming a member, individuals can submit proposals, participate in soft polls, and vote on-chain. The Moloch DAO v2 smart contract standard is designed to facilitate governance goals. Membership admission and continuation are permissioned processes, reflecting the choices of MolochDAO’s membership. Any member can propose the expulsion of another member, and if approved, the expelled member loses their shares while receiving payment tokens representing their percentage of MolochDAO’s assets.\n\nMolochDAO funds projects and researchers dedicated to the advancement of the Ethereum blockchain. They allocate over $1 million annually to projects aligning with their ethos.\n\nHow to apply\n\nPosting your idea on forums for feedback, potential video calls, and ultimately submitting a proposal on-chain through the DAOHaus platform are all steps in the funding process.\n\nFAQs"}]