[{"id": 4, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZW4uY3J5cHRvbm9taXN0LmNoLzIwMjMvMDYvMjkvcGVwZS10b2tlbi13aWxsLWxhdW5jaGVkLWJuYi1jaGFpbi_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 29 Jun 2023 07:00:00 GMT", "title": "PEPE token will be launched on the BNB Chain - The Cryptonomist", "content": "PRESS RELEASE*\n\nA new PEPE memecoin is launching, following hot on the heels of its Ethereum namesake. Issued as a BEP20 on BNB Chain, PEPE is a novel memecoin with a number of unique mechanisms programmed into its tokenomics.\n\nLike the original PEPE token, which has proven a runaway success in 2023, the BEP20 PEPE draws its cues from the mischievous frog of the same name, whose design was derived from the webcomic “Boy’s Club.”\n\nThe market capitalization of the original PEPE token, which debuted on Ethereum in April, exceeded $1 billion on May 5th, prompting a new wave of memecoins, including a number of frog-themed efforts. Unlike subsequent memecoins, the PEPE (BEP20) has several unusual features programmed in including a syndication system designed to support price stabilization.\n\nThe team behind the BNB Chain PEPE aren’t settling for just one memecoin either: they plan to launch multiple PEPE projects, all based on the loveable frog. 13 such launches have been cued up for 2023 alone.\n\nAlthough each PEPE project will operate independently, the introduction of a “Burn & Drop” system will govern the consortium. Burn & Drop is a first-of-its-kind system in which burning one token leads to the airdrop of another.\n\nTo initiate a Burn & Drop, two types of tokens are required. For example, this could involve a scenario in which Token A is priced at $1 and PEPE (BEP20) is also priced at $1. However, if the price of Token A were to drop to $0.8, Token A holders would have the option to burn their tokens on the Burn & Drop platform, automatically receiving PEPE (BEP20) through an airdrop, and with Token A having been burned, its total supply would decrease.\n\nBy initiating the Burn & Drop process between tokens, excess volatility can be reduced while possibly providing trading opportunities for arbitrage and intra-token trading. This logic is a fundamental component of PEPE (BEP20), and one which users can engage with directly within the platform.\n\nIn addition, PEPE (BEP20) has plans to introduce numerous services that will further extend the token’s utility. To further increase interest in the project, PEPE (BEP20) is currently running an airdrop campaign on Twitter that’s open to everyone.\n\nAbout PEPEBEP20\n\nPEPE is a project launched on the Binance Smart Chain, taking the form of a BEP20 token. The Pepe Syndicate and the unique “Burn&Drop” mechanism of Pepe are designed to manage the token’s price volatility. This is not a guarantee of price stability, but a mechanism to potentially mitigate price fluctuations. Furthermore, various projects that participate in the “Burn&Drop” system collaborate with each other, fostering a robust Pepe Syndicate network.\n\nFor more information regarding the campaign, please visit\n\nPEPE BEP20’s Twitter (@pepecoinbep20),\n\nJoin PEPE BEP20’s Telegram,\n\nWebsite: https://pepe.markets/"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiamh0dHBzOi8vd3d3LnR1a28uY28ua2Uvd29ybGQvNTEyMTAxLWFpcmJuYi1vd25lci1zaWNrLXRoZWZ0LWdsdWVzLW9ybmFtZW50cy1zdXJmYWNlcy1zdG9wLWd1ZXN0cy1zdGVhbGluZy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 29 Jun 2023 07:00:00 GMT", "title": "Airbnb Owner Sick of Theft Glues Her Ornaments to Surfaces to Stop Guests From Stealing - Tuko.co.ke - Tuko.co.ke", "content": "One woman who makes money by running a guest house on Airbnb showed how far she has to go to prevent stealing\n\nThe video of TikTok was a hit as people had much to say after seeing how she made sure none of her decors could be moved\n\nMzansi online users thought it was interesting to see the tactic she chose as the best way to stop those with sticky fingers\n\nOne lady who runs an Airbnb shared a business challenge. The businesswoman proved that she was willing to do anything to keep her decor in the guesthouse.\n\nA TikTok video by an Airbnb owner shows how she prevented any more theft in her guest house. Image: @ladique\n\nSource: UGC\n\nA video of her guesthouse hack got over 20 000 likes. People thought her idea to prevent stealing was hilarious.\n\nWoman's TikTok video about running Airbnb gets 300 000 views\n\n@ladique showed people that she had to resort to glueing down her decor to stop guests from stealing. In a video, the woman pulled on a candle holder to show it was permanently stuck to the table. Watch the funny bit below:\n\nPAY ATTENTION: Сheck out news that is picked exactly for YOU ➡️ click on “Recommended for you” and enjoy!\n\nNetizens in tears over Airbnb owner's struggle shown in TikTok video\n\nPeople are always fascinated to see how people deal with challenges. Online users thought it was funny to see her solution after losing things to stealing guests.\n\n<PERSON> wrote:\n\n\"Don't forget to glue the chairs and tables.\"\n\nSimp<PERSON>_<PERSON><PERSON><PERSON> commented:\n\n\"When they go low, I go to the pits of hell.\"\n\nOf<PERSON><PERSON> <PERSON> said:\n\n\"Please request the building manager to change the door locks for you as well bc you never know.\"\n\nthandi bruh added:\n\n\"Then they take the carpets.\"\n\nAkho Raas Tshweza asked:\n\n\"And how do you clean kengoku?\"\n\n_NaNkosi joked:\n\n\"When they go low I go lower- LOL\"\n\nNairobi man dupes young woman\n\nElsewhere, <PERSON> Nouah, an Airbnb owner in Nairobi, shared an unfortunate incident in which a cheeky man used the rented apartment to dupe a young woman.\n\nIn a post on Facebook christened Airbnb Chronicles Part 1, Joan disclosed that it all began when a man named Otis booked a two-bedroom house at Suba Comforts.\n\nHe then invited Njeri, a single mother of one in her very early 20s, to spend time with him at the house.\n\nTo make his facade look real, Otis dropped a suitcase full of his clothes in the master bedroom before picking up the girl and driving around the city with her in a Mercedes Benz.\n\nThe manner in which he spoke about the house convinced Njeri that the house belonged to Otis, and that he uses it whenever he's in the country.\n\n\"It's Sunday and Otis should be flying out, he leaves very early in the morning, tells Njeri to take good care of the house till he gets back, rent and cleaning services sorted,\" Joan wrote.\n\nPAY ATTENTION: Click “See First” under the “Following” tab to see TUKO News on your News Feed\n\nSource: Briefly.co.za"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vd3d3LnRoZS1zdW4uY29tL2VudGVydGFpbm1lbnQvODQ4NjI4Ni9pbnNpZGUtc2lzdGVyLXdpdmVzLW1lcmktYnJvd24tdXRhaC1iZWQtYnJlYWtmYXN0LWlubi_SAWhodHRwczovL3d3dy50aGUtc3VuLmNvbS9lbnRlcnRhaW5tZW50Lzg0ODYyODYvaW5zaWRlLXNpc3Rlci13aXZlcy1tZXJpLWJyb3duLXV0YWgtYmVkLWJyZWFrZmFzdC1pbm4vYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 29 Jun 2023 07:00:00 GMT", "title": "Inside Sister Wives star <PERSON><PERSON>’s bed and breakfast Lizzie’s Heritage Inn featuring four bedrooms and K... - The US Sun", "content": "MERI'S ESCAPE Inside Sister Wives star <PERSON><PERSON>’s bed and breakfast Lizzie’s Heritage Inn featuring four bedrooms and <PERSON><PERSON> keepsake\n\nSISTER Wives star <PERSON><PERSON>’s Utah bed and breakfast, <PERSON>’s Heritage Inn, features four bedrooms and a keepsake of her ex <PERSON><PERSON>, The U.S. Sun has exclusively learned.\n\nThe reality star, 52, moved into her bed and breakfast in April with help from <PERSON><PERSON>, The U.S. Sun reported at the time.\n\n13 <PERSON><PERSON>'s Lizzie's Heritage Inn is located in Parowan, Utah Credit: The US Sun\n\n13 The property that was built in 1870 has a cozy living room for guests to relax Credit: The US Sun\n\n13 Fresh breakfast is served each morning in the kitchen by a private chef Credit: The US Sun\n\nNow, new pics from inside <PERSON>'s Heritage Inn, exclusively obtained by The U.S. Sun, show never-before-seen treasures and a glimpse into the life of <PERSON><PERSON>.\n\nThe inn, located in the small town of Parowan, Utah, has four bedrooms that can be reserved at a rate of $250 or $300 per night plus tax.\n\nThe four rooms are called the Bee Room, the East Room, the Grandma Room, and <PERSON>'s Room.\n\n<PERSON>'s Room is named after <PERSON>, the founding mother who built the inn in 1870 along with her husband <PERSON>, both of whom are <PERSON><PERSON>'s great great grandparents.\n\nEach room has a king or queen size bed and comes complete with a private bathroom, cable TV, and a welcome tray with water bottles, cookies, and pamphlets on the town of Parowan.\n\nFor Sister Wives fans, the best features of each room are the keepsakes from <PERSON><PERSON>'s family passed on from generation to generation.\n\nThese include portraits of <PERSON>ri's loved ones and family members who had passed, dresses that were worn by the TLC star's relatives and old-time knickknacks placed inside each room.\n\nOther interesting finds throughout the home include a pillow in the living room with a photo of Meri and her mom <PERSON>, an old piano, and a sign outside that has \"The Final Five\" couples - one of which points to the right and says, \"Meri & <PERSON>dy 309 mi.\"\n\nThere is also the \"Wall of The Dead\" where photos are hung of <PERSON> (Meri's mom who died in March 2021), Joyce (Meri's grandma), Lizzie (Meri's great grandma), and Sarah.\n\n13 Lizzie's has four bedrooms that are available to reserve for $250-$300 per night Credit: The US Sun\n\n13 Each room has a welcome tray, private bathroom, and cable TV Credit: The US Sun\n\n13 The rooms also have old-time knickknacks and hidden treasures Credit: The US Sun\n\n13 The four bedrooms are named the Bee Room, the East Room, the Grandma Room, and Sarah's Room Credit: The US Sun\n\nTOURIST ATTRACTION\n\nLizzie's Heritage Inn has turned into a tourist attraction.\n\nThe U.S. Sun has learned that fans who aren't guests sometimes try entering the home to see their favorite Sister Wives star.\n\nBecause of this, the house is secured with outdoor cameras and extra locks around the home.\n\nFans who are guests of the inn can enjoy the hospitality of the historic home that is surrounded by views of Utah's Valentine Peak and Brian Head Resort.\n\nThere is a small gift shop where guests can purchase Lizzie's Heritage Inn merchandise, such as kitchen towels, aprons, coffee mugs, drink coasters, and baseball caps.\n\nEach morning, guests can enjoy the comfort of the welcoming kitchen with breakfast made by a private chef.\n\nPlus, complimentary coffee and tea is available throughout the day.\n\nUpon checking out, guests can leave a review about their stay by signing the guest book.\n\n13 A pillow of Meri with her late mom Bonnie can be found inside the inn Credit: The US Sun\n\n13 Dresses worn by Meri's relatives are hung inside one of the room's closets Credit: The US Sun\n\n13 A 'Meri & Kody' keepsake is found outside the inn despite the couple splitting Credit: The US Sun\n\nMERI'S 'RARELY' THERE\n\nGuests of the inn are greeted by Meri's best friend Jenn Sullivan, who according to a source is \"tight-lipped about her pal's whereabouts.\"\n\nFans of the reality show might be disappointed to learn that the California native might not be at the inn upon arriving.\n\nThe U.S. Sun previously reported Meri rarely spends time at the property.\n\nOne insider told The U.S. Sun: \"Meri isn't at Lizzie's Heritage Inn as much as people think.\n\n\"She travels a lot, she's always traveling.\"\n\nThe source went on to question whether the TV personality tricks her followers into thinking that she's at the inn by sharing pics of the property or footage making it appear like she is there, but in reality, she's not.\n\nAnother insider revealed: \"Meri hasn't been at the inn in quite some time. It's hard to believe she's permanently moved in.\"\n\nOne other local insider revealed that the TV personality is not so known around the town of Parowan because she's not a frequent visitor at some of the main spots downtown.\n\nNEXT CHAPTER\n\nThose who follow Meri on social media might have picked up on the fact that she normally hangs out with her friends and rarely hangs out with the other sister wives - Christine, Robyn, or Janelle.\n\nFour months after announcing her split from the family's patriarch, the reality star moved from Arizona to Utah with help from Kody.\n\nAt the end of April, Meri and Kody were spotted by fans at a gas stop picking up food along the way to Utah.\n\nThe duo was driving a large moving truck, per a Reddit post, and then seen at Meri's Lizzie's Heritage Inn moving furniture and a piano.\n\nAccording to another Reddit post, a camera crew filmed the entire move.\n\nA source told The U.S. Sun: \"She's done having anything to do with Arizona.\n\n\"She's moving on. Kody has been helpful with her move and supportive of her decision.\"\n\nAround the same time, The U.S. Sun had reported that her four-bedroom, four-bath home that she was renting in Flagstaff for nearly four years was put up for rent.\n\nLocated close to the downtown area, the 4,000 square-foot house - that costs over $1million - could be rented for $4,500 per month.\n\nIt is unclear what Meri's relationship is like with Kody today.\n\nHowever, for Father's Day this month, she snubbed her ex by only paying tribute to her late father, Bill Barber, despite sharing one child, 27-year-old Leon Brown, with Kody.\n\n13 The 'Wall of the Dead' showcases Meri's mom and other relatives who have passed Credit: The US Sun\n\n13 A gift shop inside Lizzie's Heritage Inn has merchandise available for purchase Credit: The US Sun"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vd3d3LnRoZS1zdW4uY29tL2VudGVydGFpbm1lbnQvODQ4NjI4Ni9pbnNpZGUtc2lzdGVyLXdpdmVzLW1lcmktYnJvd24tdXRhaC1iZWQtYnJlYWtmYXN0LWlubi_SAWhodHRwczovL3d3dy50aGUtc3VuLmNvbS9lbnRlcnRhaW5tZW50Lzg0ODYyODYvaW5zaWRlLXNpc3Rlci13aXZlcy1tZXJpLWJyb3duLXV0YWgtYmVkLWJyZWFrZmFzdC1pbm4vYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 29 Jun 2023 07:00:00 GMT", "title": "Inside Sister Wives star <PERSON><PERSON>’s bed and breakfast Lizzie’s Heritage Inn featuring four bedrooms and K... - The US Sun", "content": "MERI'S ESCAPE Inside Sister Wives star <PERSON><PERSON>’s bed and breakfast Lizzie’s Heritage Inn featuring four bedrooms and <PERSON><PERSON> keepsake\n\nSISTER Wives star <PERSON><PERSON>’s Utah bed and breakfast, <PERSON>’s Heritage Inn, features four bedrooms and a keepsake of her ex <PERSON><PERSON>, The U.S. Sun has exclusively learned.\n\nThe reality star, 52, moved into her bed and breakfast in April with help from <PERSON><PERSON>, The U.S. Sun reported at the time.\n\n13 <PERSON><PERSON>'s Lizzie's Heritage Inn is located in Parowan, Utah Credit: The US Sun\n\n13 The property that was built in 1870 has a cozy living room for guests to relax Credit: The US Sun\n\n13 Fresh breakfast is served each morning in the kitchen by a private chef Credit: The US Sun\n\nNow, new pics from inside <PERSON>'s Heritage Inn, exclusively obtained by The U.S. Sun, show never-before-seen treasures and a glimpse into the life of <PERSON><PERSON>.\n\nThe inn, located in the small town of Parowan, Utah, has four bedrooms that can be reserved at a rate of $250 or $300 per night plus tax.\n\nThe four rooms are called the Bee Room, the East Room, the Grandma Room, and <PERSON>'s Room.\n\n<PERSON>'s Room is named after <PERSON>, the founding mother who built the inn in 1870 along with her husband <PERSON>, both of whom are <PERSON><PERSON>'s great great grandparents.\n\nEach room has a king or queen size bed and comes complete with a private bathroom, cable TV, and a welcome tray with water bottles, cookies, and pamphlets on the town of Parowan.\n\nFor Sister Wives fans, the best features of each room are the keepsakes from <PERSON><PERSON>'s family passed on from generation to generation.\n\nThese include portraits of <PERSON>ri's loved ones and family members who had passed, dresses that were worn by the TLC star's relatives and old-time knickknacks placed inside each room.\n\nOther interesting finds throughout the home include a pillow in the living room with a photo of Meri and her mom <PERSON>, an old piano, and a sign outside that has \"The Final Five\" couples - one of which points to the right and says, \"Meri & <PERSON>dy 309 mi.\"\n\nThere is also the \"Wall of The Dead\" where photos are hung of <PERSON> (Meri's mom who died in March 2021), Joyce (Meri's grandma), Lizzie (Meri's great grandma), and Sarah.\n\n13 Lizzie's has four bedrooms that are available to reserve for $250-$300 per night Credit: The US Sun\n\n13 Each room has a welcome tray, private bathroom, and cable TV Credit: The US Sun\n\n13 The rooms also have old-time knickknacks and hidden treasures Credit: The US Sun\n\n13 The four bedrooms are named the Bee Room, the East Room, the Grandma Room, and Sarah's Room Credit: The US Sun\n\nTOURIST ATTRACTION\n\nLizzie's Heritage Inn has turned into a tourist attraction.\n\nThe U.S. Sun has learned that fans who aren't guests sometimes try entering the home to see their favorite Sister Wives star.\n\nBecause of this, the house is secured with outdoor cameras and extra locks around the home.\n\nFans who are guests of the inn can enjoy the hospitality of the historic home that is surrounded by views of Utah's Valentine Peak and Brian Head Resort.\n\nThere is a small gift shop where guests can purchase Lizzie's Heritage Inn merchandise, such as kitchen towels, aprons, coffee mugs, drink coasters, and baseball caps.\n\nEach morning, guests can enjoy the comfort of the welcoming kitchen with breakfast made by a private chef.\n\nPlus, complimentary coffee and tea is available throughout the day.\n\nUpon checking out, guests can leave a review about their stay by signing the guest book.\n\n13 A pillow of Meri with her late mom Bonnie can be found inside the inn Credit: The US Sun\n\n13 Dresses worn by Meri's relatives are hung inside one of the room's closets Credit: The US Sun\n\n13 A 'Meri & Kody' keepsake is found outside the inn despite the couple splitting Credit: The US Sun\n\nMERI'S 'RARELY' THERE\n\nGuests of the inn are greeted by Meri's best friend Jenn Sullivan, who according to a source is \"tight-lipped about her pal's whereabouts.\"\n\nFans of the reality show might be disappointed to learn that the California native might not be at the inn upon arriving.\n\nThe U.S. Sun previously reported Meri rarely spends time at the property.\n\nOne insider told The U.S. Sun: \"Meri isn't at Lizzie's Heritage Inn as much as people think.\n\n\"She travels a lot, she's always traveling.\"\n\nThe source went on to question whether the TV personality tricks her followers into thinking that she's at the inn by sharing pics of the property or footage making it appear like she is there, but in reality, she's not.\n\nAnother insider revealed: \"Meri hasn't been at the inn in quite some time. It's hard to believe she's permanently moved in.\"\n\nOne other local insider revealed that the TV personality is not so known around the town of Parowan because she's not a frequent visitor at some of the main spots downtown.\n\nNEXT CHAPTER\n\nThose who follow Meri on social media might have picked up on the fact that she normally hangs out with her friends and rarely hangs out with the other sister wives - Christine, Robyn, or Janelle.\n\nFour months after announcing her split from the family's patriarch, the reality star moved from Arizona to Utah with help from Kody.\n\nAt the end of April, Meri and Kody were spotted by fans at a gas stop picking up food along the way to Utah.\n\nThe duo was driving a large moving truck, per a Reddit post, and then seen at Meri's Lizzie's Heritage Inn moving furniture and a piano.\n\nAccording to another Reddit post, a camera crew filmed the entire move.\n\nA source told The U.S. Sun: \"She's done having anything to do with Arizona.\n\n\"She's moving on. Kody has been helpful with her move and supportive of her decision.\"\n\nAround the same time, The U.S. Sun had reported that her four-bedroom, four-bath home that she was renting in Flagstaff for nearly four years was put up for rent.\n\nLocated close to the downtown area, the 4,000 square-foot house - that costs over $1million - could be rented for $4,500 per month.\n\nIt is unclear what Meri's relationship is like with Kody today.\n\nHowever, for Father's Day this month, she snubbed her ex by only paying tribute to her late father, Bill Barber, despite sharing one child, 27-year-old Leon Brown, with Kody.\n\n13 The 'Wall of the Dead' showcases Meri's mom and other relatives who have passed Credit: The US Sun\n\n13 A gift shop inside Lizzie's Heritage Inn has merchandise available for purchase Credit: The US Sun"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMic2h0dHBzOi8vYmxvY2ttYW5pdHkuY29tL2d1aWRlL3dpa2kvb3BibmItZXhwbGFpbmVkLXdoYXQtaXMtb3BibmItdGhlLW5ldy1sYXllci0yLWJ5LWJpbmFuY2UtYW5kLXdoeS1kb2VzLWl0LW1hdHRlci_SAXdodHRwczovL2Jsb2NrbWFuaXR5LmNvbS9ndWlkZS93aWtpL29wYm5iLWV4cGxhaW5lZC13aGF0LWlzLW9wYm5iLXRoZS1uZXctbGF5ZXItMi1ieS1iaW5hbmNlLWFuZC13aHktZG9lcy1pdC1tYXR0ZXIvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 29 Jun 2023 07:00:00 GMT", "title": "opBNB Explained: What is opBNB – The New Layer-2 By Binance And Why Does It Matter? - Blockmanity", "content": "OpBNB is a new layer-2 solution for the BNB Chain (BSC chain) based on Optimism Rollups. It was announced on June 19, to solve the growing network congestion problem on the main BNB Chain .\n\nWhat is opBNB?\n\nopBNB is a solution for the growing congestion issues on the BNB Chain. It is an Ethereum Virtual Machine (EVM) compatible layer-2 solution based on Optimism Rollups Stack and optimized to enhance the scalability on the BNB Chain while maintaining security and preserving low transaction fees.\n\nOptimism-based Rollups had been proposed to go live on the BNB Chain as a way to reduce the computational load on the chain via the execution of transactions off-chain and only posting the transaction data on-chain as call data. This solution would drastically improve the scalability of the network by bundling multiple transactions together before their submission to the main chain, which reduces the memory space used.\n\nThe introduction of opBNB to BNB Chain will bring new features, thus making the chain more efficient. These features and new abilities include making data access easier, improving the cache system of the network, and adjusting the submission process algorithm to allow simultaneous operations (also known as batcher).\n\nAdjusting the submission process algorithm to support simultaneous operations allows OpBNB to push the gas limit to 100M, higher than Optimism’s supported 30M. As such, OpBNB will be able to handle over 4000 transactions per second while maintaining an average transaction cost of below $0.005. This integration will improve the existing 2000 transactions per second, making the network double as fast.\n\nopBNB can support over 4000 transfer transactions per second and an average transaction cost lower than 0.005 USD. — BNB Chain (@BNBCHAIN) June 19, 2023\n\nWhat are Optimistic rollups?\n\nAn optimistic rollup is an layer-2 scaling solution designed for Ethereum ( or EVM ). It works by moving computation, state storage and execute transactions outside Ethereum but post the data to the mainnet as calldata.\n\nOptimistic rollups also use compression techniques to reduce the data size posted on the layer-1. They are named “optimistic” because they assume off-chain transactions are valid and thus do not need to publish proofs of validity for transaction batches posted on-chain. As such, they are set apart from zero knowledge rollups that publish cryptographic proofs of validity for off-chain transactions. They instead have a time window where anyone can challenge the results of a rollup transaction via computing a fraud-proof. If the fraud-proof succeeds, the roll-up protocol re-executes the transacts and updates the rollup’s state accordingly.\n\nIf the rollup batch stays unchallenged after challenge time ellipses, it is deemed valid and expected. Others are allowed to continue building on an unconfirmed rollup block but with a caveat: transaction results will be reversed if based on an incorrectly executed transaction that has been published previously.\n\nHow will optimistic rollups interact with BNB Chain?\n\nTransaction and aggregation\n\nUsers will submit transactions to “operators”, nodes responsible for processing transactions. They are also known as validators or aggregators. They aggregate transactions, compress underlying data and publish it on opBNB.\n\nUnlike BNB Chain, where anyone can be a validator, optimistic rollup validators for opBNB will have to provide a bond before producing a block, similar to what is in a proof of stake system. In case of bad acting, the bond can be slashed to prevent dishonest acts from validators.\n\nSubmitting roll-up blocks on opBNB\n\nThe operator of an optimistic rollup bundle off-chain transactions into a batch and then send it to opBNB for notarization. This process involves compression of the data and publishing it as calldata.\n\nCalldata is a non-modifiable, non-persistent area in a smart contract that behaves like a memory. Calldata sends compressed transaction data to the on-chain contract, where the rollup operator adds a new batch by calling the required function in the contract and passing compressed data as function arguments. This process reduces user fees as most of the fees spent come from storing data on-chain.\n\nState commitments\n\nThe optimistic rollup’s state (accounts, balances, contract code, etc.) is organized as a Merkle tree called a “state tree”. The root of this Merkle tree (state route), which references the rollup’s latest state, is then hashed and stored in the contract. Every state transition on the chain produces a new roll-up state that an operator commits to by computing a new state root.\n\nWhen posting batches, the operator must submit old and new state roots. If they match, the new state root is used. The operator must also commit a Merkle root for the transaction batch to allow anyone to prove the inclusion of a transaction in the batch by presenting Merkle proof.\n\nFraud Proving\n\nOptimistic rollups allow anyone to publish blocks without providing proof of validity. However, ensuring the chains are safe allows for a specific time window where anyone can dispute a state transaction (assertion). If someone disputes an assertion, the roll-up protocol initiates the fraud-proof computation, an interactive process requiring someone to post an assertion before another person challenges it.\n\nSingle-round interactive fraud-proof schemes replay disputed transactions on L1 to detect invalid assertions. The roll-up protocol then emulates the re-execution of the disputed transaction on opBNB via a verifier contract with the computed state root showing who won the challenge. The operator is penalized by slashing their bond if the challenger is correct about mistakes.\n\nMulti-round interactive proving\n\nMulti-round interactive proving involves a back-and-forth protocol between the asserter and the challenger overseen by an L1 verifier contract that ultimately decides who lies. After an L2 node challenges an assertion, the asserter divides the disputed assertion into two halves(bisection protocol). Each assertion will contain as many steps as the other.\n\nThe challenger then chooses the assertion that they want to challenge. It continues till both parties are disputing an assertion about a single step of execution. This is when L1 contracts resolve the dispute by evaluating the instruction and its result to catch the fraudulent party.\n\nThe asserter must then provide a “one-step proof” verifying the validity of the disputed single-step computation. If the asserter fails to provide the on-step proof or the L1 verifier contract deems the proof invalid, they lose the challenge.\n\nFeatures of opBNB\n\nEnhanced scalability\n\nopBNB supports over 4000 transactions per second, double the available transaction speed on BNB Chain. This capability gives it a boost clear of BNB Chain current abilities making it ideal for applications that have large daily active users.\n\nEVM compatibility\n\nopBNB presents an accessible platform for developers familiar with Ethereum’s tooling and dApps as it is Ethereum Virtual Machine compatible. That means it will be easier for developers seeking to migrate or introduce extensions of their Ethereum applications to the BNB Chain. That also creates a chance for the developers to take advantage of both Ethereum and BNB Chain ecosystems.\n\nImproved security and trust\n\nWhen it comes to blockchain technology, security and trust are very crucial. opBNB provides a fraud-proving scheme that can be pursued by anyone who notices discrepancies. As such, the transaction(s) in question will be redone, which adds a layer of trust in developers and users alike.\n\nThe improved transparency and verifiability of transactions on opBNB will also encourage a healthier and safer environment for building blockchain ecosystems.\n\nRich ecosystem integration\n\nopBNB was built to make it a part of a broader ecosystem. It is optimized as an integration to the broader BNB Chain ecosystem to provide developers and projects the chance to interact with various other projects and tokens, including but not limited to ERC-20 and its derivative standards.\n\nAs such, opBNB has the potential to foster the growth of a diverse array of blockchain applications ranging from gaming dApps to social networks and Decentralized Finance.\n\nFuture-proof development\n\nBlockchain technology is growing rapidly, necessitating long-lasting solutions to stay competitive. opBNB does just this. It comes with improved security, better efficiency, and compatibility with other projects, making it robust and flexible. As a result, developers can adapt to changes more easily, introduce new features and experiment with new ideas making it a solution that will push BNB Chain forward for many years.\n\nConclusion\n\nBlockchain technology is a revolutionary innovation of our time. It has introduced easier and trustless data communication ways. As a result, it has found a place in sensitive industries like finance and data management. While all is said and done, some problems remain, like the scalability and affordability of networks.\n\nExisting layer 1 networks that allow the building of applications face congestion issues arising from lacking scalability. BNB Chain is easily one such network working to evade the problem by introducing scaling solutions like opBNB. This solution will help the network remain resilient for a longer time.\n\nHowever, nothing is promised regarding the future demands that the network may face. Therefore, do your research on opBNB and use it wisely. Keep watching Blockmanity for updates on the crypto and blockchain industries.\n\nDiscuss this news on our Telegram Community. Subscribe to us on Google news and do follow us on Twitter @Blockmanity\n\nDid you like the news you just read? Please leave a feedback to help us serve you better\n\nDisclaimer: Blockmanity is a news portal and does not provide any financial advice. Blockmanity's role is to inform the cryptocurrency and blockchain community about what's going on in this space. Please do your own due diligence before making any investment. Blockmanity won't be responsible for any loss of funds."}]