[{"id": 4, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA4LTE4L2JuYi1leHBsb2l0LWhhY2tlci1nZXRzLTYzLW1pbGxpb24tbGlxdWlkYXRlZC1vdmVyLXZlbnVzLXByb3RvY29s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 18 Aug 2023 07:00:00 GMT", "title": "BNB Exploit Hacker Gets $63 Million Liquidated Over Venus Protocol - Bloomberg", "content": "Why did this happen?\n\nPlease make sure your browser supports JavaScript and cookies and that you are not blocking them from loading. For more information you can review our Terms of Service and Cookie Policy."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA4LTE4L2JuYi1leHBsb2l0LWhhY2tlci1nZXRzLTYzLW1pbGxpb24tbGlxdWlkYXRlZC1vdmVyLXZlbnVzLXByb3RvY29s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 18 Aug 2023 07:00:00 GMT", "title": "BNB Exploit Hacker Gets $63 Million Liquidated Over Venus Protocol - Bloomberg", "content": "Why did this happen?\n\nPlease make sure your browser supports JavaScript and cookies and that you are not blocking them from loading. For more information you can review our Terms of Service and Cookie Policy."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vd3d3LmJsb29tYmVyZy5jb20vbmV3cy9hcnRpY2xlcy8yMDIzLTA4LTE4L2JuYi1leHBsb2l0LWhhY2tlci1nZXRzLTYzLW1pbGxpb24tbGlxdWlkYXRlZC1vdmVyLXZlbnVzLXByb3RvY29s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 18 Aug 2023 07:00:00 GMT", "title": "BNB Exploit Hacker Gets $63 Million Liquidated Over Venus Protocol - Bloomberg", "content": "Why did this happen?\n\nPlease make sure your browser supports JavaScript and cookies and that you are not blocking them from loading. For more information you can review our Terms of Service and Cookie Policy."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiamh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9ibmItY2hhaW4tZXhwbG9pdGVyLWxvc2VzLTYzbS1vbi12ZW51cy1wcm90b2NvbC10by1saXF1aWRhdGlvbi1hbWlkLW1hcmtldC1jcmFzaC_SAW5odHRwczovL2NyeXB0b3BvdGF0by5jb20vYm5iLWNoYWluLWV4cGxvaXRlci1sb3Nlcy02M20tb24tdmVudXMtcHJvdG9jb2wtdG8tbGlxdWlkYXRpb24tYW1pZC1tYXJrZXQtY3Jhc2gvP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 18 Aug 2023 07:00:00 GMT", "title": "BNB Chain Exploiter Loses $63M on Venus Protocol to Liquidation Amid Market Crash - CryptoPotato", "content": "A crypto wallet connected to the October 2022 BNB Chain $600 million exploit has lost roughly $63 million on the decentralized lending platform Venus Protocol due to liquidation that resulted from the massive bloodbath in the market yesterday.\n\nAccording to a series of tweets by blockchain security firm PeckShield, the exploiter’s wallet was first liquidated of 1.1 million Venus BNB (vBNB) worth around $9.9 million with the asset’s price as $9.4. The second liquidation was approximately 5.6 million vBNB worth $52.3 million.\n\nBNB Chain Hacker’s Loan Position Liquidated\n\nDuring the 2022 BNB Chain hack, the attacker minted two million BNB tokens, using 900,000 to borrow roughly $150 million worth of Tether (USDT) and USD Coin (USDC) against Venus. As BNB’s price continued to decline over the past few months, the loan’s health rate deteriorated and raised concerns about cascading effects of the BNB Chain upon liquidation.\n\nTo contain the situation, BNB Chain and Venus passed a proposal in November that authorized only the core team to liquidate the exploiter’s position. To that effect, the duo whitelisted a wallet, exclusively permitting it to implement the liquidation in a manner that would prevent a shortfall on Venus and provide additional support during the process.\n\nIn June, the wallet was funded with $30 million in USDT in preparation for the liquidation as BNB continued to plummet amid legal issues between the U.S. Securities and Exchange Commission and crypto exchange Binance.\n\nAs soon as BNB tumbled below $220 alongside other cryptocurrencies, the collateral positions linked to the hacker’s wallet got liquidated, and the 900,000 BNB was sent to Venus. The assets were worth around $196.2 million at writing time, with BNB trading at $218 per CoinMarketCap.\n\nNo Resulting Shortfall on Venus\n\nThe mass liquidation was contained by the proposal passed by Venus and BNB Chain after the hack last year. There was no shortfall on Venus or cascading effects on BNB or the broader market.\n\n“Following today’s market movement, the BNB Bridge exploiter account was made healthy as promised by @BNBCHAIN using whitelisted liquidation without any resulting shortfall or further impact to $bnb,” the Venus team said."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiLWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2JzYy12ZW51cy1wcm90b2NvbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 18 Aug 2023 07:00:00 GMT", "title": "DeFi on the brink of second bailout as Venus liquidates $30M - Blockworks", "content": "For the second time in just a matter of weeks, a DeFi protocol has taken unusual measures to prevent systemic risk relating to a lending position.\n\nThe Binance Smart Chain-based lending protocol, Venus, announced on Thursday night that a notorious quarter-billion dollar position was “made healthy” by the BNB core team following a liquidation of $33 million worth of Binance Coin.\n\nLoading Tweet..\n\nThe position was first created in October 2022 following one of the largest hacks in crypto history. An attacker — now widely believed to be the North Korean-affiliated Lazarus Group — stole 2 million Binance Coin (BNB) worth over a half billion dollars at the time from a cross-chain bridge.\n\nUnable to offload their haul without drastically reducing the BNB price, the attacker hesitated. They might have feared a chain rollback or another retaliatory action from Binance. Instead, the attacker deposited hundreds of millions into Venus as collateral. This allowed them to secure a massive $150 million stablecoin loan, most of which was then bridged to other chains and exchanged for ETH. Only a minor portion of it was frozen by Tether. It is unlikely the attacker ever intended to repay this debt.\n\nPer data from DeFiLlama, at the time of the attack the deposit from the exploiter accounted for nearly 20% of Venus’ total value locked, and limited liquidity for BNB meant that liquidating the position could pose existential risks for Venus in the form of bad debt.\n\nAs a result, in November the BNB core team successfully proposed becoming the sole liquidator responsible for the position via Venus governance.\n\n“As the market is very volatile, there is potential risk that if liquidated, there is potential this large portion of BNB may cause more cascading liquidation effect and unnecessary damage to the market and cause more risks to Venus, Venus users, BNB token, and BNB chain,” a core team representative wrote in the formal vote.\n\nThe position then largely sat dormant until June 2023, as the price of BNB fell close to liquidation threshold. $30 million tether (USDT) was added to the liquidator address, adding to an initial $30 million BUSD position.\n\nLast night, amid a marketwide rout, BNB dipped below the liquidation threshold of roughly $220 million, leading the assigned BNB core team liquidation address to step in.\n\nIn an interview with Blockworks, pseudonymous Venus BD & Community Lead “Danny” confirmed that – despite widespread reporting that the liquidation was for over $60 million – the BNB core team seized $33 million of BNB collateral by liquidating $30 million in USDT in debt across a series of three transactions.\n\nEven with the liquidation the protocol may not be out of the woods, as Debank shows that the health rate of the position is perilously low, and another downturn could lead to further liquidations at a price of roughly $210.8 per BNB. After the liquidations today, the liquidator address has an additional $29.9 million in stables remaining, relative to $126 million in outstanding remaining debt.\n\n“BNB Chain will continue to monitor and manage the health of the account as necessary,” said Danny.\n\nLending markets under stress\n\nVenus is not the only lending market to come under stress in recent weeks.\n\nOn July 31, decentralized exchange Curve Finance suffered a $70 million hack. While much of the losses were later recovered or seized by whitehats, one of the greatest losses was to a CRV/ETH pool, which was central to CRV’s onchain liquidity.\n\nCRV’s liquidity depth and price soon became a focal point for the DeFi community, as Curve founder Michael Egorov had outstanding loans worth upwards of $110 million owed to various protocols using CRV as collateral. The liquidation of these positions without a source of onchain CRV liquidity could have caused a rash of bad debts and liquidation cascades across the space.\n\nUltimately Egorov managed to pay down large swaths of his debts by selling large sums of CRV OTC to a number of counterparties.\n\nEgorov still has over $45 million in outstanding liabilities owed to various protocols.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}]