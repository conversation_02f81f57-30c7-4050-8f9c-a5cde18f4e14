[{"id": 2, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3LnRyYWRpbmd2aWV3LmNvbS9uZXdzL2NyeXB0b2RhaWx5OmE5OWFlM2NiYzA5NGI6MC1iaW5hbmNlLWxhdW5jaGVzLXRyYWRpbmctYm90cy1zaGlmdHMtdG8tYXV0b21hdGlvbi_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 14 May 2023 07:00:00 GMT", "title": "Binance Launches Trading Bots, Shifts To Automation - TradingView", "content": "Premier crypto exchange Binance recently announced its shift towards automation with the introduction of Trading Bots.\n\nAccording to Binance, the move also coincides with the migration of its previous bot features such as Rebalancing Bot, whose functionality is now integrated into the new (generalized) Trading Bot.\n\nIntroducing #Binance's updated Trading Bots.Automate trades using a number of strategies including:🔸 Spot Grid🔸 Futures Grid🔸 Rebalancing Bots🔸 Auto-InvestAnd more!Get all the details here 👇— Binance (@binance) May 11, 2023\n\nCrypto trading bots are software programs designed to execute buy and sell orders for cryptocurrencies at optimal times. Binance plans to release various functions to users by June 2023 as part of the implementation.\n\nAutomated crypto trading is a method of trading cryptocurrencies using software programs or bots that automatically execute buy and sell orders based on predefined rules and algorithms. This approach eliminates the need for manual intervention, allowing traders to streamline their trading strategies and capitalize on market opportunities 24/7.\n\nThe primary goal of automated crypto trading is to maximize profits while minimizing risks. To achieve this, traders develop strategies based on technical indicators, price patterns, and other market data. These strategies are then programmed into trading bots, which execute orders on behalf of the trader.\n\nTrading bots can be customized to follow various trading strategies, such as arbitrage, market making, trend following, and mean reversion.\n\nIn the case of Binance's new trading automation scheme, new spot and futures grids will be created. Users will have the option to run futures grids through their Trading Bots account while trading on the same symbol through their futures account simultaneously. Additionally, users will earn hourly trading fee savings for the Trading Bots account when utilizing their BNB balances.\n\nThe announcement of this new trading feature has increased on-chain activity for BNB Chain, with daily active users rebounding to a 5-week high over the 24 hours since its implementation. However, the chain's liquidity has continued to decline, with the total value locked (TVL) on the BNB Chain falling by over 3% to roughly $5.22 billion.\n\nRecently, Binance announced its exit from the Canadian crypto market, citing unfavorable regulatory frameworks from the country.\n\nDisclaimer: This article is provided for informational purposes only. It is not offered or intended to be used as legal, tax, investment, financial, or other advice."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiN2h0dHBzOi8vc2VnbWVudG5leHQuY29tL2luanVzdGljZS0yLWhhcmxleS1xdWlubi1ndWlkZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 14 May 2023 07:00:00 GMT", "title": "Injustice 2 Harley Quinn Guide – How to Play, Best BNB Combos, Best Builds, Movelist - SegmentNext", "content": "Injustice 2 <PERSON> to help you learn everything you need about playing as <PERSON>, her combos, and best builds. In Injustice 2, <PERSON> primarily relies on her gadgets and environmental interactions in order to deal damage. However, due to the fact that some of her attacks are fairly slow, you can get easily punished by skilled opponents.\n\nWhen it comes to Injustice 2, the core mechanics of the game are similar to its predecessor with a few small changes. One of these changes is the introduction of the Gear System that rewards players with pieces of equipment that they can use to alter a character’s appearance, abilities, and stats. As for the story, it revolves around <PERSON>’s attempts to restore the society following <PERSON>’s regime.\n\nFor more help on Injustice 2, check out our Reading Frame Data Guide, Flash Guide, and Batman Guide.\n\nInjustice 2 <PERSON>\n\nIn our Injustice 2 Harley Quinn Guide, we have detailed everything you need to know about playing as <PERSON>, her entire movelist, best combos, and best builds.\n\nInjustice 2 Harley Quinn Guide\n\nPlaying as <PERSON>\n\nIt goes without saying but <PERSON> in Injustice 2 is one of the most balanced characters in the game. However, in order to utilize her full potential, you must practice her set-ups. As a gadget user, she relies on using the environmental interactions to her advantage.\n\nOne of the strengths of <PERSON> is her Character Power that allows players to set-up plays that translate into massive combos. However, do note that some of <PERSON>’s combos are relatively slow which can get punished. If you are playing against <PERSON>, I recommend closing in the gap and not her a chance to utilize her Special Attacks.\n\nInjustice 2 <PERSON> BNB Combos\n\nIn ther section of the guide, we have detailed some BNB combos that you can use in your matches:\n\nu + 2 ~ b + 2, 2 ~ d, b + 3 ~ 1 ~ dash ~ 1, 1 ~ d, b + 3 ~ 3\n\nb + 2, 2 ~ d, b + 3 ~ 1 ~ b + 3 Wall Bound ~ u + 2 ~ f + 1, 3\n\nb + 2, 2 ~ d, b + 3 ~ 1 ~ f + 1, 3 ~ f + 1, 3 ~ 1, 1 ~ d, b, f + 1\n\nb + 2, 2 ~ d, b + 3 ~ 1 ~ b + 3 Wall Bound ~ u + 1 ~ 1, 1, 2\n\nCorner u + 2 ~ f + 1 ~ d, b + 2 Meter Burn ~ f + 1 ~ d, b + 2 Meter Burn ~ b + 2, 2 ~ d, b + 3 ~ 1 ~ 1, 1, 2 ~ 1 ~ 1 ~ 1, 1 ~ d, b, f + 1\n\nInjustice 2 Harley Quinn Movelist\n\nSpecial Moves\n\nCupcake Bomb\n\nLeft, Right + Medium Attack\n\nMeter Burn\n\nPistol Fury\n\nDown, Right + Light Attack\n\nMeter Burn\n\nJump, Down, Left + Light Attack\n\nDown, Left + Light Attack\n\nPlay Doctor\n\nDown, Left, Right + Light Attack\n\nMeter Burn\n\nPop Pop\n\nDown, Left + Medium Attack\n\nHold Medium\n\nRight, Right or Left, Left\n\nMeter Burn\n\nSuper\n\nFlip Stance + Meter Burn\n\nTantrum Stance\n\nDown, Left + Heavy Attack\n\nLight Attack\n\nMedium Attack\n\nHeavy Attack\n\nRight\n\nLeft\n\nCharacter Power\n\nBud Rush\n\nCharacter Power\n\nHold Character Power\n\nCharacter Power, Up\n\nHold Character Power + Up\n\nHold Character Power + Left\n\nCombo Attacks\n\nA Little Crazy\n\nLeft + Medium Attack, Medium Attack\n\nBatter Up\n\nMedium Attack, Right + Heavy Attack\n\nDon’t Get Hurt\n\nRight + Medium Attack, Medium Attack\n\nFor Mistah J\n\nRight + Light Attack, Heavy Attack\n\nGirl’s Best Friend\n\nLight Attack, Medium Attack\n\nGo Night Night\n\nMedium Attack, Right + Heavy Attack, Light Attack + Heavy Attack\n\nHi Puddin’\n\nMedium Attack, Down + Heavy Attack\n\nI Hope It Hurts\n\nLight Attack, Light Attack, Medium Attack\n\nIrresistible\n\nLeft + Light Attack, Medium Attack\n\nLet’s Play\n\nLeft + Light Attack, Medium Attack, Light Attack\n\nLollipops\n\nHeavy Attack, Heavy Attack\n\nMiss Me\n\nMedium Attack, Left + Heavy Attack\n\nNaughty-Naughty\n\nLight Attack, Light Attack\n\nPleased to Meetcha\n\nMedium Attack, Up + Heavy Attack\n\nRude Joke\n\nLight Attack, Medium Attack, Heavy Attack\n\nThat’s Cute\n\nRight + Medium Attack, Heavy Attack\n\nBasic Attacks\n\nAway Air Escape\n\nLeft + Meter Burn\n\nBack Throw\n\nLight Attack + Heavy Attack\n\nBounce Cancel\n\nLeft, Left + Meter Burn or Down, Left + Meter Burn\n\nClown Bash\n\nRight + Medium Attack\n\nEar Smash\n\nRight + Light Attack\n\nForward Throw\n\nRight + Light Attack + Heavy Attack\n\nGun Smack\n\nLeft + Light Attack\n\nHammer Slam\n\nRight + Heavy Attack\n\nRight\n\nLeft\n\nMeter Burn\n\nHold Heavy\n\nRight, Right or Left, Left\n\nJumping Heavy Attack\n\nJump, Heavy Attack\n\nJumping Light Attack\n\nJump, Light Attack\n\nJumping Medium Attack\n\nJump, Medium Attack\n\nMallet Slap\n\nLeft + Heavy Attack\n\nMeter Burn\n\nHold Heavy\n\nRight, Right or Left, Left\n\nOverhead Bounce Cancel\n\nRight, Right + Meter Burn or Down, Right + Meter Burn\n\nRevolver Poke\n\nDown + Light Attack\n\nRevolver Slam\n\nMedium Attack\n\nRevolver Slap\n\nLight Attack\n\nRoll Escape\n\nRight, Right + Meter Burn\n\nSingle Shot\n\nLeft + Medium Attack\n\nSpinning Pistols\n\nDown + Medium Attack\n\nSpot Kick\n\nHeavy Attack\n\nTake Aim\n\nDown + Heavy Attack\n\nUp Air Escape\n\nUp + Meter Burn\n\nInjustice 2 Harley Quinn Gear Special Effects\n\nWe are currently in the process of updating this section and will have more information soon.\n\nThis all we have in our Injustice 2 Harley Quinn Guide. Let us know if you have anything else to add to the guide!"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvemstcm9sbHVwLXNob3dkb3duLWNvbXBhcmluZy16a3N5bmMtcG9seWdvbi16a2V2bS1hbmQtemstYm5iLWZvci1zY2FsYWJpbGl0eS1zb2x1dGlvbnPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 14 May 2023 07:00:00 GMT", "title": "ZK Rollup Showdown: Comparing zkSync, Polygon zkEVM, and zk-BNB for Scalability Solutions - BSC NEWS", "content": "WEB3\n\nZK Rollups are a layer-2 solution that collects transactions off the main blockchain and batch them into a single transaction, thereby increasing throughput while reducing gas fees. ‍\n\nTL;DR:\n\nZero-Knowledge Rollups (ZK Rollups) are a Layer-2 solution for blockchain scalability.\n\nzkSync uses zk-rollups for Ethereum's layer-two blockchain, with gas fees 1/100 of L1 gas and reportedly supporting over 100,000 transactions per second.\n\nPolygon zkEVM uses zero-knowledge proofs to reduce transaction costs and increase throughput while inheriting Ethereum's security, with a lower throughput of 2,000 TPS.\n\nZkBNB can package hundreds of transactions and generate cryptographic proofs as zkRollups, allowing users to trade digital assets without permission and offering developers the opportunity to build marketplaces for NFTs.\n\nThe three zk solutions are developer-friendly, allowing easy porting of Ethereum dApps.\n\n‍\n\nZk Rollup: Layer-2 Solutions for Blockchain Scalability\n\nBlockchain technology has come a long way in recent years, but one problem still looms large: scalability. As the number of users and transactions continues to increase, the limitations of blockchain become more apparent. That's where Layer-2 solutions like Zero-Knowledge Rollups (ZK Rollups) come in. ZK Rollups are considered the ultimate Layer-2 solution for blockchain-related scalability problems, and they can potentially transform the way we use blockchain technology. In this article, we'll take a closer look at the innovations in the zk-rollup space, focusing on three main players: zkSync Era, zk-BNB, and zk-Polygon.\n\nWhat is zkSync:\n\nThe zkSync blockchain uses zk-rollups as a scaling technology for Ethereum's layer-two blockchain. In a similar fashion to optimistic rollups, zk-rollups gather transactions off the Ethereum mainnet and submit their proofs to Ethereum. By doing this, hundreds of transactions are batched into one, verified and secured by Ethereum.\n\nWith zkSync, each batch of transactions is passed to an off-chain provision that generates cryptographic proof (called a SNARK) that they are valid. Verifying the validity of the proof is easier than developing the proof. The ease of this process means that it can be sent to Layer 1 and confirmed in a smart contract. As a result, transfers between Layers 1 and 2 are near-frictionless.\n\nFurthermore, since it is EVM compatible, you can easily transfer existing Layer 1 smart contracts to Layer 2.\n\nMoreover, zkSync with its V2 version it can reportedly support over 100,000 transactions per second once ETH2 data sharding is available. According to zkSync, gas fees are 1/100 of L1 gas and cheaper than optimistic rollups.\n\nFurther, the main Ethereum blockchain secures the assets, and you can move your assets back to L1 at any time.\n\nWhat is Polygon ZkEVM:\n\nThe Polygon zkEVM takes advantage of zero-knowledge proofs' scaling power. In fact, it's the first zero-knowledge scaling solution that is entirely equivalent to an EVM.\n\nzkEVM uses ZK proofs to reduce transaction costs and increase throughput while inheriting Ethereum's security. Polygon zkEVM has a lower throughput of 2,000 TPS.\n\nFurther Polygon zkEVM makes transactions much more efficient by creating a parallel chain to the main Polygon network using zk-proofs. It allows transactions to be validated without exposing sensitive information, making them faster and more secure.\n\nMoreover, Polygon zkEVM allows developers and users to use the same code, apps, and tooling but reportedly with much better performance and lower fees. By leveraging Ethereum's network effects, EMV-compatible zkEVMs can benefit from EMV's benefits.\n\nThe zkEVM allows users to deposit assets from Ethereum and transact off-chain. All of these transactions are grouped into batches, and a zero-knowledge proof confirms each transaction's validity. By doing this, the operators of zkEVM cannot steal user funds.\n\nAdditionally, it allows smart contracts on L2 to perform transactions that can be verified on L1 without the need for nodes to re-execute them.\n\nWhat is ZkBNB:\n\nThe ZkBNB has the same capability of packaging (or \"rolling-up\") hundreds of transactions and generating cryptographic proofs as zkRollups. It is possible to prove the validity of each transaction in the Rollup Block using SNARKs (succinct non-interactive arguments of knowledge). In this way, it ensures that all funds are held on the BSC, while computations and storage are performed on the BNB Sidechains, at a much lower cost and more quickly.\n\nAccording to the protocol, ZkBNB is just as secure as BNB Smart Chain because it uses zk-SNARK proofs.\n\nFurthermore, zkBNB's built-in liquidity pools allow users to trade digital assets without permission. zkBNB also offers developers the opportunity to build marketplaces for crypto collectibles and non-fungible tokens (NFTs).\n\nIn addition to supporting 100 million addresses, zkBNB can reportedly handle up to 10 thousand transactions per second (TPS), and gas fees can be reduced by up to 10x. Moreover, if a user feels that his transactions are censored by zkBNB, they can withdraw funds at any time.\n\nSimilarities and Differences\n\nZK-rollup has several advantages over other scaling solutions, such as Sidechains and Plasma.\n\nRollup validators cannot tamper with the states or embezzle money.\n\nIt is also possible for users to retrieve their funds from the Rollup even if the validators stop cooperating. Due to the readily available data, it is easier to access than in Plasma.\n\nValidity proofs eliminate the need for users or other trusted parties to monitor Rollup blocks online.\n\nThe three zk solutions discussed here are developer-friendly, allowing easy porting of Ethereum dApps.\n\nIn spite of the similarities between these projects, there is one critical difference that sets zkSync apart. In contrast to Polygon's zkEVM and zk-BNB, zkSync does not support bytecode. With bytecode compatibility, Ethereum's core code can be easily ported to Layer 2 networks with minimal changes.\n\nAs well, zkSync requires projects to be compiled using its LLVM-based compiler before they can be used on its network. Due to this approach, zkSync differs slightly from the other two zk solutions.\n\nSince zkSync Era launched, the project has accumulated a total value locked (TVL) of $249 million, according to L2 Beat. It is significantly higher than the $8.4 million on Polygon's chain of zkEVM. This discrepancy can attributed to the airdrop hype around zkSync Era.\n\nZero-knowledge proofs allow individuals to verify their identities without divulging sensitive personal information. As an alternative to providing identity details, users can simply verify that they meet specific criteria, such as a decentralized identity that verifies their citizenship without sharing their name or passport number.\n\nEven though all current zk solutions offer several advantages for the crypto space, it will take time to see which one wins since the technology is still relatively new and many innovations have yet to be implemented."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMicGh0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvemstcm9sbHVwLXNob3dkb3duLWNvbXBhcmluZy16a3N5bmMtcG9seWdvbi16a2V2bS1hbmQtemstYm5iLWZvci1zY2FsYWJpbGl0eS1zb2x1dGlvbnPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 14 May 2023 07:00:00 GMT", "title": "ZK Rollup Showdown: Comparing zkSync, Polygon zkEVM, and zk-BNB for Scalability Solutions - BSC NEWS", "content": "WEB3\n\nZK Rollups are a layer-2 solution that collects transactions off the main blockchain and batch them into a single transaction, thereby increasing throughput while reducing gas fees. ‍\n\nTL;DR:\n\nZero-Knowledge Rollups (ZK Rollups) are a Layer-2 solution for blockchain scalability.\n\nzkSync uses zk-rollups for Ethereum's layer-two blockchain, with gas fees 1/100 of L1 gas and reportedly supporting over 100,000 transactions per second.\n\nPolygon zkEVM uses zero-knowledge proofs to reduce transaction costs and increase throughput while inheriting Ethereum's security, with a lower throughput of 2,000 TPS.\n\nZkBNB can package hundreds of transactions and generate cryptographic proofs as zkRollups, allowing users to trade digital assets without permission and offering developers the opportunity to build marketplaces for NFTs.\n\nThe three zk solutions are developer-friendly, allowing easy porting of Ethereum dApps.\n\n‍\n\nZk Rollup: Layer-2 Solutions for Blockchain Scalability\n\nBlockchain technology has come a long way in recent years, but one problem still looms large: scalability. As the number of users and transactions continues to increase, the limitations of blockchain become more apparent. That's where Layer-2 solutions like Zero-Knowledge Rollups (ZK Rollups) come in. ZK Rollups are considered the ultimate Layer-2 solution for blockchain-related scalability problems, and they can potentially transform the way we use blockchain technology. In this article, we'll take a closer look at the innovations in the zk-rollup space, focusing on three main players: zkSync Era, zk-BNB, and zk-Polygon.\n\nWhat is zkSync:\n\nThe zkSync blockchain uses zk-rollups as a scaling technology for Ethereum's layer-two blockchain. In a similar fashion to optimistic rollups, zk-rollups gather transactions off the Ethereum mainnet and submit their proofs to Ethereum. By doing this, hundreds of transactions are batched into one, verified and secured by Ethereum.\n\nWith zkSync, each batch of transactions is passed to an off-chain provision that generates cryptographic proof (called a SNARK) that they are valid. Verifying the validity of the proof is easier than developing the proof. The ease of this process means that it can be sent to Layer 1 and confirmed in a smart contract. As a result, transfers between Layers 1 and 2 are near-frictionless.\n\nFurthermore, since it is EVM compatible, you can easily transfer existing Layer 1 smart contracts to Layer 2.\n\nMoreover, zkSync with its V2 version it can reportedly support over 100,000 transactions per second once ETH2 data sharding is available. According to zkSync, gas fees are 1/100 of L1 gas and cheaper than optimistic rollups.\n\nFurther, the main Ethereum blockchain secures the assets, and you can move your assets back to L1 at any time.\n\nWhat is Polygon ZkEVM:\n\nThe Polygon zkEVM takes advantage of zero-knowledge proofs' scaling power. In fact, it's the first zero-knowledge scaling solution that is entirely equivalent to an EVM.\n\nzkEVM uses ZK proofs to reduce transaction costs and increase throughput while inheriting Ethereum's security. Polygon zkEVM has a lower throughput of 2,000 TPS.\n\nFurther Polygon zkEVM makes transactions much more efficient by creating a parallel chain to the main Polygon network using zk-proofs. It allows transactions to be validated without exposing sensitive information, making them faster and more secure.\n\nMoreover, Polygon zkEVM allows developers and users to use the same code, apps, and tooling but reportedly with much better performance and lower fees. By leveraging Ethereum's network effects, EMV-compatible zkEVMs can benefit from EMV's benefits.\n\nThe zkEVM allows users to deposit assets from Ethereum and transact off-chain. All of these transactions are grouped into batches, and a zero-knowledge proof confirms each transaction's validity. By doing this, the operators of zkEVM cannot steal user funds.\n\nAdditionally, it allows smart contracts on L2 to perform transactions that can be verified on L1 without the need for nodes to re-execute them.\n\nWhat is ZkBNB:\n\nThe ZkBNB has the same capability of packaging (or \"rolling-up\") hundreds of transactions and generating cryptographic proofs as zkRollups. It is possible to prove the validity of each transaction in the Rollup Block using SNARKs (succinct non-interactive arguments of knowledge). In this way, it ensures that all funds are held on the BSC, while computations and storage are performed on the BNB Sidechains, at a much lower cost and more quickly.\n\nAccording to the protocol, ZkBNB is just as secure as BNB Smart Chain because it uses zk-SNARK proofs.\n\nFurthermore, zkBNB's built-in liquidity pools allow users to trade digital assets without permission. zkBNB also offers developers the opportunity to build marketplaces for crypto collectibles and non-fungible tokens (NFTs).\n\nIn addition to supporting 100 million addresses, zkBNB can reportedly handle up to 10 thousand transactions per second (TPS), and gas fees can be reduced by up to 10x. Moreover, if a user feels that his transactions are censored by zkBNB, they can withdraw funds at any time.\n\nSimilarities and Differences\n\nZK-rollup has several advantages over other scaling solutions, such as Sidechains and Plasma.\n\nRollup validators cannot tamper with the states or embezzle money.\n\nIt is also possible for users to retrieve their funds from the Rollup even if the validators stop cooperating. Due to the readily available data, it is easier to access than in Plasma.\n\nValidity proofs eliminate the need for users or other trusted parties to monitor Rollup blocks online.\n\nThe three zk solutions discussed here are developer-friendly, allowing easy porting of Ethereum dApps.\n\nIn spite of the similarities between these projects, there is one critical difference that sets zkSync apart. In contrast to Polygon's zkEVM and zk-BNB, zkSync does not support bytecode. With bytecode compatibility, Ethereum's core code can be easily ported to Layer 2 networks with minimal changes.\n\nAs well, zkSync requires projects to be compiled using its LLVM-based compiler before they can be used on its network. Due to this approach, zkSync differs slightly from the other two zk solutions.\n\nSince zkSync Era launched, the project has accumulated a total value locked (TVL) of $249 million, according to L2 Beat. It is significantly higher than the $8.4 million on Polygon's chain of zkEVM. This discrepancy can attributed to the airdrop hype around zkSync Era.\n\nZero-knowledge proofs allow individuals to verify their identities without divulging sensitive personal information. As an alternative to providing identity details, users can simply verify that they meet specific criteria, such as a decentralized identity that verifies their citizenship without sharing their name or passport number.\n\nEven though all current zk solutions offer several advantages for the crypto space, it will take time to see which one wins since the technology is still relatively new and many innovations have yet to be implemented."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiOGh0dHBzOi8vc2VnbWVudG5leHQuY29tL2luanVzdGljZS0yLWdyZWVuLWxhbnRlcm4tZ3VpZGUv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 14 May 2023 07:00:00 GMT", "title": "Injustice 2 Green Lantern Guide – How to Play, Best BNB Combos, Best Builds, Movelist - SegmentNext", "content": "Injustice 2 Green Lantern Guide to help you learn everything you need about playing as <PERSON> Lantern, his combos, and best builds. <PERSON> Lantern in Injustice 2 excels at mid-range and heavily relies on using his Special Attacks and Character Power in order to deal damage. He is also excellent at zoning his opponents out and have brilliant strings that he can chain into combos.\n\nWhen it comes to Injustice 2, the core mechanics of the game are similar to its predecessor with a few small changes. One of these changes is the introduction of the Gear System that rewards players with pieces of equipment that they can use to alter a character’s appearance, abilities, and stats. As for the story, it revolves around <PERSON>’s attempts to restore the society following Superman’s regime.\n\nFor more help on Injustice 2, check out our Robin Guide, Aquaman Guide, and Supergirl Guide.\n\nInjustice 2 Green Lantern Guide\n\nIn our Injustice 2 Green Lantern Guide, we have detailed everything you need to know about playing as <PERSON> Lantern, his entire movelist, best combos, and best builds.\n\nInjustice 2 Green Lantern Guide\n\nPlaying as Green Lantern\n\nGreen Lantern in Injustice 2 is one of the few characters that have received quite a lot of changes and play like a completely different character when compared to their playstyle in Injustuce: Gods among Us. You will not see Mini Railgun and <PERSON> in the movelist as they now appear under Gear Abilities. <PERSON> Lantern in Injustice 2 can easily close in the gap with his strings and chain them into devastating combos.\n\nHe can also effectively deal with characters who excel at zoning their opponents out such as <PERSON><PERSON> with moves like Overcharged Lantern, Bowled Out, Lantern Blast, etc. Not to mention that he can increase the damage output of his Special Attacks with his Character Power.\n\nIf you are looking forward to picking up Green Lantern in near future, make sure to master his Special Attacks and chaning them into combos. As for playing against it, try to remember that he is a mid-ranged character who utilizes heavily on his Special Attacks and Character Power. As long as you are proficient at avoiding these, you should be good to go.\n\nInjustice 2 Green Lantern BNB Combos\n\nIn this section of the guide, we have detailed some BNB combos that you can use in your matches:\n\nb + 2, 3 ~ d, b + 1 Meter Burn ~ Walk ~ 2, 2, 3 ~ d, b + 1 4, b, f + 2\n\n1, 2, 3 ~ d, b + 1 Meter Burn ~ b + 3 Wall Bound ~ u + 2 ~ 2, 2, 3, 1\n\n4 ~ f + 3 ~ u + 3 ~ 2, 2, 3 ~ d, b + 1 ~ 2, 2, 3 ~ d, b + 1 Meter Burn ~ 2, 2, 3 ~ d, b + 1\n\n4 ~ b + 3 Wall Bound ~ u + 3 ~ u + 1 ~ d, b + 1 ~ 2, 2, 3 ~ db + 1 Meter Burn ~ 2, 2, 3 ~ d, b + 1\n\nPower Ring ~ b + 1, 3 ~ 4 ~ d, b + 1 ~ 2, 2, 3 ~ d, b + 1 Meter Burn ~ b + 3 Wall Bound ~ 2, 2, 3 ~ d, b + 1\n\nInjustice 2 Green Lantern Movelist\n\nSpecial Moves\n\nBattery Blast\n\nLeft, Right + Light Attack\n\nBowled Over\n\nLeft, Right + Medium Attack\n\nMeter Burn\n\nLantern’s Might\n\nDown, Left + Light Attack\n\nMeter Burn\n\nOvercharged Lantern\n\nDown, Left + Medium Attack\n\nMeter Burn\n\nDown, Left + Medium Attack, Left\n\nDown, Left + Medium Attack, Right\n\nWillpower Wall\n\nFlip Stance + Meter Burn\n\nCharacter Power\n\nPower Ring\n\nCharacter Power\n\nCombo Attacks\n\nBrightest Day\n\nLeft + Medium Attack, Heavy Attack, Heavy Attack\n\nGrand Slam\n\nMedium Attack, Medium Attack, Heavy Attack, Light Attack\n\nGuardian\n\nRight + Medium Attack, Down + Light Attack, Heavy Attack\n\nHighball\n\nMedium Attack, Medium Attack\n\nHyperbolic\n\nMedium Attack, Medium Attack, Heavy Attack\n\nJustice Is Served\n\nLeft + Light Attack, Medium Attack\n\nLantern Corps\n\nLeft + Medium Attack, Heavy Attack\n\nParallel Nature\n\nLeft + Light Attack, Heavy Attack\n\nTriple Barrage\n\nLight Attack, Medium Attack\n\nTrue Might\n\nRight + Medium Attack, Down + Light Attack\n\nVolley Smash\n\nLight Attack, Medium Attack, Heavy Attack\n\nBasic Attacks\n\nAway Air Escape\n\nLeft + Meter Burn\n\nBack Throw\n\nLight Attack + Heavy Attack\n\nBounce Cancel\n\nLeft, Left + Meter Burn or Down, Left + Meter Burn\n\nFlipkick\n\nRight + Heavy Attack\n\nMeter Burn\n\nHold Heavy\n\nRight, Right or Left, Left\n\nForward Throw\n\nRight + Light Attack + Heavy Attack\n\nHigh Tension\n\nLeft + Heavy Attack\n\nMeter Burn\n\nHold Heavy\n\nRight, Right or Left, Left\n\nHurricane Kick\n\nHeavy Attack\n\nHold Heavy\n\nRight, Right or Left, Left\n\nJumping Heavy Attack\n\nJump, Heavy Attack\n\nJumping Light Attack\n\nJump, Light Attack\n\nJumping Medium Attack\n\nJump, Medium Attack\n\nLow Cross\n\nDown + Heavy Attack\n\nLow Slide\n\nLeft + Light Attack\n\nLunge Punch\n\nRight + Medium Attack\n\nOverhead Bounce Cancel\n\nRight, Right + Meter Burn or Down, Right + Meter Burn\n\nPush Kick\n\nMedium Attack\n\nRing Cross\n\nLight Attack\n\nRing Jab\n\nDown + Light Attack\n\nRising Punch\n\nDown + Medium Attack\n\nRoll Escape\n\nRight, Right + Meter Burn\n\nSweeping Uppercut\n\nLeft + Medium Attack\n\nUp Air Escape\n\nUp + Meter Burn\n\nInjustice 2 Green Lantern Gear Special Effects\n\nHead/Cape\n\nFortified Space Cop Defender Special Effects\n\nInflict 14.43% extra damage to non-Superpowered Opponents but in Multiverse Only.\n\nDestructive Energy Construct Mask Special Effects\n\nIncreases all XP gained by 2.86%.\n\nSacred Ionic Guardian Mask Special Effects\n\nIncreases Profile XP gained by 6.03% and inflicts 11.64% extra damage to heroes but in Multiverse Only.\n\nSet Bonuses:\n\n(2) Gain an additional 100 Health\n\n(4) Gain an additional 100 Defense\n\n(5) All Constructs inflict 10.00% increased damage\n\nAquiline Mask of Thanagar Special Effects\n\nIncrease Profile XP gained by 7.19% and inflict 16.13% extra damage to Villains but in Multiverse Only.\n\nCollared Starfield Ranger Helmet Special Effects\n\nIncrease all XP gained by 6.03%.\n\nGavyn’s Space Helmet of Throneworld Special Effects\n\nIncrease Profile XP gained by 5.14%.\n\nSet Bonuses:\n\n(3) Green Lantern gains 1.00% increased total Ability Power per 100 points in his Total Ability Power.\n\nTorso\n\nHal’s Parallaxian Chest of Fear Special Effects\n\nReduces damage taken from Arena Transitions by 1.24%.\n\nGavyn’s Star hest of Throne World Special Effects\n\nReduces damage taken from Arena Transitions by 1.44%.\n\nSet Bonuses:\n\nGreen Lantern gains 1.00% increased Total Ability Power per 100 points in his Total Ability Power.\n\nBeacon of Purest Adoration Special Effects\n\nReduce damage taken from Environmental Interactions by 26.10%.\n\nGauntlets\n\nAll-American Crimefighter’s Gloves Special Effects\n\nIncreases Environmental Interaction damage by 40.90%.\n\nGauntlets of the Last Manhunter Special Effects\n\nIncreases Environmental Interaction damage by 41.75%\n\nSecret Origin Gloves Special Effects\n\nIncreases Thrown Environmental Interaction damage by 39.08% and inflict 73.42% extra block damage to Superpowered Opponents in Multiverse Only.\n\nGreaves\n\nAugmented Greaves of Rage Special Effects\n\nGain 305 bonus EXP if you don’t jump but in Multiverse Only.\n\nAccessories\n\nResilient Emotional Projector Special Effects\n\nIncreases Character XP gained by 2.95%.\n\nFortified Spark of Will Special Effects\n\nIncreases the amount of Credits and Guild Credits earned by 4.35% and inflict 18.92% extra damage to Lanterns but in Multiverse Only.\n\nThis is all we have in our Injustice 2 Green Lantern Guide. Let us know if you have anything else to add to the guide!"}]