[{"id": 8, "url": "https://news.google.com/rss/articles/CBMiZ2h0dHBzOi8vaWJzaW50ZWxsaWdlbmNlLmNvbS9pYnNpLW5ld3MvcmlwcGxlLWFuZC1vbmFmcmlxLXRvLWZhY2lsaXRhdGUtY3Jvc3MtYm9yZGVyLXBheW1lbnRzLWluLWFmcmljYS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Nov 2023 08:00:00 GMT", "title": "Ripple and Onafriq to facilitate cross-border payments in Africa - IBS Intelligence", "content": "Ripple and Onafriq to facilitate cross-border payments in Africa\n\nBy Delisha <PERSON>\n\nNovember 09, 2023 Blockchain\n\nCross Border Payments\n\nCrypto Solutions Share\n\nRipple, an enterprise blockchain and crypto solutions and payments FinTech Onafriq, previously MFS Africa, have partnered to facilitate digital asset-enabled cross-border payments between Africa and several new markets, including the Gulf Cooperation Council (GCC), the UK and Australia.\n\nOnafriq is utilising Ripple Payments, Ripple’s crypto-enabled payments technology, to open up three new payments corridors between Africa and the rest of the world. Customers of PayAngel in the UK, Pyypl in the GCC, and Zazi Transfer in Australia are now able to make speedy and cost-effective remittance and business payments to recipients in 27 countries across Onafriq’s pan-African network.\n\n“For a number of years, Ripple has supported crypto-enabled, cross-border payments to individuals and businesses, and we are particularly excited to expand the reach of our solution into Africa thanks to our Onafriq partnership,” said <PERSON>, SVP, Global Customer Success at Ripple. “Connecting our partners PayAngel, Pyppl and Zazi Transfer with Onafriq over Ripple Payments will bring the benefits of faster and more cost-effective cross-border payments to individuals seeking to send money into Africa from around the globe.”\n\nUsing Ripple’s crypto technology, Onafriq is eliminating the traditional problems associated with cross-border payments such as lengthy transfer times, unreliability and excessive cost. In this way the partnership is bringing faster, more efficient, and cost-effective international money transfers to Africa, and is set to accelerate financial inclusion across the continent.\n\nDare Okoudjou, Founder & CEO at Onafriq, said, “Our mission is to make borders matter less when it comes to payment within, to, and from Africa. We are advancing this mission through our partnership with Ripple, which is already enabling new types of connections with fintechs such as PayAngel, Pyppl and Zazi Transfer. These connections are set to enable fast, secure and low-cost remittances at scale between Africa and the rest of the world, and represent a bold first step for our crypto strategy to leverage blockchain technologies to amplify our impact on people and businesses on the continent.”"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vd3d3LmZ4ZW1waXJlLmNvbS9mb3JlY2FzdHMvYXJ0aWNsZS94cnAtbmV3cy11cy1jb25ncmVzc21hbi10b20tZW1tZXItcmVmZXJlbmNlcy1zZWMtdi1yaXBwbGUtaW4taG91c2UtYmlsbC0xMzg3NDQw0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Nov 2023 08:00:00 GMT", "title": "XRP News: US Congressman <PERSON> SEC v Ripple in House Bill - FX Empire", "content": "English English Italiano Español Português Deutsch العربية Français\n\nImportant Disclaimers The content provided on the website includes general news and publications, our personal analysis and opinions, and contents provided by third parties, which are intended for educational and research purposes only. It does not constitute, and should not be read as, any recommendation or advice to take any action whatsoever, including to make any investment or buy any product. When making any financial decision, you should perform your own due diligence checks, apply your own discretion and consult your competent advisors. The content of the website is not personally directed to you, and we does not take into account your financial situation or needs.The information contained in this website is not necessarily provided in real-time nor is it necessarily accurate. Prices provided herein may be provided by market makers and not by exchanges.Any trading or other financial decision you make shall be at your full responsibility, and you must not rely on any information provided through the website. FX Empire does not provide any warranty regarding any of the information contained in the website, and shall bear no responsibility for any trading losses you might incur as a result of using any information contained in the website.The website may include advertisements and other promotional contents, and FX Empire may receive compensation from third parties in connection with the content. FX Empire does not endorse any third party or recommends using any third party's services, and does not assume responsibility for your use of any such third party's website or services.FX Empire and its employees, officers, subsidiaries and associates, are not liable nor shall they be held liable for any loss or damage resulting from your use of the website or reliance on the information provided on this website. Risk Disclaimers This website includes information about cryptocurrencies, contracts for difference (CFDs) and other financial instruments, and about brokers, exchanges and other entities trading in such instruments. Both cryptocurrencies and CFDs are complex instruments and come with a high risk of losing money. You should carefully consider whether you understand how these instruments work and whether you can afford to take the high risk of losing your money.FX Empire encourages you to perform your own research before making any investment decision, and to avoid investing in any financial instrument which you do not fully understand how it works and what are the risks involved."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMifWh0dHBzOi8vd3d3LmFyY2hkYWlseS5jb20vMTAwOTMxNS9lc2tldy1wbHVzLWR1bWV6LXBsdXMtcmlwcGxlLXJldmVhbHMtZGVzaWduLWZvci1uZXctYWNhZGVtaWMtY2VudGVyLWluLWdlb3JnaWEtdGVjaC1hdGxhbnRh0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Nov 2023 08:00:00 GMT", "title": "Eskew+Dumez+<PERSON><PERSON>ple Reveals Design for New Academic Center in Georgia Tech, Atlanta - ArchDaily", "content": "EskewDumezRipple has just revealed the designs for a new academic building for the Georgia Institute of Technology in Atlanta. Embedded into the new “Technology Square,” this expansive project marks phase 3 of Georgia Tech’s growth initiative. Designed to facilitate the development of technology leaders and enhance the human condition of Georgia Tech students and faculty, the scheme creates a vibrant intellectual environment filled with inspiration.\n\n+ 6\n\nExpected to be delivered in 2026, the scheme consists of two towers, one standing at 14 stories and the other at 18 stories, with a shared central core. Designed as two separate towers, each building hosts diverse functions. The 14-story Scheller Tower will house the MBA and Executive Education programs for the Scheller College of Business. Meanwhile, the 18-story Georgy Tower will serve as the new home for the School of Industrial Systems Engineering. The central zone between the towers acts as a communal pathway, running through the building and culminating in a significant gathering space.\n\nViewed from the building's façade, the district's outdoor promenade, which links to the surrounding neighborhoods, has a green roof terrace stacked vertically. This creative element, which mimics a vertically oriented quad, efficiently regulates lighting and adds to the ecological identity of the urban campus. The project serves as a cornerstone for Georgia Tech's environmental goals and provides inspiration for future projects inside Technology Square.\n\nRelated Article <PERSON><PERSON><PERSON>'s Science Center Breaks Ground in Singapore\n\n'The Porch,' a classic Southern idea, serves as the basis for the ground floor design, which expands it to allow for conversation and idea sharing. This space is intended to host sizable district meetings and interact with Midtown's well-known 5th Street corridor, which connects to the original campus's central section.\n\nExtensive, flexible entrances allow the central meeting area to become a dynamic social experiment by seamlessly blending indoor and outdoor elements. Additionally, there are 'collision zones' on the center podium where students can congregate for learning, sharing ideas, and creating. This gathering area is situated next to an outside patio shaded by trees, providing a year-round setting for creative collaboration.\n\nThe design of university buildings and campuses is crucial to how people view and experience education. This week, MVRDV and Diamond Schmitt unveiled the design for a new building for the Scarborough Academy of Medicine and Integrated Health (SAMIH) at the University of Toronto’s Scarborough Campus. In October, Foster + Partners revealed the designs for the Ellison Institute of Technology campus in Oxford. The institute's focus is on cancer, wellness, public health, food security, clean energy, and climate change. Finally, WilkinsonEyre has been selected to design a new college campus on the Olympic Way in Wembley, London, to serve as the new educational facility for the College of North West London."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vYmxvY2tvbm9taS5jb20vcmlwcGxlLXhycC1leHBhbmRzLWdsb2JhbC1yZWFjaC13aXRoLW5ldy1pbml0aWF0aXZlLWluLWFmcmljYS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Nov 2023 08:00:00 GMT", "title": "Ripple XRP Expands Global Reach with New Initiative in Africa - Blockonomi", "content": "Crypto solution provider Ripple has teamed up with African payment fintech Onafriq (formerly MFS Africa) to power seamless cross-border payments for digital assets between Africa and other markets, including the Gulf Cooperation Council (GCC), the UK and Australia.\n\nThe company also upgraded its payment services to onboard more corporate clients. Africa is a huge market, but the African banking system is poorly developed. Now, XRP can help bring African banking to a new level.\n\nRipple Teams up with African Fintech Leader\n\nThe GCC is a political and economic union which consists of six sovereign states Bahrain, Kuwait, Oman, Qatar, Saudi Arabia, and the United Arab Emirates.\n\nAs part of the collaborative strategy, Onafriq will launch three corridors in the mentioned markets with Pyypl in the GCC, PayAngel in the UK, and Zazi Transfer in Australia. Powered by Ripple Payments technology, those corridors will bring faster, more efficient, and cost-effective international money transfers between Africa and other regions.\n\nAccording to the press release, Ripple’s technology helps Onafriq solve the inefficiencies and high costs associated with traditional cross-border payment systems. The partnership also helps foster financial inclusion by bringing financial services to underserved communities across the continent.\n\n“Connecting our partners PayAngel, Pyppl and Zazi Transfer with Onafriq over Ripple Payments will bring the benefits of faster and more cost-effective cross-border payments to individuals seeking to send money into Africa from around the globe,” said <PERSON>, SVP, Global Customer Success at Ripple.\n\nPioneering the use of blockchain and cryptocurrency, Ripple is among the first to tackle the multi-trillion dollar inefficiencies in cross-border payments. Ripple’s services are now accessible to hundreds of customers in over 55 countries across 6 continents, with payout capabilities in more than 70 markets.\n\nOnafriq is the largest mobile money movement platform across Africa. The fintech leader has connected over 500 million mobile wallets across 40 African countries. It has over 1,300 payment corridors across Africa.\n\nFollowing a partial victory in its legal battle with the U.S. Securities and Exchange Commission (SEC), Ripple has continuously expanded its network and partnered with new businesses to integrate its cross-payment solutions.\n\nLast month, Bank of America announced its collaboration with Ripple to develop a cross-border payments solution utilizing Ripple’s XRP cryptocurrency.\n\nRipple Upgrades Service to Attract Corporate Clients\n\nRipple recently has announced the rebranding of RippleNet, its crypto-enabled cross-border payments solution, to Ripple Payments. The transformation aims to offer businesses a platform for faster, cost-effective international transactions.\n\nRipple Payments is now accessible to over 70 crypto and traditional payout markets, providing nearly 100% global payout coverage through a simplified onboarding process. The firm holds over 30 licenses, including a MAS Major Payments Institution license and Money Transmitter Licenses across the U.S.\n\nThe integration with the XRP Ledger’s native decentralized exchange (XRPL DEX) improves product performance and reduces barriers to entry into new markets. Currently, Ripple Payments is exclusively offered in the United States.\n\nWith global expansion plans scheduled for next year, the solution is poised to reach a wider audience. The complexity and insufficiency of traditional processes have prompted many institutional players to leverage blockchain technology to tackle the problems.\n\nBlockchain-based solutions can enable faster, cheaper, more transparent, and more secure payments across countries, which will benefit the global economy and consumers worldwide.\n\nIn September 2023, Bloomberg reported that JPMorgan is developing a blockchain-based digital deposit token that aims to speed up cross-border payments and settlements with on-demand liquidity around the clock.\n\nThe token would be issued by JPMorgan and could be used to represent deposits in multiple currencies. It would allow for faster and more efficient cross-border payments, as the token could be settled directly between banks without the need to go through the traditional correspondent banking network."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNTkzLTAyMy0wMTQ3MS050gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Nov 2023 08:00:00 GMT", "title": "Topological analysis of sharp-wave ripple waveforms reveals input mechanisms behind feature variations - Nature.com", "content": "Animals\n\nMale and female mice (Mus musculus) between 2 and 12 months of age were used in this study. All protocols and procedures were performed according to the Spanish legislation (R.D. 1201/2005 and L.32/2007) and the European Communities Council Directive 2003 (2003/65/CE). Experiments were approved by the Ethics Committee of the Instituto Cajal, the Spanish Research Council (CSIC) and Comunidad de Madrid (protocol no. PROEX 162/19). Experiments included in this paper follow the principle of reduction, to minimize the number of animals. Thus, we obtained several sessions (electrode penetration) per animal, which were treated as independent observations. Whenever critical for the scientific question at hand, data are reported by animals. Mice were housed either alone or together with others to secure their well-being (for example, when implants were compromised and/or there was a dominant mouse in the cage requiring separation). They were maintained in a 12-h light–dark cycle (07:00 to 19:00) at 21–23 °C and 50–65% humidity with access to food and drink ad libitum.\n\nStudy design\n\nMice from different lines were randomly assigned to head-fixed and freely moving experiments, as described below. No statistical method was used to predetermine sample sizes, which were similar to those reported previously for this type of study7,29. Data collection was not performed blind to the conditions of the experiments (that is, head-fixed, freely moving, optogenetic stimulation, sleep, awake, tasks) due to execution requirement. For data analysis, detection of SWRs was blind to the topological analysis. All SWR events and recording sessions were used, except for analysis requiring specific inclusion criteria (for example, sleep, rest, awake conditions), which are indicated in the corresponding section.\n\nHead-fixed electrophysiological recordings from awake mice\n\nAdult wild-type C57BL/6 male and female mice were implanted with fixation head bars under isoflurane anesthesia (1.5–2% mixed in oxygen; 400 ml min−1). Two silver wires, previously chlorinated, and screws were inserted over the cerebellum for reference/ground connections. Implants of wires and screws were secured to the skull with light-cured glue (Optibond Universal, Kerr Dental) and secured with dental cement (Unifast LC, GC America). For optogenetic experiments, mice were injected in the same surgical act with adeno-associated viruses (AAVs) to drive expression at specific hippocampal regions, including: (a) CA2 pyramidal cells, which were targeted by injecting AAV5-DIO-EF1a-hChR2-mCherry (1 µl, 3.4 × 1012 viral genomes per ml) in Amigo2-Cre mice51 (now available at The Jackson Laboratory, as Amigo2-cre1Sieg/J, 030215); and (b) CA3 pyramidal cells, which were targeted with PHPeB-CamKII-ChRger2-TS-EYFP-WPRE52 (0.5 µl, 2.6 × 1013 viral genomes per ml at 1:4 dilution in saline) in C57BL/6 mice. Injection coordinates were: for CA2, −1.6 mm anteroposterior and 1.5 mm mediolateral from bregma, depth 1.1 mm; for CA3, −2 mm anteroposterior and 1.75 mediolateral (angle 10°) from bregma, depth 1.9 mm.\n\nAll mice were habituated to the head-fixed setup, consisting of a wheel (20 cm radius) coupled to a stereotactic frame. Habituation sessions (5–7 d, two sessions per day) included handling and placing/removing mice on the apparatus for increasing periods of time (from 5–10 min to more than 1 h). Once mice were habituated to staying in the wheel for long periods, a cranial window was opened over CA1 at 2 mm posterior from bregma and 1.25 mm lateral from the midline in each hemisphere under isoflurane anesthesia. Then, the craniotomy was covered with a low-toxicity silicone elastomer (Kwik-Sil, World Precision Instruments).\n\nRecordings started the day after craniotomy and proceeded over the following 3–4 d. Individual penetrations were considered an independent experimental session, thus providing several sessions per animal. At the end of each recording day, the craniotomy was clean and sealed with Kwik-Sil. Mice returned to their home cage until the next recording day.\n\nFor recordings, we used a 16-channel silicon probe comprising a linear array with 100 µm resolution and 703 µm2 electrode area (A1x16-5 mm-100-703; Neuronexus). For optogenetic experiments, a 105-µm optical fiber was attached to the probe over the 8th–10th electrode from the bottom. Extracellular signals were pre-amplified (4× gain) and recorded with a 16-channel AC amplifier (100×, Multichannel Systems), and sampled at 20 kHz per channel (Digidata 1440, Molecular Devices). Data were acquired with Axoscope (v11). Silicon probes were inserted up to 400–500 µm below the cell body layer of the CA1 region of the dorsal hippocampus to get a laminar profile, including SO, SP, SR and SLM. Relevant LFP events (ripples, multiunit firing, sharp-waves and theta-gamma oscillations) helped to identify penetration.\n\nOptogenetic stimulation\n\nTo evoke SWRs optogenetically, we applied 100-ms square pulses of light at 0.2–0.33 Hz with a 532-nm-wavelength laser (MGL-FN-532-300 mW, CNI Optoelectronics Tech) to stimulate axon terminals of CA2 pyramidal neurons (SO) and CA3 pyramidal neurons (SR and SO) in the CA1 region. In both cases, the fiber remained over the alveus. The laser power was adjusted in each experiment to obtain physiological-like oscillations comparable to spontaneous SWRs (CA2, 200–1,000 µW; CA3, 2–500 µW). Note we did not use the 473-nm-wavelength light (optimal wavelength to activate channelrhodopsin) because it evoked large amplitude nonphysiological oscillations. In a subset of experiments, we tested half-sinusoidal pulses of 50–100 ms and found similar types of events as generated with square pulses.\n\nElectrophysiological recordings from freely moving mice\n\nAdult male and female mice, either wild-type (C57BL/6, in-house) or from the B6.Cg-Tg(Thy1-CO P4/EYFP)18Gfng/J (JAX mice, 007612) line, were implanted with optrodes consisting of four tungsten wires (0.002-inch, bare; 0.004-inch, PFA coated; AM Systems) coupled to optic fibers (200 µm diameter; Thorlabs). The wire tips protruded between 100 µm and 400 µm from the fiber flat surface (located at the alveus) allowing for laminar recordings around the SP and the SR. Implants targeted both hemispheres (anteroposterior: −2.5 mm; mediolateral 2.2 mm from bregma; −1.1 mm depth from the dura). Once the wires were located in their final position, the shanks were glued to the skull with OptiBond Universal (Kerr Dental, Switzerland) and secured with light-cured acrylic resin (Unifast LC, GC Corporation).\n\nIn addition, some adult C57BL/6 wild-type mice were implanted with 32-channel silicon probes (A4X8-5mm 100-200-413; Neuronexus). Animals were anesthetized with isoflurane (2% for induction, 1–2% for maintenance, 500 ml min−1) mixed with oxygen. Probes targeted the right dorsal hippocampus (anteroposterior: −2.0 mm; mediolateral: 1.5 mm from bregma; −1.7 mm depth from the dura). Reference and ground electrodes were placed at the skull above the cerebellum. Once in place, silicon probes were covered by Vaseline and cemented to the skull. A grounded copper mesh cage was built to protect the probes and to ground the system. All mice received doses of enrofloxacin (20 mg per kg body weight), dexamethasone (0.2 mg per kg body weight) and buprenorphine (0.05 mg per kg body weight) subcutaneously on the day of surgery and 24 h later.\n\nAnimals were allowed to recover for at least a week before habituation began. Signals were recorded at 30 kHz with an Open Ephys system using an Intan RHD2132 32-channel head-stage, including a 3-axis accelerometer (Intan Technologies). Data were acquired with Open Ephys GUI 0.4.6.\n\nFreely moving tasks and recordings\n\nThe experimental protocol consisted of four tasks done with at least 3 d of separation between them. Behavioral tasks started after a habituation phase consisting of at least 3 d of handling, followed by 2 d of habituation to the recording box. This box, used throughout the experimental protocol, consisted of a black polypropylene enclosure (28 cm × 22 cm × 42 cm height) with bedding up to 2 cm. Habituation took place in the familiar room where mice were about to run the first round of tasks (room A). Animals were water deprived for 24 h before the tasks. Tasks lasted 20 min each, and SWRs were recorded immediately before (pre) and after (post) during 2 h in the home cage. Habituation mimicked the behavioral tasks, and consisted of 2 h of recording (pre), followed by a 15-min exposition to the home cage with access to water, and then back to the recording box for another 2 h of recording (post).\n\nThe first behavioral tasks consisted of an alternation task in a linear track (LT1; 74 cm long × 7 cm wide, with 12-cm-tall walls) located in the already familiar room A. This linear track had visual (three vertical white stripes on each side) and somatosensory (three polishing paper stripes on the floor) cues in one-half of the corridor. Mice were transported to the maze and allowed to run for reward (4 µl sugar water, 10%) during 15 min. Rewards were automatically delivered through a water valve, which was activated by an infrared sensor controlled by an Arduino system. A reward was delivered only if mice successfully alternated in the maze.\n\nThe second task consisted of free exploration (15 min) of a two-chamber place preference enclosure (TC, chambers of 18 cm × 20 cm and 25 cm height, connected by a 7-cm-wide corridor) located in a room the mice had never visited (room B). This task was the only one that did not require the animals to be water deprived and was used to test for effects of novelty in the absence of training and reward.\n\nFor the next two tasks, mice returned to the familiar room A. The third task was run in a semicircular track (CT; 120 cm long × 7 cm wide, 2-cm-tall walls) with somatosensory cues like in the linear track, where animals had to alternate (15 min). Water port rewards were available at both extremes of the semicircular track.\n\nFinally, the fourth task consisted of a repetition of the first linear track (LT2) over 15 min. Mice carrying wires performed all the tasks in a row. Mice carrying silicon probes were recorded only during the first task (LT1) to provide data for CSD analysis.\n\nSWR detection and feature analysis\n\nFor detecting SWR events, we followed consensus criteria2. First, we removed noisy epochs determined by excessive signal similarity between two separated recording channels (for example, masticatory artifacts). LFP signals from these channels were summed, and epochs deviating >10 times the s.d. from the mean were deleted.\n\nNext, we selected the SP channel from the different shanks and/or wires, which was characterized by the larger ripples and MUA firing, as judged from the maximal power in the ripple (100–250 Hz) and MUA bands (300–400 Hz), respectively. SP signals were filtered (forward-backward-zero-phase finite impulse response filter of order 512 implemented in either MATLAB 2020a and 2021b (MathWorks) between 70 Hz and 400 Hz, and the envelope calculated with a fourth-order Savitzky-Golay filter with a window duration of 33.4 ms, followed by two smoothing moving windows of 2.3 and 6.7 ms, using the ‘movmean’ function. We intentionally left the bottom filter cutoff at 70 Hz to allow for detection of a wide diversity of SWR events, including slow SWRs of 80–100 Hz, similar to those recorded in primates. The upper filter at 400 Hz permitted detection of MUA firing, which is typically used for replay studies. These detection limits are within the ranges reported by consensus2. Importantly, all candidate SWR events were validated (see below). A MUA index was estimated from the area of the spectral power at 300–400 Hz bandwidth.\n\nFor detection, candidate events were detected by thresholding over 2–5 s.d. of the envelope signal. Detected events closer than 15 ms were merged. All candidate events were centered by the minimum value of the waveform closer to the peak of the envelope using a 30-ms window. Finally, an expert validated all candidate events using a custom-made MATLAB GUI. Validation was based on the following criteria: (a) a clear LFP ripple oscillation should be confined to SP, sometimes intermixed with MUA; (b) the ripple should be associated with a sharp-wave at SR. Importantly, all events are detected from non-theta periods.\n\nAnalysis of LFP signals was implemented in MATLAB. To estimate SWR features of validated events, raw signals at the SP (ripples) and the SR (sharp-waves) were filtered in different bands. The amplitude of the ripple was defined from the envelope of the 70–400 Hz filtered SP signal. We deliberately chose a wide frequency range to evaluate potential segregation between events in the fast gamma (80–100 Hz) and the ripple (>120 Hz) bands. The slopes were defined for both the ripples and the sharp-waves using a 1–10 Hz filtered signal from the SP and the SR, respectively. Slopes to (slope-to-peak) and from (slope-from-peak) the peak were defined similarly from both signals using a linear fit. These features were estimated in the ±25 ms window centered on the ripple peak.\n\nThe ripple spectral features were computed from the individual power spectra of the SP channel. The ripple frequency was defined as the power peak (estimated from the spectral bump) in the 70–400 Hz range. To account for the exponential power decay in higher frequencies, we subtracted a fitted exponential curve (‘fitnlm’ from MATLAB toolbox) before looking for the ripple frequency. The spectral entropy was computed from the normalized power spectrum (divided by the sum of all power values along all frequencies) as:\n\n$$Entropy=-\\sum Power(\\,f\\,)\\cdot lo{g}_{2}(Power(\\,f\\,))$$\n\nWhere f is the frequency binned at 10 Hz. The spectral entropy has been described as useful for characterizing normal and pathological SWRs7. The ripple duration was estimated either directly from the envelope of the 70–400 Hz filtered SP signal or from the AUC of the amplitude-normalized 70–400 Hz filtered SP signal, using extended windows of ±100 ms around the peak. To validate estimation of SWR duration, we manually tagged the onset and end of SWRs using three sessions (259 events).\n\nCSD signals were calculated from the second spatial derivative. We included only those sessions meeting spatial criteria (at least eight channels covering continuously from SO to SLM layers). Smoothing was applied to CSD signals for visualization purposes only. Tissue conductivity was considered isotropic across layers. ICA was applied to dissect the different spatial generators53, using the ‘runica’ and ‘icaproj’ functions from the EEGLAB ICA toolbox (https://sccn.ucsd.edu/eeglab/index.php). Each session was analyzed separately, and the ICA initialization matrix was always the identity matrix to reproduce the order of components. After excluding ICA components corresponding to noise and artifacts, the remaining SWR-associated ICA spatial profiles were visually inspected and only those fitting the definition of input current generators were selected (1,789 events). Definitions include: (a) the CA2 SWR generator characterized by a sink at SO and a source at the SP/SR border; (b) the CA3 generator characterized by sinks at SO and SR flanking a source at the SP (contralateral), or those associated to SR sinks and SP sources (ipsilateral); (c) the EC3 generator characterized by a sink at deep SLM layer with a source at SR; and (d) the EC2 di-synaptic inhibition generator characterized by a source at the SLM and a sink at the SR. These definitions were derived from the existing knowledge regarding cell-type-specific input pathways54,55.\n\nHistological analysis\n\nUpon completion of experiments, all mice were deeply anesthetized with sodium pentobarbital (300 mg per kg body weight) and transcardially perfused with PBS (pH 7.4) followed by 4% paraformaldehyde and 15% saturated picric acid in 0.1 PBS. Brains were post-fixed and cut into 50-μm coronal sections in a vibratome (Leica VT 1000S).\n\nSelected sections were washed in 1% Triton X-100 (Sigma) in PBS (PBS-Tx), treated with 10% FBS in PBS-Tx for 1 h, and incubated overnight with the primary antibody solution: rabbit anti-PCP4 (1:100 dilution; Sigma, HPA005792) in 1% FBS in PBS-Tx. After three washes in PBS-Tx, sections were incubated for 2 h at room temperature with the secondary antibody: donkey anti-rabbit Alexa Fluor 647 (1:200 dilution; Invitrogen, A-32795), in PBS-Tx-1% FBS. Following 10 min of incubation with bisbenzimide H33258 (1:10,000 dilution in PBS; Sigma, B2883) for labeling nuclei, sections were washed and mounted on glass slides in Mowiol (17% polyvinyl alcohol 4–88, 33% glycerin and 2% thimerosal in PBS).\n\nMultichannel fluorescence stacks were acquired in a confocal microscope (Leica SP5), with the LAS AF software v2.6.0 build 7266 (Leica), and objectives HC PL APO CS 10.0 × 0.40 DRY UV or HCX PL APO lambda blue 20.0 × 0.70 IMM UV. The pinhole was set at 1 Airy unit, and the following channel settings were applied (fluorophore, laser, excitation wavelength, emission spectral filter): (a) bisbenzimide, Diode, 405 nm, 415–485 nm; (b) EYFP or track autofluorescence, Argon, 488 nm, 499–535 nm; (c) mCherry, DPSS, 561 nm, 571–620 nm; (d) Alexa Fluor 647, HeNe, 633 nm, 652–738 nm. For epifluorescence imaging, a microscope (LEICA AF 6500/7000) with a 10 × 0.3 dry objective and the following filters were used (excitation, dicroic, emission spectral filters): N2.1 (BP515-560, LP590, 580). Fiji software (National Institutes of Health Image; v.2.13.0) was used for subsequent image adjustment and analysis.\n\nQuantification of mCherry+/PCP4+ cells were made in ×20 confocal images at one confocal plane per mouse. For illustration purposes, z-projections (average intensity) were made. Estimation of CA3 infection was achieved in ×10 epifluorescence images, measuring the linear extension along the pyramidal layer for both the EYFP+ region and the complete CA3 region (from CA3c at the hilus to the border with CA2 defined by PCP4). These analyses were made in one or two sections for each animal at around −2 mm anteroposterior from bregma, coinciding with the recordings coordinate.\n\nMethods for estimation of the intrinsic dimension of the waveform space\n\nOur topological method starts by projecting the ripple waveforms in a high-dimensional space determined by the temporal sampling rate. To build the high-dimensional space, we first downsampled SP signals to 2,500 Hz and cut ±25-ms windows around the peak of detected and filtered SWRs (rounded to 127 points). Projecting all SWRs into the 127D space (one dimension per sample, one point per SWR) resulted in a data cloud, which could be recovered into a low-dimensional space. This idea was inspired by early work on unbiased classification of SWRs using unsupervised methods7,10,18. However, instead of predefining the visualization dimension to 2D, we looked for the minimal number of dimensions that preserves the data structure.\n\nWe first compared different methods for estimating intrinsic dimension of the data cloud in the 127D space. To this purpose, we used the R library ‘intrinsicDimension’ in Python (version 1.2.0; https://cran.r-project.org/web/packages/intrinsicDimension/vignettes/intrinsic-dimension-estimation.html). This includes methods such as local expected simplex skewness (ESS Local), dimension estimation via translated poisson distributions (MaxL Local) and local PCA (PCA Local). In addition, we used an ABID method, which does not rely on distances but instead estimates the angle distribution in the vicinity of each point22.\n\nTo validate the different methods, we built the ground truth from several objects in the high-dimensional space, including 2D plane and Swissroll, and a five-dimensional hyperball using codes from the R library. For building a 2D torus, we adapted the R functions to Python. To generate the objects, N points were uniformly distributed along the corresponding surface or volume defined by their parametric equations. They were subsequently embedded in 127 dimensions, with added Gaussian noise (s.d. = 0.01) in all directions of space.\n\nSynthetic SWRs\n\nIn addition to objects, we also simulated synthetic SWRs similarly to experimental events. To generate synthetic ripples, we convolved a sinusoidal signal of a given frequency with a Gaussian signal of a given amplitude and s.d., which defined duration. For each of the three parameters, we used a uniform random distribution of 2,000 samples between the values corresponding to percentiles 5/95% of the real data for the amplitude and the frequency, and between 0.5 and 2 s.d. for duration. Synthetic SWRs were created at the same sampling rate as experimental events. Two different synthetic datasets were built, one with a continuous distribution of frequencies (80–240 Hz); and the other built from three different frequency ranges (80–100 Hz, 130–150 Hz, 190–210 Hz). To make them comparable to experimental SWRs, noise equivalent to the root mean square error of LFP signals was added.\n\nPersistent homology analysis\n\nWe evaluated the topology of the data cloud directly in the high-dimensional space (127D) using the persistent homology package Ripser.py (https://github.com/scikit-tda/ripser.py/). Persistent homology looks for the persistence of n-dimensional simplicial complexes as varying the radius around each data point. The different homology groups are defined from the number of cuts that separate data in pieces of different dimensions (H 0 , H 1 and H 2 ), with the Betti numbers representing the rank of the homology group. In H 0 , the number of connected components that persist after increasing the radius is shown. H 1 quantifies the number of loops. H 2 identifies the number of cavities in the data. To validate analysis, we used objects of known topology (torus, ball, plane, and so on) and synthetic SWR data (continuous and 3-clustered distributions). For this analysis, we excluded outliers as in ref. 56. Analysis was executed in the supercomputer cluster Artemisa (https://artemisa.ific.uv.es/web/content/nvidia-tesla-volta-v100-sxm2/) using >400 Gb RAM. To this purpose, data were bootstrapped 100 times in groups of 3,500 points and results were tested for consistency across different realizations.\n\nDimensionality reduction techniques\n\nTo reduce dimension from the original 127D space to the intrinsic dimension, we used different methods. Isomap was applied using the Python library sklearn.manifold version 0.24.2 (https://scikit-learn.org/stable/modules/manifold.html). We used the UMAP version 0.5.1 (https://umap-learn.readthedocs.io/en/latest/) in Python 3.8.10 Anaconda, which is known to properly preserve local and global distances while embedding data in a lower-dimensional space. A standard PCA was also applied. We found UMAP to be very efficient in computational terms with execution time independent of the number of data points. In contrast, Isomap was computationally costly especially for >10,000 data points. We also tested t-SNE57, which had a bit better computer efficiency than Isomap, but can reduce space only up to 3D. In all cases, we used default values for reconstruction parameters. Algorithms were initialized randomly. We found UMAP to provide robust results independent of initialization. Because the symmetric Laplacian of the graph G is a discrete approximation of the Laplace Beltrami operator of the manifold, the method uses a spectral layout to initialize the embedding. This provides convergence and stability within the algorithm.\n\nFeature space\n\nTo evaluate the advantage of UMAP versus simpler approaches, we constructed a space using the SWR features (frequency, amplitude, entropy and duration). In this 4D space, SWRs will form a point cloud similarly to the waveform space, but they will differ in location in the space coordinates and hence their shapes will be different. Note that that neighbors in the 4D feature space will not necessarily be neighbors in the 4D UMAP space.\n\nStructure index\n\nWe used the SI to quantify the amount of structure the projection of a given feature presents over the data cloud23. We started with a data cloud in which each point has a value of an arbitrary feature. First, we divided the feature values into ten equal bins, and then we assigned each point to a group associated with a feature bin (bin group). Next, we computed the pairwise overlap between bin groups as follows. Given two bin groups, \\({\\mathscr{U}}\\) and \\({\\mathcal{V}}\\), we define the overlap score (OS) from \\({\\mathscr{U}}\\) to \\({\\mathcal{V}}({\\mathrm{OS}}_{{\\mathscr{U}}\\to {\\mathcal{V}}})\\) as the ratio of k-nearest neighbors of all the points of \\({\\mathscr{U}}\\) that belong to \\({\\mathcal{V}}\\) in the point cloud space. That is,\n\n$${\\mathrm{OS}}_{{\\mathscr{U}}\\to {\\mathcal{V}}}\\left({k}\\right)=\\frac{1}{{\\mathscr{U}}\\times k}\\sum _{u\\in {\\mathscr{U}}}{\\rm{|}}\\Big\\{{N}_{u}^{\\,j}\\left({\\mathscr{U}}\\cup {\\mathcal{V}}-\\left\\{u\\right\\}\\right){\\rm{|}}\\;j=1,\\ldots ,k\\Big\\}\\cap {\\mathcal{V}}$$\n\nwhere \\({N}_{u}^{\\,j}\\left({\\mathscr{U}}\\cup {\\mathcal{V}}-\\{u\\}\\right)\\) is the j th nearest neighbor of point u in the set \\({\\mathscr{U}}\\cup {\\mathcal{V}}-\\{u\\}\\).\n\nComputing the OS for each pair of bin groups (\\({{\\mathscr{U}}}_{a}\\) and \\({{\\mathcal{V}}}_{b}\\)) yields an adjacency matrix (A nxn ) whose entry (a,b) equals fg. A can be thought of as representing a weighted directed graph, where each node is a bin group, and the edges represent the overlap (or connection) between them. We do not allow any self-edges in the weighted directed graph so that we set \\({\\mathrm{O{S}}}_{{\\mathscr{U}}\\to {\\mathscr{U}}}\\left({\\rm{k}}\\right)=0\\).\n\nFinally, we define the SI as 1 minus the mean weighted out-degree of the nodes after scaling it:\n\n$${\\mathrm{SI}}\\left({\\mathscr{M}}\\right)=1-\\left(\\frac{2}{{n}^{2}-n}\\,\\mathop{\\sum }\\limits_{a}^{n}\\mathop{\\sum }\\limits_{b}^{n}{A}_{a,b}\\right)$$\n\nThe SI takes values between 0 (random feature distribution, fully connected graph) and 1 (maximally separated feature distribution, non-connected graph). According to this definition, on small datasets and using a small number of neighbors (k), the non-symmetry of k-nearest neighborhoods can yield slightly negative values. Thus, we define the final SI to be the maximum of 0 and the result of the equation above. Importantly, by definition the SI agnostic to the type of structure (for example, gradient and patchy). Instead, it is the weighted directed graph that provides additional insights. Note that this metric can be applied to n-dimensional spaces and any arbitrary cloud distribution (for example, torus, Swissrolls and planes).\n\nImportantly, for quantitative comparison of structural indices from different features, the same set of points should be used. For instance, since CSD values are typically estimated from a subset of recordings meeting methodological criteria, their structural values cannot be directly compared with that of frequency or amplitude for the full dataset.\n\nSpatial correlation analysis\n\nSpatial correlation analysis of SWR features was implemented at 4D by using voxels of different resolutions. To validate the voxel size, a toy model of anticorrelated and random feature distributions was simulated over the 4D experimental SWR embedding. The number of experimental data points per voxels of different sizes (in UMAP coordinates), as well as mean values per feature, were estimated to match the expected correlation of the toy model. The spatial correlation coefficient was calculated using the Pearson correlation between mean voxel features for both the anticorrelated (expected R2 = 1) and random (expected R2 = 0) distributions. The optimal voxel size was defined as the value that best optimized the expected correlation for both distributions at 4D (voxel size of 1 corresponding to about 200 events). Note that this is a linear correlation between two features in 4D voxels, not requiring corrections for multiple dimensions.\n\nTopological categorization of SWRs in the UMAP embedding\n\nWe defined different categories of SWR events in the UMAP embedding by looking at the complementary distribution of different features using Python (3.8.10 Anaconda) with libraries Numpy (1.18.5), SciPy (1.5.4) and Matplotlib (3.3.3). Regions of interest (ROIs) were operationally defined along the topological limits of gradient distribution per feature. To this purpose, we first defined the ranges of interest of the SWR individual features (for example, frequency, amplitude, entropy). For the n ripples with feature values in a predetermined range, their coordinates X n in the UMAP embedding were used to estimate their probability density \\(\\hat{f}({\\boldsymbol{x}})\\) in a 2D grid space, x. For this, we computed the bivariate kernel density estimator making use of the seaborn ‘kdeplot’ function with a Gaussian kernel K and a smoothing bandwidth h determined internally using the Scott method (https://seaborn.pydata.org/generated/seaborn.kdeplot.html). The grid space x had a size of 200 × 200 points evenly spaced from the extreme values of \\({{\\boldsymbol{X}}}_{n}\\).\n\n$$\\hat{f}({\\boldsymbol{x}})=\\frac{1}{n{h}^{2}}\\mathop{\\sum }\\limits_{i=1}^{n}K\\left\\{\\frac{1}{h}({\\boldsymbol{x}}-{{\\boldsymbol{X}}}_{i})\\right\\}$$\n\nThe estimator \\(\\hat{f}({\\boldsymbol{x}})\\) allowed representing the scattered discrete events into a continuous probability density function, which was normalized by the number of ripples n such that the total area under all densities sums to 1. Each point of the grid space x was assigned a density value, which can be considered as a third axis z. To visualize the density values as contours in two dimensions, the probability density function was partitioned in 10 levels of the same density proportion in the z axis. Each curve shows a level set such that a proportion of the total density lies below it, with contour plots of smallest area representing higher density. The iso-contour that best controlled the over-smoothing and under-smoothing of the distribution was selected for each SWR feature. This was often the 6th or 7th contour from highest to lowest density, which represents 60% to 70% of the highest density iso-proportions. Density contours from each feature were then combined, and the overlapping ROIs were identified.\n\nWe also estimated the centroid location of the data cloud by selecting events with different characteristics (for example, percentile values) or SWRs of different origin (for example, sleep/awake; optogenetically evoked, and so on). The distance between centroids or between data points was calculated using the Euclidean distance in UMAP coordinates either in 2D projections or in the reduced 4D space.\n\nFor bootstrapping analysis, we subsampled the embedding by picking up a similar number of events for each session/task and repeating this process 10–100 times, resulting in a mean value per session. The sample size was typically 200, 100 or 50 events depending on the analysis and data availability for each observation unit (session). For shuffling, we randomized the SWR coordinates at the UMAP embedding and repeated the process 100 times, resulting in a mean value per session. Bootstrapping and shuffling were performed per UMAP projection and at 4D.\n\nAlignment of different datasets\n\nTo compare between datasets, we used manifold alignment58. To this purpose, the center of mass of points sharing similar bin values of a given feature (20 bins) was estimated for each manifold in the 4D reduced space. The two point sets {p i } and {p i ’} with i = 1, 2,…, 20; follow a one-to-one relation of the form p i ’ = Rp i + T + N i , where R is a rotation matrix, T a translation vector, and N i a noise vector. Using the algorithm presented by ref. 58, we computed the least squares solution of R and T to calculate the optimal manifold alignment. Once aligned by a given feature, the spatial correlation between features in the two datasets was estimated using the method explained above (UMAP voxels of 1 corresponding to 200 events).\n\nFitting new data into an existing embedding\n\nTo align evoked SWRs into an existing embedding, we used spontaneous SWRs of the optogenetic experiments as the control. To avoid on/off effects of light, we used pulses of 100 ms to isolate a ±25 ms window. The window was centered at the power peak of the evoked ripple. Evoked SWRs were aligned into the existing embedding 1 built with the original spontaneous SWRs. To evaluate correspondence, we built a new embedding 2 by pooling together the original events and the spontaneous SWRs from the optogenetic experiments. This provided a reference location for the distribution of both the original and the new spontaneous events in the new resulting embedding 2. In the third step, we used the coordinates of the original events in embedding 1 versus 2 to estimate the error of the original spontaneous events (alignment error) and those fitted (fitting error). Finally, evoked events were aligned directly into the original embedding and their distance distribution was confronted with the fitting and the alignment error of spontaneous events, which were always significantly lower than the data (distance between centroids of CA3-evoked and CA2-evoked SWRs; P < 0.00001).\n\nTopological decoding of SWR laminar information\n\nTo evaluate the explanatory capability of topological representation of SWRs, we adopted a decoder approach to predict laminar information from SWRs (both in the original space and 4D reduced topological spaces, as well as in the 4D feature space). First, we divided the dataset of SWRs with an associated CSD into the training and test sets through a tenfold cross-validation approach. To ensure independence between training and testing in the 4D reduced space, the UMAP embedding was recomputed for each fold using the training set, and then the test set was projected into the fitted space. We then preprocessed the CSD values by dividing each layer by its standard deviation (without subtracting the mean to avoid losing polarity information). Then, a decoder for each CSD layer was trained using the SWR position in the original space, in the 4D reduced space or in the 4D feature space.\n\nTo determine the goodness of fit of each decoder, we computed the explained variance regression score between the test CSD values and the predicted ones. To determine a confidence chance level, we evaluated the explained variance of shuffled data. The explained variance was calculated using the following formula:\n\n$${\\mathrm{{explained}\\,{variance}}}\\,\\left(\\,y,{y}^{{\\prime} }\\right)=1-\\frac{{var}\\{\\,y-{y}^{{\\prime} }\\}}{{var}\\{\\,y\\}}$$\n\nwhere y is the original (or the shuffled) variable and y’ is the predicted variable.\n\nFollowing this schema, multiple decoders were tested, including Wiener Filter, Wiener Cascade, Extreme Gradient Boosting (XGBoost) and support vector regression, with support vector regression yielding the best performance.\n\nTo predict laminar information of SWR without an associated CSD, we input the SWR topological coordinates either in the original space or in the 4D reduced space to all tenfold decoders, and the average CSD prediction was computed. We confirmed that the median error of predictions across layers was roughly at zero level, supporting no bias of the decoder trained either in the original or in the low-dimensional space.\n\nAn SV classifier was used by leveraging the sklearn library (C-support vector classification). A tenfold approach was used for training the decoder to classify evoked SWRs from CA3 and CA2 based in their position in the 4D UMAP space. The regularization parameter C was set to 1, and a stationary kernel radial basis function was used as suggested by the library. The accuracy classification score (fraction) was used to evaluate the performance of the trained decoders and tested against shuffling data.\n\nSleep scoring and state classification of SWRs\n\nBrain state scoring was implemented semiautomatically. Information from lateral and ceiling cameras was used to validate movement indices calculated from the head-stage accelerometer. The theta/delta signal was estimated from the time frequency spectrum calculated using the ‘bz_WaveSpec’ function from the Buzcode (https://github.com/buzsakilab/). Periods of immobility were separated from periods of running (awake). Immobility periods were subsequently reclassified as ‘rest’ (no movement awake) and ‘sleep’ based on spectral criteria (skewed distribution of spectral values across time epochs). The maximal power in the 1–35-Hz band was used to identify episodes of REM sleep, which helped to define flanked periods of slow-wave sleep. Sensory thresholds during sleep were tested with mild sound stimulation (clicks), which permitted benchmarking of separate periods of rest and sleep during immobility. All SWRs detected in the different periods were classified accordingly.\n\nStandard statistical analysis\n\nStatistical analysis was performed with Python and/or MATLAB. Normality and homoscedasticity were confirmed with the Kolmogorov–Smirnov and Levene’s tests, respectively. The number of replications is specified in the text and figures.\n\nSeveral-way ANOVAs and/or other non-parametric tests were applied for group analysis. Post hoc comparisons were evaluated with Tukey–Kramer two-tailed tests with appropriate adjustment for multiple comparisons. For two-sample comparisons, the one-tailed and two-tailed Student’s t-test or another equivalent test was used. Correlation between variables was evaluated with the Pearson product-moment correlation coefficient, which was tested against 0 (that is, no correlation was the null hypothesis) at P < 0.05 (two-sided). In most cases, values were z-scored (subtract the mean from each value and divide the result by the s.d.) to make data comparable between experimental sessions and across layers.\n\nReporting summary\n\nFurther information on research design is available in the Nature Portfolio Reporting Summary linked to this article."}]