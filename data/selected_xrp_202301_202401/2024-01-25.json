[{"id": 7, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vd3d3LmpwbC5uYXNhLmdvdi9pbWFnZXMvcGlhMjYyNDItaW5nZW51aXR5cy12aWV3LW9mLXNhbmQtcmlwcGxlcy1kdXJpbmctZmxpZ2h0LTcw0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 25 Jan 2024 08:00:00 GMT", "title": "Ingenuity's View of Sand Ripples During Flight 70 - NASA Jet Propulsion Laboratory", "content": "NASA's Ingenuity Mars Helicopter captured this view of sand ripples during its 70th flight, on Dec. 22, 2023. Taken from about 39 feet (12 meters) above the surface, the image shows the widest swath of sandy, relatively featureless terrain the helicopter had ever flown over.\n\nIngenuity navigates by tracking the relative motion of surface features it sees beneath it, using its black-and-white navigation camera. An algorithm used by the navigation system incorporates the relative motion of features such as rocks, boulders, and ridges into the helicopter's calculation of position, velocity, and attitude. The more featureless the terrain is, the harder it is for Ingenuity to successfully navigate across it.\n\nDuring the descent phase of Flight 72, on Jan. 18, 2024, Ingenuity experienced an anomalous landing near the right side of this image. Subsequent imaging from the helicopter's onboard cameras indicated that one of the rotor blades was damaged during touchdown. The team believes that the relatively featureless terrain in this region, which the navigation system was not designed for, was likely the root cause of the anomalous landing.\n\nThe Ingenuity Mars Helicopter was built by NASA's Jet Propulsion Laboratory, which manages the project for the agency. It is supported by NASA's Science Mission Directorate. NASA's Ames Research Center in California's Silicon Valley and NASA's Langley Research Center in Hampton, Virginia, provided significant flight performance analysis and technical assistance during Ingenuity's development. AeroVironment Inc., Qualcomm, and SolAero also provided design assistance and major vehicle components. Lockheed Martin Space designed and manufactured the Mars Helicopter Delivery System. JPL is managed for the agency by Caltech in Pasadena, California.\n\nFor more information about the mission, go to https://mars.nasa.gov/technology/helicopter/."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiXWh0dHBzOi8vdS50b2RheS9yaXBwbGUtY3RvLWVuZHMteHJwLWxvc2luZy1zcGVjdWxhdGlvbnMtaXQtc2hvdWxkLWJlLWltcG9zc2libGUtdG8tbG9zZS12YWx1ZdIBYWh0dHBzOi8vdS50b2RheS9yaXBwbGUtY3RvLWVuZHMteHJwLWxvc2luZy1zcGVjdWxhdGlvbnMtaXQtc2hvdWxkLWJlLWltcG9zc2libGUtdG8tbG9zZS12YWx1ZT9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 25 Jan 2024 08:00:00 GMT", "title": "Ripple CTO Ends XRP Losing Speculations: 'It Should Be Impossible to Lose Value' - U.Today", "content": "Ripple CTO <PERSON> has reassured the XRP community, declaring that with the implementation of the automated market maker (AMM) protocol in XRP Ledger, it should be virtually impossible to incur losses. The recent amendment, currently boasting 28 out of the required 35 votes, has triggered discussions within the community.\n\nAdvertisement\n\nResponding to concerns about the risk of losing XRP, <PERSON> outlined the AMM's unique strategy. Liquidity providers receive tokens specific to the AMM, and the protocol aims to ensure these tokens' value only increases over time, even in periods of volatility.\n\nUnless there's a bug or flaw in the AMM implementation, it is supposed be impossible for a particular invariant to be broken. By the (admittedly weird) standard of that invariant, it is not supposed to be possible to lose.\n\n\n\nWhen you provide liquidity to an AMM depositing into its… — <PERSON> \"<PERSON>\" <PERSON> (@JoelKatz) January 24, 2024\n\nThe cons\n\nHowever, despite the inherent stability, the Ripple CTO acknowledged the potential downsides. While losses are theoretically minimized, users may not see as much profit as if they held the underlying assets during price surges. Furthermore, the AMM does not guarantee a fixed yield, exposing liquidity providers to market uncertainties.\n\n<PERSON> highlighted the advantages, emphasizing the ability to convert volatility into yield and mitigate losses during drops in asset values. However, he cautioned users about counterparty risk, as exposure to at least two assets, including stable ones like USD, could pose challenges.\n\nIn summary, these statements aim to assure XRP holders that the innovation is designed to protect against value losses. However, the potential risks associated with market dynamics and the possibility of unforeseen issues in the AMM implementation or the XRP Ledger are still there.\n\nAs the amendment vote progresses, the XRP community closely watches developments in the pursuit of a more robust and secure trading environment."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMifmh0dHBzOi8vd3d3LmZ4ZW1waXJlLmNvbS9mb3JlY2FzdHMvYXJ0aWNsZS94cnAtbmV3cy1yaXBwbGUtZmlsZXMtYS1uZXctbW90aW9uLWluLXRoZS1vbmdvaW5nLWxlZ2FsLWJhdHRsZS13aXRoLXRoZS1zZWMtMTQwNDcyNNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 25 Jan 2024 08:00:00 GMT", "title": "XRP News: <PERSON><PERSON><PERSON> Files a New Motion in the Ongoing Legal Battle with the SEC - FX Empire", "content": "English English Italiano Español Português Deutsch العربية Français\n\nImportant Disclaimers The content provided on the website includes general news and publications, our personal analysis and opinions, and contents provided by third parties, which are intended for educational and research purposes only. It does not constitute, and should not be read as, any recommendation or advice to take any action whatsoever, including to make any investment or buy any product. When making any financial decision, you should perform your own due diligence checks, apply your own discretion and consult your competent advisors. The content of the website is not personally directed to you, and we does not take into account your financial situation or needs.The information contained in this website is not necessarily provided in real-time nor is it necessarily accurate. Prices provided herein may be provided by market makers and not by exchanges.Any trading or other financial decision you make shall be at your full responsibility, and you must not rely on any information provided through the website. FX Empire does not provide any warranty regarding any of the information contained in the website, and shall bear no responsibility for any trading losses you might incur as a result of using any information contained in the website.The website may include advertisements and other promotional contents, and FX Empire may receive compensation from third parties in connection with the content. FX Empire does not endorse any third party or recommends using any third party's services, and does not assume responsibility for your use of any such third party's website or services.FX Empire and its employees, officers, subsidiaries and associates, are not liable nor shall they be held liable for any loss or damage resulting from your use of the website or reliance on the information provided on this website. Risk Disclaimers This website includes information about cryptocurrencies, contracts for difference (CFDs) and other financial instruments, and about brokers, exchanges and other entities trading in such instruments. Both cryptocurrencies and CFDs are complex instruments and come with a high risk of losing money. You should carefully consider whether you understand how these instruments work and whether you can afford to take the high risk of losing your money.FX Empire encourages you to perform your own research before making any investment decision, and to avoid investing in any financial instrument which you do not fully understand how it works and what are the risks involved."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzI5NTA2L3JpcHBsZS1pcG8v0gEyaHR0cDovL2FtcC5jb2luY29kZXguY29tL2FydGljbGUvMjk1MDYvcmlwcGxlLWlwby8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 25 Jan 2024 08:00:00 GMT", "title": "What Is the Ripple IPO Stock Price and Ripple IPO Date? - CoinCodex", "content": "Ripple is an important fintech company that’s known to many cryptocurrency investors due to its connection with XRP. There has been a lot of talk about the Ripple IPO over the last few years, although the company is yet to officially file for a public listing.\n\nEven though it’s very likely that Ripple will go public in the future, the Ripple IPO date is still unknown. We’ll show you what we know about the Ripple IPO so far, as well as related topics such as Ripple’s valuation and investors.\n\nBefore we dive deeper into the topic of the Ripple IPO, however, let’s first learn some basic facts about Ripple as a company.\n\nKey highlights: Ripple, a fintech company connected to XRP, has not yet filed for an IPO but is expected to go public in the future.\n\nThe company's legal battle with the SEC over whether XRP is a security is a major factor delaying its IPO.\n\nRipple's last known valuation was $15 billion, but its exact value and potential IPO stock price remain speculative.\n\nBuying Ripple stock pre-IPO is possible for accredited investors via private equity platforms, though it's not common.\n\nWhat is Ripple?\n\nRipple is a United States-based financial technology company that provides enterprise-grade solutions for payments, liquidity management, access to working capital, and more. Ripple has also developed a platform for CBDCs (Central Bank Digital Currencies).\n\nSome of Ripple’s solutions are made possible by XRP, a cryptocurrency that was launched in 2012 with the intention of providing an alternative to Bitcoin. Its creators, <PERSON>, <PERSON> McCaleb, and Arthur Britto, aimed to develop a digital currency that was more suitable for payments and had lower energy consumption.\n\nTogether with investor Chris Larsen, the trio established the company that is now recognized as Ripple. Upon its formation, it allocated 80 billion XRP tokens (equivalent to 80% of the maximum supply) to the company. Presently, Ripple retains a significant portion of the XRP supply, although the majority of its holdings are held in special escrow accounts.\n\nWhat’s the Ripple IPO date?\n\nWe currently don’t know the Ripple IPO date. While Ripple CEO Brad Garlinghouse has said on multiple occasions that the company has ambitions to go public, Ripple hasn’t yet officially filed for an IPO. So, at the moment, making a Ripple IPO price prediction is not really possible.\n\nThe biggest roadblock to Ripple’s IPO plans is the company’s ongoing legal battle with the U.S. securities regulator SEC. The SEC alleges that Ripple’s sales of XRP constituted an unregistered securities offering, while Ripple is defending its position that XRP is not a security. The lawsuit has been going on since December 2020, but Ripple hopes that it will conclude sometime in 2024.\n\nIt's worth noting that Ripple scored a major legal win against the SEC in 2023 when the presiding judge ruled that XRP that was sold on exchanges to programmatic buyers cannot be considered a security. In other words, XRP that was distributed to crypto exchange users does not classify as a security, according to the latest judgment.\n\nIn May 2022, Brad Garlinghouse told CNBC that Ripple will “explore the possibility of an initial public offering” after the company’s lawsuit with the SEC is over:\n\n“I think we want to get certainty and clarity in the United States with the U.S. SEC. You know, I’m hopeful that the SEC will not slow that process down any more than they already have.”\n\nIn April 2023, Ripple reportedly held a private “road show,” which is a type of event where a company meets with potential investors and gauges interest in an initial public offering. Reportedly, Ripple’s road show was attended by several institutional investment firms.\n\nCan you buy Ripple stock before IPO?\n\nTechnically, it’s possible to buy Ripple stock before the IPO. There are certain platforms that give investors access to investing in private startups before they go public with an IPO. However, such platforms are typically only available to accredited investors and not retail investors.\n\nThe best way to see whether Ripple stock is currently available for purchase is to monitor multiple private equity investment platforms until a deal for Ripple stock appears. Unfortunately, we cannot provide a guide on how to buy Ripple IPO stock because we can’t find any private equity investment platforms where Ripple stock is currently being offered.\n\nWhat is Ripple’s valuation?\n\nRipple’s last known valuation is $15 billion. Since Ripple is a privately held company, it’s more difficult to determine its valuation than if it was traded publicly. In early 2022, reports came out that Ripple had purchased back shares from its Series C investors in a deal that valued the company at $15 billion.\n\nWhat’s the Ripple IPO stock price?\n\nAs far as the Ripple IPO stock price is concerned, it’s very difficult to make an estimate at the moment since Ripple hasn’t filed for its IPO yet. Once the company’s public offering plans become available, analysts will be able to make better estimates about the Ripple stock price based on the company’s estimated valuation.\n\nRipple’s funding rounds\n\nRipple has raised investments on several occasions, most recently in 2019. The company raised a $28 million Series A funding round in 2015, a $4 million Series A funding round in 2015, a $55 million Series B in 2016, and a $200 million Series C in 2019.\n\nCompanies that have invested in Ripple include notable players such as Andreessen Horowitz, Lightspeed Venture Partners, Standard Chartered, SBI Holdings, CME Group, Santander InnoVentures and several others.\n\nIn addition to receiving capital from investors, Ripple has also sold substantial amounts of the XRP cryptocurrency. The company also sells software and professional services to companies in the financial industry.\n\nRipple has also used its capital to acquire other companies. In May 2023, Ripple acquired Metaco, a Switzerland-based crypto custody firm, for $250 million in cash and Ripple equity.\n\nThe bottom line: The Ripple IPO will most likely happen, but it's unclear when\n\nWhile it seems pretty clear that Ripple will eventually file for an IPO, there’s still a lot of uncertainty at the moment, primarily due to the company’s legal battle with the SEC. Once Ripple’s legal issues are resolved, we’ll likely have a much better idea of Ripple’s IPO timeline, as well as the valuation the company will be pursuing on the public market.\n\nUntil then, the only way to acquire Ripple stock is through private equity investment platforms, which don’t have the best liquidity and are typically only open to accredited investors. For most people who are interested in investing in Ripple, waiting until the company actually goes public is likely a smarter move, as you will be able to make a more informed decision.\n\nIf you’re interested in more topics related to Ripple, make sure to check out our article exploring whether XRP is a good investment."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNDY3LTAyNC00NDk4My160gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 25 Jan 2024 08:00:00 GMT", "title": "A developmental increase of inhibition promotes the emergence of hippocampal ripples - Nature.com", "content": "CA1 activity scales with age while E-I ratio tilts toward inhibition\n\nTo investigate the developmental profile of SPW-Rs, we analyzed a large dataset of extracellular recordings (total duration = 127 h) of local field potential (LFP) and single unit activity (SUA, n = 1057 single units) from the hippocampal CA1 area of non-anesthetized P4-P12 mice (n = 111 mice) (Fig. 1A and Supplementary Fig. 1A, B, C). To verify the quality and stability of recordings over time, we compared the firing rate in the first and second half of the recording, and quantified the proportion of refractory period violations (RPV), as measure of whether the clustered spikes originate from one unit and, correspondingly, show the characteristic biophysical refractory period (Supplementary Fig. 1D, E). We found that the single unit firing rate of the two halves robustly correlated with each other (0.81 and 0.76, <PERSON> and <PERSON><PERSON><PERSON> correlation coefficients, respectively) and that the number of RPVs was low (median = 0.34%, below 1% for 92.81% of units).\n\nFig. 1: Activity patterns in the hippocampal CA1 area of P4-P12 mice. A Schematic of the experimental paradigm82 depicting the location of multi-site electrode in the hippocampal CA1 area (top) and the total number of investigated mice per age group (bottom). B Characteristic SPW and SPW-R events extracellularly recorded in the stratum pyramidale and stratum radiale of the hippocampal CA1 area of P4, P8, and P12 mice displayed together with the wavelet spectrum of the LFP at identical timescale. C Violin plot displaying the percentage of time spent in active periods of P4–P12 mice (n = 110 mice). Generalized (binomial) linear model with logit link function, 95% CI [0.97; 1.05], p < 10−50, two-sided. D Violin plot with a box plot displaying the single unit (SUA) firing rate of P4–P12 mice (1057 single units from 95 mice). Generalized (gamma) linear mixed-effect model with log link function, mouse as random effect, 95% CI [0.22; 0.33], p < 10−22, two-sided. E Log–log plot displaying the normalized aperiodic component of the power spectral density (PSD) in the 25–45 Hz frequency range of P4–P12 mice (n = 109 mice). F Violin plot displaying the 1/f exponent of P4–P12 mice (n = 109 mice). Linear model with segmented fit, breakpoint at P10 95% CI [7.81; 12.19], age slope P4–P10 95% CI [0.12; 0.21], p < 10−11, age slope P10–P12 95% CI [−0.43; 0.24], p = 0.14, two-sided. In (C) and (F), dots correspond to individual animals. In (D) data in the box plot are presented as median (central white circle), interquartile range (thick line) and whiskers (thin lines) extending to the maxima/minima at most 1.5 times the interquartile range. In (E) thin lines correspond to individual animals, and thick lines to the mean PSD per age. In (C), (D), and (F) the shaded area represents the probability distribution density of the variable. In (C), (D), and (F), asterisks indicate a significant effect of age. ***p < 0.001. Hash (#) indicates estimated breakpoints in the linear piece-wise regression. Source data are provided as a Source Data file. Full size image\n\nDuring the first two postnatal weeks, the hippocampal LFP activity undergoes a massive transition, from an almost entirely isoelectric state, only seldom punctuated by SPWs, to continuous oscillatory activity patterns (Fig. 1B). This developmental change was reflected by the monotonic increase over age of the time fraction with active LFP periods (age slope = 1.01, 95% CI [0.97; 1.05], p < 10−50, generalized linear model) (Fig. 1C). The augmented LFP activity was accompanied by a log-linear rise in SUA firing rate (age slope = 0.27, 95% CI [0.22; 0.33], p < 10−22, generalized linear mixed-effect model) (Fig. 1D).\n\nBesides monitoring the main frequency, power, and bandwidth of the periodic component of the power spectrum (PS) of the LFP signal, we analyzed also the aperiodic components of the PS. In particular, the aperiodic component can be expressed by a 1/f χ function, where f is a frequency and χ is the so-called 1/f exponent, which is equivalent to the linear slope of the power spectrum in log-log coordinates. Experimental and theoretical studies have linked the 1/f exponent to the network E-I ratio39,48,49,50,51,52,53. In particular, a steeper slope (i.e., a higher 1/f exponent) is indicative of an E-I ratio that is more tilted towards inhibition. Since inhibition in CA1 area has been reported to increase during the first two postnatal weeks32,36, we hypothesized that the 1/f exponent should augment too. Indeed, we found that the 1/f exponent increased with age and reached constant values around P10 (estimated breakpoint at P10, 95% CI [7.81; 12.19], age slope P4–P10 = 0.16, 95% CI [0.12; 0.21], p < 10−11, age slope P10–P12 = −0.26, 95% CI [−0.43; 0.24], p = 0.14, piece-wise linear model) (Fig. 1E, F). The developmental change in the 1/f exponent was robust across fits in different frequency ranges (Supplementary Fig. 2B), and resembled the dynamics previously reported for the developing mPFC39.\n\nThus, similar to other cortical areas, CA1 shows an age-dependent increase of broadband LFP power and SUA firing rate, accompanied by an increase of the 1/f exponent indicative of E-I ratio tilting towards inhibition.\n\nStrengthening the inhibition of PYRs in a neural network model promotes the emergence of ripples\n\nTo uncover the emergence mechanisms of developmental ripples and investigate the causal effect of inhibition on their generation, we implemented a spiking neural network representing a simplified circuitry of CA1 (Fig. 2A). As previously published9 the network architecture comprised an excitatory population (E), representing PYRs, and an inhibitory population (I), representing parvalbumin-positive INs in a ratio set according to anatomical data to 60 to 119,54. Neurons were modeled as leaky integrate-and-fire units, equipped with conductance-based biexponential synapses. Outgoing synapses from PYRs were modeled as AMPA synapses, while INs were implemented with outgoing GABAergic synapses. The network had no spatial structure, and neurons were randomly connected with population-specific probabilities. All units received input noise and in addition to that, to mimic the excitatory input from CA3, a random subpopulation of PYRs received temporally jittered excitatory currents with Gaussian profiles9,11. The LFP was calculated as a sum of the absolute values of the AMPA and GABA currents on all excitatory cells53,55.\n\nFig. 2: Effect of increasing inhibition on ripples in a neural network model. A Schematic of the spiking neural network model. B Activity patterns simulated in the network with “adult”-levels of I-to-E and I-to-I inhibition. C Reconstructed LFP in 80–250 Hz frequency range (top) and raster plots (bottom) displaying units firing in response to the external input. Left: network with a low level of I-to-E inhibition. Right: network with an “adult” level of I-to-E inhibition. D Mean power spectral density for 30 simulations with increasing I-to-E inhibition. E Change in peak power (top) and frequency (bottom) over increasing levels of inhibition. F, G, H are same as (C), (D), (E) for I-to-I inhibition. In (D), (E), (G), and (H), the red line marks an “adult”-like level of inhibition. In (D), (E), (G), and (H), simulation 1 is the network with the lowest level of inhibition, and simulation 30/35 is the one with the highest. Source data are provided as a Source Data file. Full size image\n\nIn line with previous work9, when constrained with adult CA1 circuitry parameters, upon arrival of the external drive, the network exhibited ripple-like LFP activity and temporally structured firing in both E and I populations (Fig. 2B). To elucidate the contribution of inhibition to the generation of high-frequency oscillation, we systematically varied the magnitude of the inhibitory conductance between either E-I or I-I populations. In the models with adult I-I conductance and varying levels of E-I inhibition, the external drive consistently increased the firing rate in both E and I populations, but ripple-like activity was not present in all simulations. When E-I inhibition was low (Fig. 2C, left), the firing of the E population did not exhibit a clear temporal structure, and the network did not generate fast-frequency LFP oscillations. When the E-I conductance was raised to levels approaching the adult one (Fig. 2C, right), the LFP generated by the model started exhibiting ripple-like high-frequency oscillations (Fig. 2D). Further increasing the E-I conductance resulted in higher power of the ripple-like oscillations (Fig. 2E, top), but the frequency remained largely stable (median frequency = 150 Hz, Fig. 2E, bottom).\n\nVarying the I-I inhibition while keeping E-I inhibition at adult levels had a distinct effect. In these simulations, upon arrival of the external stimulus, the network always exhibited high-frequency oscillations, regardless of the strength of the I-I inhibition (median frequency = 140 Hz) (Fig. 2F, G). However, increasing the strength of the I-I inhibition increased the power (Fig. 2H, top) and the frequency (from 100 to 180 Hz, Fig. 2H, bottom) of the oscillations.\n\nThus, simulations of the CA1 local circuitry model reveal that high levels of E-I inhibition are needed for the emergence of ripple-like activity, while the strength of I-I inhibition mainly modulates its frequency.\n\nRipples emerge halfway through the second postnatal week\n\nThe in silico data above are instrumental for subsequent experimental approaches, since they predict that an increase in inhibition is necessary for the generation of ripple-like LFP activity. Since from P4 to P10 we observed an increase of the 1/f exponent as an indicator of a shift of E-I ratio towards inhibition, we hypothesized that ripples emerge during this developmental phase. To test this hypothesis, we separately evaluated the age-dependent presence of ripples in the frequency and time domain.\n\nIn the fast frequency band (80–200 Hz), we found that the average power exponentially increased over age (age slope = 0.24, 95% C.I. [0.19; 0.28], p < 10−14, generalized linear model) (Fig. 3A, B). However, an increase of power in a certain frequency band could simply reflect a broadband power increase (the offset of the aperiodic component). To disentangle the aperiodic and periodic contributions to the power increase, we used FOOOF, a method that separately parametrizes the two components49 (Fig. 3C, D, and Supplementary Fig. 3A). Using this approach, we found that both components contributed to the power increase in the ripple frequency band. On the one hand, we identified a broadband exponential increase of the PS offset (age slope = 0.25, 95% C.I. [0.19; 0.30], p < 10−12, generalized linear model) (Fig. 3E). On the other hand, from P9 onwards, FOOOF detected peaks in the PS that are indicative of bona fide oscillatory phenomena in the ripple frequency band (Fig. 3F). The power of these peaks slightly increased over age (Fig. 3G, left) (age slope = 0.01, 95% CI [−0.001; 0.02], p = 0.078, linear model), whereas their central frequency (Fig. 3G, right) and width (Supplementary Fig. 3B) showed no age-related changes (age slope = 2.14, 95% CI [−5.35; 9.62], p = 0.55 and age slope = −0.34, 95% CI [−4.88; 4.20], p = 0.88, respectively, linear model).\n\nFig. 3: Developmental profile of fast oscillatory activity in the CA1 area of P4–P12 mice. A Power spectral density (PSD) of hippocampal activity of P4–P12 mice (mean ± s.e.m., n = 111 mice). B Violin plot displaying the power in the 80-200 Hz frequency band across age (n = 111 mice). Generalized (gamma) linear model with log link function, 95% C.I. [0.19; 0.28], p < 10−14, two-sided. C Example of a parameterized power spectrum in the 70–200 Hz frequency range from a P12 mouse. D Representative examples of parameterized power spectra in the 70–200 Hz frequency range recorded from P4, P8, P10, and P12 mice. E Violin plot displaying the power spectrum offset across age (n = 108 mice). Generalized (gamma) linear model with log link function, 95% C.I. [0.19; 0.30], p < 10−12, two-sided. F Distribution of high-frequency peaks over age of P4-P12 mice (n = 108 mice). Gray bars indicate the total number of mice per age group, while blue bars indicate the number of mice with detected peaks. G Characteristics of detected fast frequency peaks. Left, log peak power, and right, peak central frequency. Linear model, left 95% CI [−0.001; 0.02], p = 0.078, right 95% CI [−5.35; 9.62], p = 0.55, two-sided. In (B), (E), and (G), dots correspond to individual animals. In (B) and (E), the shaded area represents the probability distribution density of the variable. In (B) and (E), asterisks indicate a significant effect of age. ***p < 0.001. Source data are provided as a Source Data file. Full size image\n\nFor ripple detection in the time domain (Fig. 4A, B), we separately detected SPWs and ripples. Ripples were considered for further analysis only if they co-occurred with SPWs. In line with previous studies5,30,33,35,46, SPWs were detected by thresholding the LFP signal in the CA1 radial layer (Fig. 4A, B), with a threshold ranging from 3 to 5 standard deviations. As the mean and variance of the LFP signal in our dataset exponentially increased over age, we used an adaptive threshold multiplier comprised between 3 and 5 standard deviations and inversely proportional to the signal variance (Supplementary Fig. 4C). The detection of ripples in the time domain relies on heuristically set thresholds and post hoc manual curation, a process that is inherently prone to errors, as recently highlighted56. To avoid biases, we used two different methods for the detection of ripples in the time domain (Fig. 4A, B, Supplementary Fig. 4A). The first method is based on thresholding the power of the LFP signal after narrow-band filtering in the frequency band of interest. This approach is commonly used to detect ripples in adult animals5,17. The second method57 works without narrow-band filtering. By-cycle relies on splitting the LFP signal into putative cycles and, based on cycle features, on determining whether individual cycles are part of a genuine oscillatory event. Only events identified by both detection methods were considered as ripples in the present study.\n\nFig. 4: Developmental emergence of SPWs and ripples in the CA1 area of P4–P12 mice. A A characteristic example of 3s long LFP traces recorded in the stratum pyramidale and stratum radiale of the hippocampal CA1 area of a P12 mouse (left), and zoomed-in 300 ms long LFP traces of one SPW-R event (right). B LFP traces shown in (A) highlighting the detected ripples (top and middle) and SPW (bottom). C Violin plot displaying the SPWs rate of P4-P12 mice (n = 111 mice). Linear model with segmented fit, breakpoints at 7.21 95% C.I. [6.40; 8.00] and 10.56 95% C.I. [9.94; 11.19]; P4–P7 age slope 95% C.I. [2.23; 5.86], p < 10−4, P7–P10 age slope 95% C.I. [−5.47; −0.77], p < 10−5, P10–P12 age slope 95% C.I. [2.97; 13.51], p = 0.00017, two-sided. D Violin plot displaying the SPW-R rate of P4–P12 mice (n = 111 mice). Linear model with segmented fit, breakpoint at 9.75 95% C.I. [8.69; 10.82]; P4–P10 age slope 95% C.I. [−0.17; 0.16], p = 0.974, P10-P12 age slope 95% C.I. [0.36; 1.23], p = 0.00096, two-sided. E Violin plot displaying the percentage of SPWs with ripples of P4–P12 mice (n = 111 mice). Linear model with segmented fit, breakpoint at 8.97 95% C.I. [7.41; 10.53]; P4–P9 age slope 95% C.I. [−0.02; 0.01], p = 0.47, P9–P12 age slope 95% C.I. [0.01; 0.05], p = 0.0034, two-sided. F Violin plot showing ripple length over age (n = 111 mice). Linear mixed-effects model, mouse as random effect, 95% C.I. [9.33; 15.74], p < 0.0001, two-sided, Tukey correction for multiple comparisons. G Line plot displaying the absolute number of short and long ripples over age. Linear model, long ripples age slope 95% CI [48.93; 189.37], p = 0.00511, short ripples age slope 95% CI [−45.15; 28.55], p = 0. 0.6108, two-sided. H Bar plot showing the percentage of long and short ripples at each age. Linear model, long ripples percentage age slope 95% CI [1.87; 7.87], p = 0.0064, short ripples percentage age 95% CI [−7.87; −1.87], p = 0.0064, two-sided. In (C), (D), (E), and (F), the shaded area represents the probability distribution density of the variable, dots correspond to individual animals, and asterisks indicate a significant effect of age. **p < 0.01, ***p < 0.001. Hash (#) indicates estimated breakpoints in linear piece-wise regression. Source data are provided as a Source Data file. Full size image\n\nAs previously reported27, SPWs were present already at P4, and their rate exhibited a nonmonotonic change over age (Fig. 4C). Both the SPW-R rate and the percentage of SPWs with ripples showed no change from P4 to P10, followed by an increase from P10 to P12 (estimated break-point 9.75 95% C.I. [8.69; 10.82]; P4–P10 age slope = −0.0028, 95% C.I. [−0.17; 0.16], p = 0.974, P10–P12 age slope = 0.80, 95% C.I. [0.36; 1.23], p = 0.00096, piece-wise linear model) (Fig. 4D). (estimated break-point 8.97 95% C.I. [7.41; 10.53]; P4–P9 age slope = −0.0051, 95% C.I. [−0.02; 0.01], p = 0.47, P9-P12 age slope = 0.03, 95% C.I. [0.01; 0.05], p = 0.0034, piece-wise linear model) (Fig. 4E).\n\nThe SPW-R rate during the investigated developmental window did not change for different frequency bands used for ripple detection (Supplementary Fig. 5A–C, Supplementary Table S5). Further corroborating the robustness of the detection approach, the presence of an oscillatory peak in the power spectrum (detected in the frequency domain) significantly correlated with the SPW-R rate (detected in the time domain) for all frequency bands (Supplementary Fig. 5D, Supplementary Table S5).\n\nTo control for the possible effect of body movement on SPW-R rate, we recorded a separate group of P11–12 mice (n = 7 mice) while simultaneously video-monitoring their motor activity. We detected no differences in SPW-R rate between periods with and without movement (condition effect = −0.13, 95% CI [−1.49; 1.23], p = 0.837) (Supplementary Fig. 6).\n\nEven though the SPW-R rate conspicuously increased starting from P10, few SPW-Rs were detected also at earlier ages. However, the average ripple length in the early ages was significantly shorter compared to older ages (median ripple length in P4–P9 32 ms, median ripple length in P10-P12 47 ms, effect of age on ripple length (P4–P9 vs P10–P12) = 12.53 ms, 95% C.I. [9.33; 15.74], p < 0.0001) (Fig. 4F). The length of 30 ms roughly corresponds to maximum three oscillatory cycles at >100 Hz. It is therefore questionable whether such few cycles are sufficient to represent a genuine oscillatory event. In addition, it is possible that population synchrony in the form of short non-rhythmic spike bursts occurring upon the arrival of SPW might have been detected as short ripples due to filtering effects56,58. The number and the proportion of longer ripples, with a duration comparable to those observed in adult animals (3–9 cycles, according to2), markedly increased after P10 (long ripples age slope = 119.2, 95% CI [48.93; 189.37], p = 0.00511, short ripples age slope = −8.30, 95% CI [−45.15; 28.55], p = 0. 0.6108, linear model) (Fig. 4G). (long ripples percentage age slope = 4.874, 95% CI [1.87; 7.87], p = 0.0064, short ripples percentage age slope = −4.874, 95% CI [−7.87; −1.87], p = 0.0064, linear model) (Fig. 4H). (Supplementary Fig. 7A, Supplementary Table S5). Similar to data from the adult HP58, we also detected “solo” ripples (i.e., ripples that did not co-occur with SPWs) in all age groups (Supplementary Fig. 7B), yet these events were excluded from further analysis.\n\nThese data indicate that ripples are detectable in the frequency and time domain from P10 on, when the value of the 1/f exponent stabilizes. Their power but not frequency augments with age, in line with the results of neural network simulations.\n\nDevelopmental changes in the temporal structure of single unit activity during SPWs\n\nSince the interplay between excitatory and inhibitory neurons appears to be critical for ripple generation, we investigated how developmental changes in the temporal structure of neuronal firing underpin the emergence of ripples.\n\nIn agreement with previously published data27,34,35, spiking activity was positively modulated during ~90 % of all SPWs (Fig. 5A). The firing increase was most prominent during SPW-Rs than during SPWs lacking ripples (Fig. 5B). The firing rate during both baseline and SPWs/SPW-Rs was higher in older animals (firing rate during SPW age slope = 0.09, 95% CI [0.06; 0.12], p < 10−8, firing rate during SPW-R age slope = 0.14, 95% CI [0.05; 0.22], p = 0.0014, linear model), yet the relative firing increase was similar in all ages, being at ~3 standard deviations (Fig. 5C, top). In addition, the number of units contributing to SPW events, as well as the reliability of their participation, increased with age (age slope = 0.414, 95% CI [0.296; 0.531], p < 10−9, linear model) (Supplementary Fig. 8A) (age slope = 1.13, 95% CI [0.603; 1.658], p < 10−4, linear mixed-effect model) (Supplementary Fig. 8B).\n\nFig. 5: Single unit activity during SPWs/SPW-Rs in the hippocampal CA1 area of P4–P12 mice. A Percentage of SPWs with modulated population spiking activity (n = 111 mice). B Population firing during SPW-Rs and SPWs shown as mean ± s.e.m (line plots, top) and mean (heatmaps, bottom) (n = 111 mice). Linear model, firing rate during SPW age slope 95% CI [0.06; 0.12], p < 10−8, firing rate during SPW-R age slope 95% CI [0.05; 0.22], p = 0.0014; Linear mixed-effects model, mouse as random effect, condition (presence or absence of a ripple), p < 0.0001, two-sided. C Line plot (left) and heatmap (right) of the population firing rate with respect to the timing of SPW as a function of age in experimental data (top), and as a function of inhibition strength with respect to the timing of external drive in the model with increasing I-to-E inhibition (middle) and in the model with increasing I-to-I inhibition (bottom). On the heatmaps, the horizontal black line marks an “adult”-like level of inhibition. D Line plot displaying the cumulative distribution function of the first spike offset during the SPW (top), and bar plots displaying the percentage of units contributing 1, 2–3, or 4 or more spikes to the SPW (middle and bottom). Linear model, 1 spike age slope p = 0.0176, 2-3 spikes age slope p = 0.01014, >=4 spikes age slope p = 0.00148, two-sided. In (D), arrow down indicates a negative effect of age, whereas arrow up corresponds to a positive effect of age. In (B) and (D), asterisks indicate a significant effect of age or ripple. * p < 0.05, ** p < 0.01, *** p < 0.001. Source data are provided as a Source Data file. Full size image\n\nThe comparable firing increase from P4 to P12 might indicate that the age-dependent emergence of ripples relates to the fine temporal structure of single-unit activity during SPWs. To test this hypothesis, we quantified the timing of the population firing rate peak, the offset of the first spike, and the number of spikes that individual units fired during SPWs. The timing of the population firing peak varied with age, peaking after the SPW maximum amplitude in younger animals and before the SPW maximum amplitude in older mice (Fig. 5C, top). To test whether the different timing is the result of the increasing inhibitory tone, we quantified the population activity peak as we varied the inhibition strength in neural network simulations. In line with this hypothesis, the dynamics in the model network with increasing E-I conductance mirrored the experimentally observed dynamics (Fig. 5C, middle). In the network with increasing I-I conductance, no difference in the peak was observed (Fig. 5C, bottom). The shift in firing rate peak timing with age indicate a stronger inhibition exerted onto PYRs.\n\nA wealth of studies documented that PYRs and INs exhibit a distinct temporal firing patterns during SPW-Rs in adult rodents. PYRs typically fire once per event, and the timing of their individual firing is determined by the magnitude of the pre-SPW hyperpolarization they receive, with larger hyperpolarizations resulting in later spiking59. INs have much higher firing rates, and can contribute with up to a spike to every ripple cycle8,60. We, therefore hypothesized that the developmental increase of IN involvement in ripples might result in more temporarily jittered PYR firing and a higher number of units firing multiple times per SPW event. In line with this hypothesis, at younger ages, the offset of the first spike had a very narrow distribution, as most units fired close to the maximum amplitude of the SPW. In older animals, the variance of the distribution was higher, with more units first engaged at earlier or later time points in the SPW (Fig. 5D, top). Also conforming to the hypothesis of a greater contribution of INs in older mice, the percentage of units firing several spikes per SPW event increased with age (Fig. 5D, middle). This effect was particularly strong (i.e., two-fold increase) in cells contributing with four or more spikes per SPW event (Fig. 5D, bottom).\n\nThus, while during SPWs the neuronal firing increased in all age groups, subtle age-dependent changes in the firing temporal structure reflect a greater contribution of INs to the developmental emergence of ripples.\n\nOptogenetic activation of CA1 pyramidal neurons induces high-frequency oscillations from P10 on\n\nIn adult rodents, optogenetic activation of PYRs in CA1 area with a rectangular light stimulus produces ripple-like induced high-frequency oscillations (iHFO)5,17,60. This kind of stimulus is considered to mimic the incoming wave of excitation that takes place during SPWs5,17. To confirm that ripples emerge around P10, we therefore tested whether an analogous optogenetic approach induced iHFOs in P8-12 mice.\n\nTo this aim, we used a previously developed manipulation protocol30 and specifically transfected CA1 PYRs with a construct carrying the fast and efficient channelrhodopsin-2 mutant (ChR2E123T/T159C) and the red fluorescent protein tDimer2 by in utero electroporation (IUE). An opsin-free control group underwent the same IUE protocol, with a construct carrying only tDimer2. This IUE protocol targets PYRs in the CA1 area of both dorsal HP (dHP) and intermediate/ventral HP (i/vHP) and does not alter the development at embryonic and postnatal stages30,46,47. For these experiments, we pooled data from recordings performed in either the dorsal or ventral/intermediate CA1, depending on the expression localization (Fig. 6A). The number of transfected cells was similar across all ages (Fig. 6A) (age slope = −3.61, 95% C.I. [−10.13; 2.91], p = 0.261, linear model).\n\nFig. 6: Optogenetic activation of CA1 pyramidal neurons modulates activity in the CA1 area of P8-P12 mice. A Schematic of the experimental protocol for optogenetic activation of ChR2-transfected CA1 pyramidal neurons, the total number of investigated mice per age group and the quantification of transfected cells across age (top) and tDimer2- and ChR2-expressing cells (red) in the CA1 of a P11 mouse (bottom). The white line marks the position of the recording electrode (n = 22 mice). B Characteristic example of optogenetically evoked LFP activity extracellularly recorded in the stratum pyramidale of the hippocampal CA1 area of P8 and P12 mice displayed together with wavelet spectrum of LFP at identical time scale (top), and raster plots displaying single unit firing in response to 30 sweeps of optogenetic stimulation (bottom). The LFP plot corresponds to the sweep highlighted in gray. C Population firing rates in response to the optogenetic stimulation of P8–P12 mice (n = 22 mice). D Percentage of stimulation periods with iHFO of P8-P12 mice (n = 22 mice). Generalized (binomial) linear model with logit link function, 95% CI [0.67; 0.81], p < 10−50, two-sided. E Cumulative distribution function of the first spike offset in response to the optogenetic stimulation (top) and percentage of units contributing 1, 2–3, or 4 or more spikes to the population activity during stimulation (bottom) (n = 22 mice). Linear model, 1 spike age slope p = 0.00198, 2–3 spikes age slope p = 0.00198, >=4 spikes age slope p = 0.0129, two-sided. In (C) and (D), data are shown as mean ± s.e.m. In (E), the arrow down indicates a negative effect of age, whereas the arrow up corresponds to a positive effect of age. In (D) and (E), an asterisk indicates a significant effect of age. *p < 0.05, **p < 0.01, ***p < 0.001. Source data are provided as a Source Data file. Full size image\n\nBlue light tapered-in pulse stimulation (473 nm, 2.4–54 mW at fiber tip, 30 sweeps, 120 ms-long pulses) at all light intensities augmented SUA in all investigated mice (Fig. 6B, C, and Supplementary Fig. 9A). For P8 and P9 mice, augmenting laser power evoked a stronger and faster spiking response, yet failed to induce iHFOs. Only from P10 onwards, light stimulation led to the generation of ripple-like iHFOs (age slope = 0.73, 95% CI [0.67; 0.81], p < 10−50, generalized linear model) (Fig. 6D and Supplementary Fig. 9B). At this age, increasing the light intensity augmented the reliability of evoking iHFOs (Supplementary Fig. 9C). Light-evoked iHFO were accompanied by prominent spiking with features highly reminiscent of those observed during recorded SPW-Rs. In particular, P10–P12 mice exhibited a wider distribution of the first spike offset (Fig. 6E, top) and a twofold increase in units spiking four or more spikes per single stimulation period (Fig. 6E, bottom).\n\nThese data confirm that, only from P10 onwards, optogenetic stimulation of PYRs in the CA1 area induces ripple-like iHFOs and spiking signatures similar to those that have been described in adult mice. Conversely, at younger age, the optogenetic stimulation only increased the firing activity in a temporally unstructured manner.\n\nChemogenetic inhibition of CA1 interneurons reduces ripple rate in P11–P12 mice\n\nTo strengthen the in silico evidence highlighting the role of inhibition in ripple generation with a mechanistic experimental approach, we chemogenetically silenced INs in the CA1 area of P11–12 mice. We hypothesized that if inhibition plays a role in generating ripples, this manipulation should decrease their amount.\n\nTo this aim, we transfected Dlx5/6cre INs with inhibitory DREADDs by injecting the construct hM4D(Gi) (AAV9-EF1a-DIO-hM4D(Gi)-mCherry) into the CA1 of P0-P2 Dlx5/6cre mice. Subsequently, we recorded the CA1 activity in vivo before (45 min, baseline) and after administration (45 min) of C21 (3 mg/kg, i.p.), a synthetic activator of DREADDs61 (Fig. 7A). We controlled for potential non-specific effects of C21 administration by including a group of controls comprising Dlx5/6cre-negative mice, Dlx5/6cre-positive mice that did not express the virus, and wild type mice.\n\nFig. 7: Effects of silencing interneurons by inhibitory DREADDs on SPW-Rs in P11-P12 mice. A Schematic of the experimental protocol for chemogenetic silencing of Dlx5/6cre+ interneurons. B Scatterplot displaying the SPW-R rate for control (left, n = 6) and Dlx5/6cre+ (right, n = 9) mice before and after C21 injection. C Violin plot with a box plot displaying the C21 injection modulation index of SPW-R rate in control (n = 6) and Dlx5/6cre+ (n = 9) mice. Black dots correspond to individual animals. Linear model, condition effect 95% CI [−1.01; −0.12], p = 0.017, two-sided, Tukey correction for multiple comparisons. D Violin plot with a box plot displaying the single unit firing rate in control (left, 53 single units from 6 mice) and Dlx5/6cre+ (right, 73 single units from 9 mice) mice before and after C21 injection. Black dots correspond to individual single units. E Scatterplot displaying the mean single unit firing rate for control (left, n = 6) and Dlx5/6cre+ (right, n = 9) mice before and after C21 injection. F Violin plot with a box plot displaying the C21 injection modulation index of single unit firing rate in control (53 single units from 6 mice) and Dlx5/6cre+ (73 single units from 9 mice) mice. Linear mixed-effect model, mouse as random effect, condition effect 95% CI [0.05; 0.71], p = 0.042, two-sided Tukey correction for multiple comparisons. In (B) and (E), blue/gray dots on the scatterplot correspond to individual animals while black dots represent mean values. In (C), (D), and (F), data in the box plot are presented as median (central white circle), interquartile range (thick line) and whiskers (thin lines) extending to the maxima/minima at most 1.5 times the interquartile range. The shaded area on the violin plot represents the probability density distribution of the variable. In (C) and (F), asterisks indicate a significant effect of condition. *p < 0.05. Source data are provided as a Source Data file. Full size image\n\nIn line with our hypothesis, C21-treated Dlx5/6cre-positive mice exhibited a considerable reduction in SPW-R rate compared to the control group (condition effect = −0.56, 95% CI [−1.01; −0.12], p = 0.017, linear model) (Fig. 7B, C). The reduction in detected SPW-Rs took place despite an increase in SUA firing rate after C21 administration (condition effect = 0.38, 95% CI [0.05; 0.71], p = 0.042, linear mixed-effect model) (Fig. 7D–F). This data strengthens the evidence that inhibition is necessary for ripple generation and that this process relies on the balanced interplay between INs and PYRs, and not merely on increased firing rate.\n\nRipples strengthen the coupling between mPFC and HP during SPWs\n\nHippocampal SPW-Rs have been shown to drive a robust increase of activity in the mPFC at adult age62,63,64. Similarly, a robust hippocampal-prefrontal coupling is already present towards the end of the first postnatal week in rodents30,46, yet it is still unknown how the emergence of ripples impacts the developing prefrontal activity. To fill this knowledge gap, we analyzed SUA (n = 2457 single units from 85 mice) simultaneously recorded from the prelimbic subdivision (PL) of the mPFC and the hippocampal CA1 area of non-anesthetized P4-P12 mice (Fig. 8A).\n\nFig. 8: Coupling between the medial prefrontal cortex (mPFC) and HP during spontaneous hippocampal SPWs and optogenetically induced HFO. A Schematic of the experimental paradigm depicting the location of multi-site electrodes in the hippocampal CA1 area and mPFC area (top left), the total number of investigated mice per age group (top right), distribution of unit numbers over age (bottom left) and mice (bottom right). On the bottom right plot, each dot corresponds to an individual animal, and the bar plot indicates the median number of units. B Population firing rates in response to SPW (left) or SPW-R (right) events in the HP CA1, shown as mean ± s.e.m (line plots, top) and the difference between average (mean) activity during SPW and random baseline period/during SPW-R and SPW (heatmaps, bottom). Linear model, firing rate during SPW age slope 95% CI [−0.005; 0.058], p = 0.108, firing rate during SPW-R age 95% CI [−0.14; 0.16], p = 0.909; Linear mixed-effects model, mouse as random effect, condition (presence or absence of a ripple), p < 0.0001, two-sided. C Same as (A) plus optogenetic activation of ChR2-transfected CA1 pyramidal neurons (top), animal number per age group, and the number of single units (bottom). D Example of an activity recorded in the mPFC area of P8 and P12 mice. Top: Representative raster plots displaying single unit firing in response to 30 sweeps of tapered-in pulse stimulation (120 ms). Bottom: Population firing rates in response to optogenetic stimulation shown as mean ± s.e.m. Average overall stimulation periods. E Population firing rates in response to optogenetic stimulation shown as mean ± s.e.m. (line plot, top) and the difference between average (mean) activity during stimulation periods and random baseline periods (heatmap, bottom). In (B) asterisks indicate a significant effect of age or ripple. **p < 0.01. Source data are provided as a Source Data file. Full size image\n\nDuring the entire developmental time window, the prelimbic firing sharply increased during SPWs, yet no age-dependent differences were detected (firing rate during SPW age slope = 0.026, 95% CI [−0.005; 0.058], p = 0.108, firing rate during SPW-R age slope = 0.009, 95% CI [−0.14; 0.16], p = 0.909, linear model) (Fig. 8B). The firing increase in PL was larger during SPW-Rs than during SPWs without ripples (Fig. 8B, right), indicating that ripples tighten the hippocampal-prefrontal coupling.\n\nIn addition, we evaluated the effects of light stimulation of hippocampal CA1 on the prelimbic spiking (n = 696 single units from 19 mice) (Fig. 8C, D). Despite the rather low number of PYRs that are transfected with IUE30, and the fact that not only the i/vHP strongly projecting to PL but also the dHP with weak connectivity is stimulated, light pulses (473 nm, 2.4–54 mW at fiber tip, 30 sweeps, 120 ms-long) evoked spiking activity in PL (Fig. 8D, E). The effect increased with age and at P12, the oldest investigated age, a prominent spiking peak was detected at 100–150 ms onset from light stimulus.\n\nThe data show that both ripples and optogenetically-induced ripple-like activity strengthen the developmental drive from hippocampal CA1 area to PL."}]