[{"id": 12, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vdS50b2RheS94cnAtcmlwcGxlcy1uZXctY2FtcGFpZ24tZW1lcmdlcy1pbi1sb25kb24tdW5kZXJncm91bmTSAUpodHRwczovL3UudG9kYXkveHJwLXJpcHBsZXMtbmV3LWNhbXBhaWduLWVtZXJnZXMtaW4tbG9uZG9uLXVuZGVyZ3JvdW5kP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 14 Dec 2023 08:00:00 GMT", "title": "XRP: <PERSON><PERSON><PERSON>'s New Campaign Emerges in London Underground - U.Today", "content": "In a strategic move to bolster its brand presence, Ripple, the innovative crypto payment and blockchain solutions company, has unveiled a captivating advertising campaign on the London Underground.\n\nAdvertisement\n\nAntony Welfare, the individual overseeing Central Bank Digital Currencies (CBDC) at Ripple, shared detailed images of the eye-catching banners.\n\nThis marketing push follows recent revelations about Ripple's collaboration with payments fintech Onafriq, aimed at enhancing remittance capabilities in Africa and neighboring regions.\n\nA little Ripple goes a long way on the London Underground 👌👌 #Ripple adverts at Bank station .. and yes it’s cold in london hence the outfit!! 🤣 pic.twitter.com/9PT0kOBSBP — Antony Welfare (@AntonyWelfare) December 14, 2023\n\nAt Ripple's annual conference, Swell, in Dubai, plans were unveiled for three new blockchain-based payment corridors connecting Onafriq users in Africa with PayAngel in the U.K., Pyypl in the Gulf Cooperation Council and Zazi Transfer in Australia.\n\nUnderground and above ground\n\nRipple's interest in London as a global financial hub has been a consistent theme, with cofounder <PERSON> expressing the belief that the U.K. will remain a key hub for financial and technological innovation.\n\nThe ambitions extend to securing a position in the U.K.'s register of crypto companies, as evidenced by its application to be recognized as a crypto asset firm by the Financial Conduct Authority.\n\nThis London Underground campaign is part of Ripple's broader strategy to heighten brand awareness and aligns with its expanding global footprint. The ripple effect extends beyond mere advertising, impacting XRP, the token integral to the company's services and operations."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vdGhlY3J5cHRvYmFzaWMuY29tLzIwMjMvMTIvMTQvcmVwb3J0LXJpcHBsZS1zcGVuZHMtMTIwbS14cnAtb3V0LW9mLTIwMG0tdW5sb2NrZWQtZnJvbS1lc2Nyb3ctaW4tZGVjZW1iZXIv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 14 Dec 2023 08:00:00 GMT", "title": "Report: Ripple Spends 120M XRP Out of 200M Unlocked From Escrow in December - The Crypto Basic", "content": "On-chain data from XRPScan shows that R<PERSON>ple has already spent 120M XRP out of the 200M coins kept from this month’s 1B escrow release.\n\nIt is common knowledge that Ripple has been releasing 1B XRP coins from its escrow accounts since December 2017. In a routine procedure, the crypto payment company unlocked 1B XRP on December 1, 2023, before locking back 800M XRP in escrow.\n\n120M Spent Already\n\nOut of the 200M XRP kept this month, on-chain data shows that Ripple has already spent 120M XRP ($75.12M) in less than 15 days.\n\n- Advertisement -\n\nData from XRPScan shows that the company’s main spending address, dubbed “Ripple 1,” sent a total of 120M XRP to an unknown address, ‘rP4X2…sKxv3‘ in two different transactions.\n\nAccording to the data, the first transaction with 60M XRP was made on December 6. Subsequently, the crypto payments company sent another 60M XRP to the same address on December 12, bringing its total outflow to 120M XRP.\n\nDetails of the Transaction\n\nShortly after receiving the first 60M XRP, the unknown address distributed a total of 43,125,000 (43.12 million) XRP in bits to several anonymous addresses. Similarly, the address also distributed 44,520,375 (44.52 million) XRP from the second 60M received, according to on-chain data.\n\nOut of the entire 87,645,375 (87.64 million), XRP disbursed in bits by the initial recipient of Ripple’s escrow funds, a particular unknown address, ‘rhWt2…E32hk,’ stood out.\n\n- Advertisement -\n\nThis address received a total of 85,245,375 (85.25 million) XRP. Interestingly, most of the XRP coins were sent to ‘r4wf7…h4Rzn’ before the owner behind the address deposited the funds in multiple crypto exchanges, including Bitstamp and Bitso.\n\nAt the time of writing, the address holds a paltry balance of 20 XRP, as most of the received funds have been distributed to exchanges and sold to retail investors in blind bid/ask transactions.\n\nXRP Price Still Stable\n\nRipple has always deployed XRP into the market in ways that will not negatively impact the price. Despite offloading 120M XRP from the 200M released escrow funds, the coin’s price has been fluctuating within the $0.6 price level.\n\nAt press time, XRP was changing hands at $0.626, up 3.4% over the past 24 hours."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9iaXRjb2luLWJ0Yy1yb2xsZXJjb2FzdGVyLXJpcHBsZS14cnAtcHJpY2UtcHJlZGljdGlvbnMtc29sYW5hLXNvbC1kZXZlbG9wbWVudHMtYml0cy1yZWNhcC1kZWMtMTQv0gF-aHR0cHM6Ly9jcnlwdG9wb3RhdG8uY29tL2JpdGNvaW4tYnRjLXJvbGxlcmNvYXN0ZXItcmlwcGxlLXhycC1wcmljZS1wcmVkaWN0aW9ucy1zb2xhbmEtc29sLWRldmVsb3BtZW50cy1iaXRzLXJlY2FwLWRlYy0xNC8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 14 Dec 2023 08:00:00 GMT", "title": "Bitcoin (BTC) Rollercoaster, <PERSON><PERSON><PERSON> (XRP) Price Predictions, Solana (SOL) Developments: Bits Recap Dec 14 - CryptoPotato", "content": "TL;DR\n\nBitcoin (BTC) reached over $44,000, a 20-month high, before falling below $41,000, but recently surged again to $43,000, influenced by the Federal Reserve’s decision to keep interest rates unchanged and hint at future rate cuts.\n\nRipple (XRP) also saw significant fluctuations, moving from around $60 to almost $70, then back to $60, with recent bullish trends suggesting potential further increases.\n\nSolana (SOL) made news with its price surge and NFT sales volume, outperforming Ethereum in weekly NFT sales with a recent 53% increase to nearly $68 million, while Ethereum saw a 5% drop.\n\nBitcoin (BTC) Flashes Green Again\n\nThe largest cryptocurrency by market capitalization – Bitcoin (BTC) – has experienced enhanced volatility in the past few weeks. Its price stood at around $38,000 at the start of December, but in the following days, it took the offensive, reaching a 20-month high of over $44,000. However, the asset could not keep up with the momentum, dropping below $41,000 earlier this week.\n\nAnd while bears started envisioning a more severe plunge in the near future, BTC launched another uptrend a few hours ago, soaring to $43,000 (per CoinGecko’s data). The latest surge could be attributed to the FOMC meeting held on December 13.\n\nRecall that the Federal Reserve kept interest rates unchanged, hinting about three rate cuts next year. Such a pivot has been touted as a bullish factor for the cryptocurrency industry, particularly Bitcoin, as it would make borrowing money cheaper and thus allow more investors to deal with risk-on assets.\n\nThose willing to see what other factors could trigger a BTC rally in the near future could take a look at our latest dedicated video below:\n\nIs Ripple (XRP) Ready for a Final 2023 Sprint?\n\nRipple’s native token – XRP – is another digital asset that experienced severe price swings since the beginning of the month. Its valuation was hovering around $60 on December 1, while approximately a week later, it spiked to almost $70. However, the subsequent market correction suppressed its price back to $60.\n\nXRP headed north once again today (December 14), following the bullish trend in the entire sector. Numerous analysts, including the X (Twitter) users EGRAG CRYPTO and JD, believe the token’s price might climb even higher in the short term. The former claimed XRP could surpass $1.20 by New Year’s Eve, whereas the latter envisioned substantial gains should the coin bounce off on weekly close.\n\nThose curious to check how Ripple’s token might perform in the remaining weeks of the ongoing year could take a look at our video below:\n\nHow is Solana (SOL) Doing?\n\nLast but not least, we will focus on Solana and the recent developments surrounding the blockchain protocol. Apart from making the headlines with SOL’s massive price surge in the past several months, many other advancements are worth noting.\n\nAs CryptoPotato reported, Solana’s non-fungible token (NFT) sales volume has marked a bigger increase on a weekly basis than Ethereum. The figure with the former has reached almost $68 million, 53% higher than the observed data a week before, while the latter recorded a 5% drop.\n\nSolana’s NFT sales volume for the last 24 hours currently stands at over $10 million (a 27% increase on a daily basis), while Ethereum has comprised around $14 million."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiYmh0dHBzOi8vd3d3LndmeWkub3JnL25ld3MvYXJ0aWNsZXMvaW5keS1jaGFydGVyLXNjaG9vbHMtZ3JhbnRlZC0xNS15ZWFyLWNvbnRyYWN0cy1ieS1tYXlvcnMtb2ZmaWNl0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 14 Dec 2023 08:00:00 GMT", "title": "Indy charter schools granted 15-year contracts by mayor's office - WFYI", "content": "Eleven Indianapolis charter schools are renewed for the next 15 years — more than double the length of time previously allowed in Indiana. The Indianapolis Mayor’s Office will use a new law to allow schools in four networks to operate until 2039 before it’s required to renew the charter.\n\nThe extended renewals are for K-12 and adult schools part of Purdue Polytechnic High Schools, Paramount Schools of Excellence, Goodwill Education Initiatives, and Herron Classical Schools.\n\nEach school is in the seventh or final year of its initial charter authorization, part of a network with one or more schools is facing renewal. The schools also had to meet standards of the Office of Education Innovation performance framework, which evaluates academics, finance, governance, and external site visit evaluations.\n\n“We're really looking for schools that have demonstrated excellence over at least the balance of their first charter term,” said <PERSON>, the office director. “They also have to meet our rigorous standards in order to be considered for this distinction.”\n\nCharter schools are public schools that are granted a charter to operate by one of several authorizers in Indiana, including the Indianapolis mayor’s office.The maximum charter renewal term was extended by legislators earlier this year, from seven years to 15 years.\n\nCharter advocates who supported the new law say longevity can help charter organizations with securing funding and paying off loans towards building or improving facilities. Charter schools are publicly funded but independently managed and don’t typically receive property tax revenue in Indiana.\n\n“If we were to go out and we needed some financing for capital improvements, it just gives us an opportunity for better rates,” said <PERSON><PERSON><PERSON>, CEO of Purdue Polytechnic Schools. “We are very intentional about being responsible with any funding that we have, and so being able to get better rates and better opportunities, just allows us to pour more back into the classroom.”\n\nThe approval follows an examination by Chalkbeat Indiana into Marion County charter schools, revealing a lack of safeguards in state law to guarantee accountability for both charter schools and their authorizers.\n\nThe 11 schools are still required to receive OEI evaluations annually in order to stay open. School operators are in regular communication with OEI staff, McAlister said.\n\n“This doesn't take away from the kind of accountability that we have for schools,” McAlister said. “We will still review them annually the way that we have always done, and if things aren’t going well, we have the ability to intervene, just as we had before.”\n\nCourtney Hughley, Vice President of Communications for the National Association of Charter School Authorizers, said longer terms don’t carry an inherent risk of less school accountability.\n\n“A longer charter term in no way equals less oversight,” Hughley said. “And we definitely think it's a good practice that when you have a school who has exhibited excellence, that you reward them in some sort of way.”\n\nThe charters for these schools will renew for 15 years starting in the 2024-25 school year:\n\nPurdue Polytechnic High School Englewood (Purdue Polytechnic High Schools)\n\nPurdue Polytechnic High School Broad Ripple (Purdue Polytechnic High Schools)\n\nParamount Brookside (Paramount Schools of Excellence)\n\nParamount Cottage Home (Paramount Schools of Excellence)\n\nParamount Englewood (Paramount Schools of Excellence)\n\nThe Excel Center for Adult Learners (Goodwill Education Initiatives)\n\nThe Excel Center West (Goodwill Education Initiatives)\n\nThe Excel Center University Heights (Goodwill Education Initiatives)\n\nHerron Preparatory Academy (Herron Classical Schools)\n\nHerron High School (Herron Classical Schools)\n\nHerron-Riverside High School (Herron Classical Schools)\n\nContact WFYI Marion County education reporter Sydney <NAME_EMAIL>."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNDY3LTAyMy00Mjk2OS140gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 14 Dec 2023 08:00:00 GMT", "title": "CA3 hippocampal synaptic plasticity supports ripple physiology during memory consolidation - Nature.com", "content": "AMPAR immobilization in the dorsal hippocampus impairs memory consolidation\n\nBased on our previous reports showing that AMPAR immobilization at neuronal surface blocked efficiently post-synaptic expression of hippocampal LTP, we used intra-hippocampal infusions of AMPAR cross-linkers to test for the implication of synaptic plasticity in the process of memorizing a spatial alternation task. For this, mice were cannulated bilaterally above the dorsal hippocampus, and trained for working memory-based Delayed Spatial Alternation task (DSA17). In this task, food-deprived mice are taught to find food rewards in a Y-maze according to a simple rule: reward location is alternating between right/left ending arms (Fig. 1a). A delay of 30 s between consecutive runs is imposed, forcing the animals to remember the previous location before engaging in the upcoming run. In control conditions, a training day of 4 sessions—about 40 trials or reward positions—is sufficient for the animals to decrease their number of errors and reach their maximal performance, which is maintained the following days (Fig. 1d). To mediate AMPAR immobilization in the dorsal hippocampus, we performed bilateral, intracerebral injections of AMPAR cross-linkers (anti-GluA2 IgGs) or their controls (anti-GluA2 monovalent Fabs) at key times of the learning process, with a sufficient delay (>1 h) between IgG injections and behavioral testing to allow efficient AMPARM blockade (Fig. 1b, c): immediately before the first learning session of day 1 (pre-learning), immediately after the end of the first training day (pre-rest), and immediately before the first session of day 2 (pre-test). Our aim was to test the importance of hippocampal AMPARM-dependent plasticity in the encoding, the consolidation and the recall of DSA rule, respectively. Collectively, our results pointed to an impact of AMPAR cross-linking onto memory consolidation. Indeed, pre-learning injections of AMPAR cross-linkers did not impact animal performance on day 1 (pre-learning; Fig. 1d, left and 1e, left), but rather on the following day, characterized by mice’s choices returning to random level (Fig. 1d, left and 1e, right). A similar effect was observed when injections were performed immediately after session #4 (pre-rest; Fig. 1d, middle and 1f), but not if done before the test performed on day 2 (pre-test; Fig. 1d, right and 1g). Thus, the results indicated that memory retrieval was not impacted by AMPAR cross-linking, while pointing that the AMPARM-dependent process occurs during the resting period that is thought to support memory consolidation.\n\nFig. 1: AMPAR surface mobility in the dorsal hippocampus is necessary for the consolidation of a delayed spatial alternation rule. a Schematic of the Delayed Spatial Alternation task. In each session, after an initial forced choice, nine food-rewarded positions—called trials, alternatively right and left—are set. Up to five error runs are permitted per trial before the animal is forced to enter in the rewarded arm. In between runs, the animal is positioned in its home cage for 30 s. After 10 training sessions (session #1 to #10) allocated within a week, animals are alternating almost perfectly (right panels). b Intracerebral bilateral injections in the dorsal hippocampus were performed at three different time points of the DSA training: pre-learning (before session #1), pre-rest (after session #4) and pre-test (before session #5) (orange syringes). c: Two cannulas were implanted above the dHPC, and anti-GluA2-IgGs or control compounds were injected. Template is from “the mouse brain” Paxinos and Franklin. d Behavioral results obtained in the various cohorts expressed as mean error rates. Injections of anti-GluA2 bivalent IgGs (red) or monovalent Fabs (blue) were performed as indicated. A two-way ANOVA was performed to analyze the effect of time and treatment on error rates. Statistically significant interaction between time and treatment was found for pre-learning injections (F(9,308) = 2.204, p = 0.022), but not for pre-rest (F(9,130) = 1.547, p = 0.138) and pre-test (F(9,110) = 1.632, p = 0.115) injections. FaB vs IgG pairwise multiple comparisons were done using the Holm–Sidak method. *p < 0.05, **p < 0.01, ***p < 0.001. e Single animal data are shown for crucial behavioral steps. Memory encoding (left) is achieved within the first day, the error rate being minimal at session #4 (d). Error rates between session #4 and session #5 are similar in FaB-injected control animals, but returned to chance level in IgG-injected mice. f, g Same presentation as in (e), but for consolidation—session #4 vs session #5—in pre-rest (f) and pre-test (g) injected cohorts. Paired t-tests were used. In case that sample distribution was not normal—after the Shapiro–Wilk test—a Wilcoxon ranked test was used. *p < 0.05, **p < 0.01, ***p < 0.001. n represents the number of injected animals. Data are presented as mean values ± SEM. Full size image\n\nAn important question to be addressed is the origin of performance deficits observed on day 2 in pre-learning and pre-rest injected animals. Indeed, an increase in the number or errors can be characterized by various origins such as disorientation, disengagement, bad animal state, up to the complete forgetting of the DSA rule. To further distinguish between these options, we thoroughly analyzed animal behavior during the acquisition of the DSA rule (Fig. 2). As in the other groups using T and/or Y mazes to test for mice cognitive abilities17,18, we noticed that runs can be separated into two groups: those in which the animals were running in the maze with almost constant speed and those in which hesitation can be observed at the crossing point, with significant changes in head orientation and speed, called vicarious trial and error runs or VTE runs (Fig. 2a) that are predictive of accurate good choice, as testified by the difference in the error rates in VTE and non-VTE runs (Fig. 2a, right). Interestingly, probably because DSA rule was not yet clear, animals exhibited more no-VTE runs at the beginning of the learning day (Fig. 2b).\n\nFig. 2: Immobilization of AMPAR in the dorsal hippocampus led to complete forgetting of the acquired DSA rule. a DSA runs can be separated in two groups according to animal hesitation at the middle of the maze. As defined by ref. 18, vicarious trial and errors (VTE) behavior indicates mouse cognitive engagement in the task. Left: examples of no-VTE (green) and VTE runs for mouse 3462. Right: animals performance at session #1 for VTE and no-VTE runs, Mann–Whitney rank sum test, p = 0.002. n are independent animals. b For pre-leaning injections cohorts, number of VTE and no-VTE runs was analyzed along the DSA sessions. Note that upon IgG injections, the number of no-VTE runs at session #5 was similar to session #1. n are independent animals. Data are presented as mean values ± SEM. c Cumulative single animal data for no-VTE run numbers in session #1 and session #5. t-tests were used to compare the evolution of no-VTE runs occurrence between sessions #1 and #5. FaB: p = 0.005; IgG, p = 0.862. n are independent animals. d Choice acuteness during VTE and no-VTE runs was analyzed along the DSA sessions in the pre-learning injection cohorts. The lack of animal performance at session #5 (see Fig. 1d) is associated with a decrease in VTE run accuracy that returned to its initial value in session #1. A milder effect is observed for no-VTE runs. A two-way ANOVA was performed to analyze the effect of time and treatment on error rates for VTE (left) and non-VTE (Right) runs. No statistically significant interaction between time and treatment was found for either VTE runs (F(9,294) = 1.463, p = 0.161) or no-VTE runs (F(9,304) = 1.327, p = 0.222). FaB vs IgG pairwise multiple comparison procedures were done using the Holm–Sidak method. **p < 0.01, ***p < 0.001. Data are presented as mean values ± SEM. e Same presentation as in (c) for choice accuracy for VTE and no-VTE run numbers in session #1 and session #5. t-tests were used. In case sample distribution was not normal—after the Shapiro–Wilk test—a Mann–Whitney rank sum test was used. **p < 0.01, ***p < 0.001. n are independent animals. Full size image\n\nNext, to get insights in the origin of the performance loss of pre-learning treated animals, we examined the occurrence and choice quality of VTE and no-VTE runs (Fig. 2b–e). Surprisingly, the lack of performance of pre-learning IgG-injected animals was associated with reinstatement of initial values, suggesting an apparent amnesia of DSA rule (Fig. 2c–e): (i) in IgG-treated mice, the occurrence of no-VTE runs in session #5 was similar to its initial session #1 value (Fig. 2b, c), as if the animals had to re-acquire DSA rule, rather than being unable to apply the beforehand encoded one; (ii) the error rate of VTE runs in session #5 also increased, returning to the level observed in session #1 (Fig. 2d), suggesting that when attempting to apply the rule, animals behaves randomly, as initially (Fig. 2e, bottom) and (iii) whereas the no-VTE runs performance improved with training and progressively diverged from random choices, they also returned back to their initial values in session #5 in IgG-injected mice (Fig. 2e, top). Importantly, on day 1, performance during VTE runs evolved significantly in IgG- and control-injected animals, suggesting that DSA rule encoding processes are ongoing normally, even in the absence of AMPARM (Fig. 2b–e and Supplementary Fig. 1). Based on this, we propose that a hippocampal AMPARM-dependent mechanism is involved in consolidating memory during resting periods following training, and that dorsal hippocampus (dHPC) AMPAR immobilization leads to a total forgetting of the acquired DSA rule rather than an incorrect encoding or execution of it.\n\nAMPAR immobilization in the dorsal hippocampus impairs ripple physiology during slow-wave sleep\n\nHippocampal ripples are fast oscillations that develop during slow wave sleep (SWS) phase and that are considered offline replays of immediately preceding experiences6,8,19. They are generated in CA2/CA3 regions of the hippocampus, and propagate in CA1 before broadcasting to cortical regions9,20. Interestingly, their interplay with immediately preceding synaptic tagging is unknown, even if specific downscaling and NMDAR-dependent synapse refinement have been reported in in situ preparations11.\n\nThus, we wanted to examine the impact of IgG treatment and/or DSA learning on dHPC ripples (Fig. 3). To achieve that, animals were implanted bilaterally with wire bundles medially to injection cannula (Supplementary Fig. 2a). dHPC Local Field Potentials (LFPs) were recorded for 3 h immediately following Y-maze habituation (“habituation” in Day 1 or D-1) or after the first 4 DSA sessions (“DSA” in Day 1 or D1, Fig. 3). At first, we separated awake and resting/sleeping state in the home cage using animal tracking (mobility, Fig. 3a, top and Supplementary Fig. 2c). Then, slow wave sleep (SWS) and rapid eye movement (REM) sleeping phases were separated using a Theta/Delta ratio defined on hippocampal LFP spectra (Fig. 3a, middle and Supplementary Fig. 2c). REM periods host robust theta oscillations absent in SWS periods, that are characterized by pronounced Delta waves (for a typical example see Supplementary Fig. 2c). Importantly, as expected, SWS periods correlate nicely with the occurrence of hippocampal ripples (see methods, Fig. 3a, bottom and Supplementary Fig. 2c).\n\nFig. 3: Immobilization of AMPAR in the dorsal hippocampus led to learning-dependent impairment of ripple activities. a During resting periods in the home cage, animal tracking and dHPC electrophysiological LFP recordings were performed to temporally define three animal states: awake state is defined by animal mobility, whereas resting states were separated in rapid eye movement (REM) and slow wave sleep (SWS) phases, according to the Delta/Theta ratio of dHPC LFPs. LFP signals were also filtered at 150–250 Hz to extract ripples, the frequency which is closely correlated with SWS periods6. b Bilateral dHPC LFPs were recorded for 3 h resting periods before (habituation, D-1) or after DSA encoding (after session #4, D1). Typical examples of ripple frequency in pre-learning FaB (Top) and IgG-injected (middle) animals, or in IgG-injected animals that were not subjected to DSA learning (bottom). Note the decrease in SWS-ripple frequency in D1 of the IgG-injected DSA-trained animal. c–f Amplitude and frequency of ripples during SWS periods were extracted in D-2, D-1 or D-1 resting periods (as indicated) in four different groups: c day-to-day recordings with no injection nor DSA learning, d before and after control drug injections and DSA learning, e before and after IgG injections and DSA learning, f before and after control IgG injections but no DSA learning. Paired t-tests were used. In case that sample distribution was not normal—after the Shapiro–Wilk test—a Wilcoxon ranked test was used. ns not significant, *p < 0.05, **p < 0.01, ***p < 0.001. n is the number of recorded animals. Data are presented as mean values ± SEM. Full size image\n\nThen we tested if the DSA protocol and AMPAR cross-linking were leading to alterations in ripple frequency and amplitudes (Fig. 3b–f). At first, we tested if our recordings were stable over time. Indeed, and not surprisingly, neither the amplitude nor the frequency of detected ripples differed between two basal consecutive days (Controls D-2 and D-1 recordings, Fig. 3c). Some reports described that spatial learning or retrieval was leading to an increase in dHPC ripple frequency21. However, no noticeable changes in recorded ripples were observed in animals submitted to DSA learning, and injected with control constructs or non-injected (see specific mentions in Fig. 3d). We next tested if the blockade of AMPARM in the dHPC was perturbing ripple physiology in DSA-trained animals (Fig. 3e) or non-trained mice (Fig. 3f). In both cases, IgG injections were done at pre-learning time, i.e., several hours before the recorded resting periods, the only difference being that non-trained animals are solely positioned in the maze for a similar time duration, but with no specific rule to learn (see methods). Surprisingly, we detected a significant impact of IgG injections on ripple amplitude and frequency that were significantly decreased, but this effect was only observed when learning was present (compare Fig. 3e, f). Because ripple inactivation during SWS has been proven to impair spatial memory consolidation22, this decrease in ripple content may explain the lack of consolidation observed in pre-learning IgG-injected animals (Fig. 1d). Thus, our data indicate that post-learning AMPARM-dependent plasticity events in the dHPC support the genesis and strength of hippocampal ripples.\n\nAMPARM-dependent plasticity at CA3 recurrent synapses support ripple activity in situ\n\nMost of what we know about cellular and synaptic contribution to ripple physiology comes from acute in situ preparations in which ripple-like oscillations are spontaneously generated10,12,13. While certain aspects of in vivo ripples are absent, such as their cognitive content6, in situ ripples are still thought to recapitulate most of the in vivo ripple properties and link with in vivo experience6,11.\n\nHere, we wanted to address if an interplay exists between AMPARM-dependent plasticity and ripple physiology. For that, we setup and used in situ hippocampal preparations exhibiting spontaneously ripples and combined them with synaptic “tagging” by inducing LTP at CA3→CA1, CA3→CA3 and DG→CA3 synaptic contacts with or without the presence of AMPAR cross-linkers (Figs. 4 and 5). In optimized in situ preparations13, ripple-like activities—here called SPW-Rs—can be stably and robustly recorded using field recording pipettes positioned in the CA3 and CA1 regions (Fig. 4a–e). To be included, SPW-Rs recordings have to have stable occurrence frequency, showing co-detected CA3 and CA1 events, present a constant delay between CA3 (first) and CA1 (delayed) responses (Fig. 4c, middle), and have a good amplitude matching between both signals (Fig. 4b, c, right). Some other criteria were eventually respected when present: (i) the signal polarity in the CA1 region was dependent of the recording location: positive in the stratum pyramidale, and negative in the stratum radiatum, confirming that incoming CA3 activities were generating a significant synaptic field response in CA1 (Supplementary Fig. 3a), (ii) both evoked and spontaneous SPW-Rs eventually engaged CA3 unitary activities (Supplementary Fig. 3b), (iii) when tested, stimulations in the CA1 stratum radiatum that generated SPW-Rs were interfering with spontaneous SPW-Rs, generating a refractory period (Supplementary Fig. 3c). Importantly, after a 20-min period in the recording chamber, all parameters were stable for more than an hour, allowing the combination of SPW-Rs recordings with high frequency stimulation (HFS) application and/or pharmacological manipulations (Figs. 4 and 5).\n\nFig. 4: Immobilization of AMPAR in the dorsal hippocampus did not affect spontaneous ripple activities in naive in situ preparations. a Spontaneous sharp waves events (SPW-Rs) are recorded in fresh in situ hippocampal preparations (see methods) using extracellular field electrodes positioned in the stratum radiatum (Sr) or the stratum pyramidale (Sp) of CA3 or CA1 regions. b Examples of recorded events. c CA1/CA3 simultaneously recorded events showed a significant delay (left), and correlated amplitudes (right) suggesting their propagation from CA3 to the CA1 area. d, e Single example (d) and averaged (e, n = 7 independent experiments, presented as mean values ± SEM) measures showing the stability of SPW-Rs frequency (top), amplitudes (middle) and delay (bottom) with time. All pharmacological experiments start after respecting a 20-min period required for SPW-Rs stabilization. f, g Effect of AMPAR X-linking on spontaneous SPW-Rs was tested in in situ preparations by pressure injection of anti-GluA2 IgGs. Left: schematic of the experiment. Right: time course of co-detected SPW-Rs frequency. The “pink” area indicates that events are recorded in the IgG-injected area (for IgG condition, red dots). Control condition—no injection—is shown in black dots. h All single experiments and average values after 20 min, in control or after IgG injections. t-tests were used, but failed in finding any difference between groups (CA3 X-link vs ctrl: p = 0.756; CA1 X-link vs ctrl: p = 0.803). The box plot minima and maxima are 25–75% with a center at the median. Whiskers are at 10 and 90%. f–h: n = X/Y with X biologically independent slices examined over Y independent animals. Data are presented as mean values ± SEM. Full size image\n\nFig. 5: Interplay between plasticity induction, spontaneous SPW-Rs and AMPAR mobility in hippocampus in situ. a1 Spontaneous sharp wave ripples were recorded in hippocampal acute slices. After stabilization, application of HFS in the stratum radiatum of CA1 (CA1-sr) was applied to induce LTP at CA3→CA1 synapses (see Supplementary Fig. 4). a2 No major impact of CA1 HFS onto SPW-Rs frequency or amplitude was observed. Data are presented as mean values ± SEM. b1 Top: same presentation as in (a1) for HFS application in CA3-sr. Bottom: typical example of the effect of CA3 HFS on evoked CA3 responses (left, gray line is basal trace), and frequency of spontaneous SPW-Rs (right). b2 Top: same presentation as in (a2). Data are presented as mean values ± SEM. Note the increase in SPW-Rs frequency and amplitude after HFS application. Bottom: a significant delay exists between synaptic potentiation (evoked fEPSPs, black dots) and SPW-Rs frequency increase (gray dots). c Results presented in (a) and (b) are summarized. Data are presented as mean values ± SEM. Top: the effect of CA3 HFS (black dots) depends on the initial SPW-Rs frequency. SPW-Rs frequency evolution with time did not depend on initial frequency (gray dots). Bottom: relative fEPSP amplitude and SPW-Rs frequency 0–5 and 15–20 min after CA3 and HFS applications. Paired t-tests were used. In case that sample distribution was not normal—after the Shapiro–Wilk test—a Wilcoxon ranked test was used. ns not significant, *p < 0.05. (d1/d2) Same presentation as in (b1/b2). Data are presented as mean values ± SEM. 10 min before electrophysiological recordings, IgG injections were performed in the recorded CA3 region (CA3 X-link antibody). Note that all effect triggered by CA3 HFS in control conditions are absent in the presence of IgG. Number of recordings is indicated. e Same presentation as in (c). For intra-group time course comparisons, paired t-tests were used. In case that sample distribution was not normal—after the Shapiro–Wilk test—a Wilcoxon ranked test was used. ns not significant, *p < 0.05, **p < 0.01. For comparisons of similar timing between groups, t-tests were used. In case sample distribution was not normal—after the Shapiro–Wilk test—a Mann–Whitney rank sum test was used. *p < 0.05, **p < 0.01, ***p < 0.001. n = X/Y with X biologically independent slices examined over Y independent animals. Data are presented as mean values ± SEM. Full size image\n\nWe previously showed that AMPAR immobilization at the neuronal surface in the CA1 region impaired LTP expression at CA3→CA1 synapses15. We first aimed to reproduce and extend this finding to other synapses eliciting post-synaptic LTP expression. Interestingly, in the CA3 region, pyramidal neurons receive two major excitatory afferent that are intrinsically different. Mossy fibers originating in the dentate gyrus generate “detonating” synapses expressing a huge rate-dependent facilitation that can be prolongated by a sustained potentiation of presynaptic origin (presynaptic release probability increase23). In contrast, recurrent synapses emitted by distant or neighboring CA3 pyramidal cells are classical Hebbian synapses, expressing post-synaptic LTP23. We thus tested the impact of AMPAR cross-linking on LTP in our in situ “SPW-Rs” preparation (Supplementary Fig. 4). Not surprisingly, we observed that AMPAR X-linking led to an absence of LTP at synapses post-synaptic to CA3 axons (CA3→CA1 and CA3→CA3) but not at DG→CA3 projections, that were solely affected by PKA blockade using Rp-cAMP preincubations (Supplementary Fig. 4).\n\nIn the absence of learning-associated synaptic tagging, AMPAR immobilization did not lead to changes in ripple frequency or amplitude (Fig. 3f). Acute slice preparations from naive mice are often used as models to study molecular and cellular mechanisms of LTP induction and expression at hippocampal synapses24. It is then often considered that in naive mice, no significant synaptic tagging—endogenously triggered LTP—is present. We thus tested the effect of AMPAR cross-linking in SPW-Rs containing naive preparations by locally infusing anti-GluA2 IgGs in CA1 or CA3 stratum radiata (Fig. 4f–h). Notably, the efficacy of this injection procedure on LTP expression was previously validated in CA115, and reiterated here in CA1 and CA3 regions (Supplementary Fig. 4). When compared to basal conditions, these injections had no effect on SPW-Rs frequency or amplitude (Fig. 4f–h and Supplementary Fig. 5). The local effect of IgG injection on amplitude being attributable to the one/two pipette(s) procedure (Supplementary Fig. 5). Therefore, in line with the absence of effect of AMPAR immobilization on basal synaptic transmission15, and in our in vivo data obtained in naive mice (Fig. 3), our in situ results suggest that basal SPW-Rs did not rely on AMPARM in the absence of specific synaptic tagging.\n\nNext, we wanted to test if synaptic tagging—here generated by HFS applications enabling LTP induction (Supplementary Fig. 4)—could modulate SPW-Rs frequency (Fig. 5). Interestingly, CA1 HFS stimulations did not impact SPW-Rs frequency (Fig. 5a, c), indicating that synaptic strength at CA3→CA1 synapses may not be a determinant of SPW-Rs genesis. Strikingly, the same procedure applied at CA3→CA3 recurrent synapses led to a strong increase in SPW-Rs frequency (Fig. 5b, c), prominent in case of low basal SPW-Rs frequency (Fig. 5c). Furthermore, we observed that the effect of HFS on synaptic strength and SPW-Rs frequency seems to have different time courses, the increase in evoked fEPSP amplitude being detectable as early as in the 0–5 min post-tetanic period, whereas the effect on SPW-Rs frequency was not yet present (Fig. 5b2, bottom, 5c). This possibly reflects an ongoing development of synaptic inputs potentiation, along with a progressive rise in CA3 cells excitability. Thus, these results suggested that the reinforcement of CA3→CA3 recurrent synapses increases CA3 region excitability and promotes the generation of ripples.\n\nFinally, we tested if AMPAR immobilization, in addition to abolishing LTP at CA3→CA3 synapses, would also perturb SPW-Rs modulations by CA3 HFS. We applied HFS-CA3 stimulations in SPW-Rs expressing slices in which local infusions of anti-GluA2 IgGs were performed in the CA3 region (Fig. 5d–e). Under AMPAR immobilization, the HFS-associated effect onto SPW-Rs frequency was minimized (Fig. 5d–e), suggesting that their physiology depends on AMPARM-dependent CA3 recurrent synaptic strength. Of note, a contribution of synaptic potentiation at DG→CA3 synaptic inputs is unlikely to contribute to the HFS effect. indeed, we systematically tested for 1 Hz frequency facilitation in our evoked synaptic responses in CA3-sr, retaining only experiments with negligible facilitation, and HFS-triggered plasticity at these synapses is insensitive to AMPAR cross-linking (Supplementary Fig. 4). Thus, we propose that AMPARM-dependent LTP at CA3 recurrent synapses positively controls ripple activity in situ. Together with our in vivo data, our findings suggest that CA3→CA3 synaptic tagging may be triggered during DSA learning, which is important for ripple-mediated consolidation occurring during upcoming sleep phases.\n\nAMPAR mobility in CA3 area is necessary for memory consolidation\n\nBased on our in situ data, we next wanted to restrict AMPAR cross-linking in the CA3 area and evaluate if local impairment of CA3 plasticity would be sufficient in affecting memory consolidation and ripple physiology. However, the antibody-based AMPAR cross-linker strategy lacks spatial and temporal resolution, as in vivo injections often cover large parts of the dHPC15 (Fig. 1). In addition, antibody-associated changes of AMPAR composition25 may be present and can lead to misinterpretation of the data (see discussion). Thus, we used a recently developed approach to cross-link endogenous GluA2-containing AMPAR using biotin/streptavidin complexes16 (Fig. 6a). In Knock-in mice expressing AP-tagged GluA2 subunits, the presence of an exogenous enzyme—BiRAER, brought by viral infections—allow the biotinylation of GluA2-containing AMPAR, that can be cross-linked in the presence of tetravalent neutravidin added in the extracellular space (Fig. 6a). This cross-linking approach that has been validated in vitro and in vivo16 and among other advantages improve spatial resolution through a combination of viral expression and drug delivery via intracerebral cannula (Fig. 6b). We first validated our capacity to target specifically the CA3 area by infusing NA-texasRed (red-tagged tetravalent neutravidin) through cannula implanted above the CA3 regions of BIRA-expressing mice (Fig. 6c). Indeed, red labeling was almost restricted to the CA3 region, in a subpart of the green expressing region (Fig. 6c). Then, we tested the DSA rule expression on day 2 from mice with pre-rest CA3 cross-linking and various control conditions. This time point was chosen because the time course of NA action is not yet ascertained (Fig. 6b). Furthermore, as GluA2 KI animals were slow in learning DSA rule, we mixed sessions #1–2 and #5–6 to get more robust behavioral outcomes. When compared to initial scores (sessions #1–2), error rate of control animals was significantly lower in day 2 tests (sessions #5–6, Fig. 6d), indicating that encoding and consolidation of DSA rule were successfully achieved in the GluA2-AP KI mice. In contrast, error rates at these two time points were close to random values in X-linking conditions (Fig. 6d) a phenotype that is again accompanied by an apparent forgetting of the DSA rule. Indeed, the accuracy of VTE runs improved in control mice, but remained unchanged in the X-linking conditions (Fig. 6e).\n\nFig. 6: An alternative AMPAR X-linking strategy allowing a better targeting of the CA3 area also induced complete forgetting of DSA rule. a We recently developed a new strategy for AMPAR X-linking. Knock-in mice expressing endogenous AP-tagged GluA2 AMPAR subunits can be biotinylated in the presence of BiRAER, and once exported to the cell surface can be immobilized in the presence of external neutravidin (NA, cross-linking condition). b Similar in vivo pharmacological experiments as in Fig. 1d were performed, combining early stereotaxic dHPC injections of AAV-BiRA-GFP or AAV-GFP, and pre-rest injections of saline, mSA or NA. c Histological controls for the mSA and NA staining on top of the AAV-GFP expression. The combination of both injections better restrict AMPAR immobilization to the CA3 area. Template is from “the mouse brain” Paxinos and Franklin. d Mean error rates were compared between sessions #1–2 and sessions #5–6 to evaluate the retention of the DSA rule upon various pharmacological treatments (as indicated by color coding). Paired t-tests were used. ns not significant, ***p < 0.001. Data are presented as mean values ± SEM. e The error rate in VTE runs was reported in sessions #1–2 (filled dots) and 5–6 (empty dots) for control (left) and cross-link (right) groups. t-tests were used. ns not significant. **p < 0.01. n = biologically independent animals. Full size image\n\nTo confirm that the lack of consolidation is associated with impairment of ripples physiology, we combined this novel cross-linking method with dHPC recordings using the same recording methodology as for IgG experiments (Fig. 3). Ripples occurring during SWS were extracted and counted in habituation (D-1) and after DSA (D1) sessions (Fig. 7). As the last recording started 1 h after drug delivery, it was important to control for unspecific effects of drug actions. Of note, NA application on GFP only and saline or mSA delivery on BiRA-expressing dHPCs were not leading to changes in ripple frequency, as for DSA consolidation (Fig. 6). However, NA delivery in BiRA-expressing CA3 areas was associated with a pronounced decrease in ripple frequency, reminding the effect observed upon pre-learning and pre-rest IgG injections (Fig. 3). Therefore, through utilization of two different cross-linking approaches, we demonstrated that AMPARM in the CA3 region is necessary for memory consolidation and support ripple physiology during slow wave sleep."}]