[{"id": 16, "url": "https://news.google.com/rss/articles/CBMisgFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vcHJpbWUvZmludGVjaC1hbmQtYmZzaS9yZWxpYW5jZS1pbmR1c3RyaWVzLXBsYW4tdG8tZW50ZXItaW5zdXJhbmNlLWFuZC1hbWMtYnVzaW5lc3Nlcy1jb3VsZC1jcmVhdGUtcmlwcGxlcy1pbi10aGUtbWFya2V0L3ByaW1lYXJ0aWNsZXNob3cvOTk2MTk3OTIuY21z0gG2AWh0dHBzOi8vbS5lY29ub21pY3RpbWVzLmNvbS9wcmltZS9maW50ZWNoLWFuZC1iZnNpL3JlbGlhbmNlLWluZHVzdHJpZXMtcGxhbi10by1lbnRlci1pbnN1cmFuY2UtYW5kLWFtYy1idXNpbmVzc2VzLWNvdWxkLWNyZWF0ZS1yaXBwbGVzLWluLXRoZS1tYXJrZXQvYW1wX3ByaW1lYXJ0aWNsZXNob3cvOTk2MTk3OTIuY21z?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Reliance Industries’ plan to enter insurance and AMC businesses could create ripples in the market - The Economic Times", "content": "There’s only one buzz in the insurance industry circles — Reliance Industries Limited’s (RIL) foray into the sector. Though the details of the blueprint of RIL’s plan about its forthcoming foray into insurance (both life and non-life) and asset management will be unveiled only early next month, the industry grapevine is that it’s likely to be a game-changer. The hot topic is: what will be the focus of Jio Financial Services Limited (JFSL)?"}, {"id": 18, "url": "https://news.google.com/rss/articles/CBMisgFodHRwczovL20uZWNvbm9taWN0aW1lcy5jb20vcHJpbWUvZmludGVjaC1hbmQtYmZzaS9yZWxpYW5jZS1pbmR1c3RyaWVzLXBsYW4tdG8tZW50ZXItaW5zdXJhbmNlLWFuZC1hbWMtYnVzaW5lc3Nlcy1jb3VsZC1jcmVhdGUtcmlwcGxlcy1pbi10aGUtbWFya2V0L3ByaW1lYXJ0aWNsZXNob3cvOTk2MTk3OTIuY21z0gG2AWh0dHBzOi8vbS5lY29ub21pY3RpbWVzLmNvbS9wcmltZS9maW50ZWNoLWFuZC1iZnNpL3JlbGlhbmNlLWluZHVzdHJpZXMtcGxhbi10by1lbnRlci1pbnN1cmFuY2UtYW5kLWFtYy1idXNpbmVzc2VzLWNvdWxkLWNyZWF0ZS1yaXBwbGVzLWluLXRoZS1tYXJrZXQvYW1wX3ByaW1lYXJ0aWNsZXNob3cvOTk2MTk3OTIuY21z?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Reliance Industries’ plan to enter insurance and AMC businesses could create ripples in the market - The Economic Times", "content": "There’s only one buzz in the insurance industry circles — Reliance Industries Limited’s (RIL) foray into the sector. Though the details of the blueprint of RIL’s plan about its forthcoming foray into insurance (both life and non-life) and asset management will be unveiled only early next month, the industry grapevine is that it’s likely to be a game-changer. The hot topic is: what will be the focus of Jio Financial Services Limited (JFSL)?"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vdGhlY3J5cHRvYmFzaWMuY29tLzIwMjMvMDQvMjAvZGF2aWQtc2Nod2FydHotc2F5cy1yaXBwbGUtd2FzLWNyZWF0ZWQtdG8tZGlzdHJpYnV0ZS14cnAtYXMtYnJvYWRseS1hcy1wb3NzaWJsZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "<PERSON> Says “<PERSON><PERSON><PERSON> Was Created to Distribute XRP as Broadly as Possible” - The Crypto Basic", "content": "<PERSON> corrects a misconception about <PERSON><PERSON><PERSON>’s primary objective.\n\n<PERSON>, CTO at Ripple, has taken to Twitter to debunk a misconception regarding the Silicon Valley-based blockchain company.\n\nA Twitter user known as “Sc<PERSON> are bad” quoted an old tweet, saying, “XRP was created to sell to retail.” In response to the comment, a blockchain enthusiast named <PERSON><PERSON><PERSON><PERSON> asserted that “Ripple was created to sell XRP to retail.” The user added that Ripple’s co-founder <PERSON> left the company because of how XRP was sold to retail investors.\n\nIMO, Ripple was created to sell XRP to retail. I am not sure if you and <PERSON> are saying this in combat of the phrase \"XRP was never for retail\". But there was an obvious disagreement regarding XRP being sold to retail, the way it was. So <PERSON> left.https://t.co/CG6fGdYvC3 — <PERSON><PERSON><PERSON>l – DG – PwntNub (@pwntnub) April 19, 2023\n\n- Advertisement -\n\nRipple CTO Debunks the Misconception\n\nCommenting on the development, <PERSON> quickly corrected the misconception to prevent it from spreading like wildfire. He clarified that Ripple was created to distribute XRP as broadly as possible and generate enough revenue from the coin’s sales to sustain its operations.\n\nAt least in my view, Ripple was created to distribute XRP as broadly as possible and, with luck and if necessary, be able to generate enough revenue from sales (or things like sales) of XRP to sustain its operations. <PERSON> <PERSON> \"<PERSON><PERSON>atz\" <PERSON> (@JoelKatz) April 19, 2023\n\nThere has been a growing misconception about Ripple’s XRP sales recently. As reported earlier, a Twitter user asserted that the leading blockchain actively sells XRP and that these sales account for 98% of the company’s revenue.\n\n- Advertisement -\n\nResponding to this so-called “fact,” Schwartz confirmed the claim as accurate only in the broader definition of “sell.”\n\nEven though XRP sales account for most of Ripple’s revenue, the company is limited to only two options regarding its XRP holdings- to sell or hold the coins, Schwartz said.\n\nThe Ripple CTO added that most people complain that the company “holds too much [XRP].”\n\nSince Ripple holds an enormous amount of XRP and controls its issuance, many believe the cryptocurrency is more centralized than other coins like Bitcoin.\n\nInterestingly, Ripple is making significant efforts to dispel these claims by offloading many of its XRP holdings over the past months.\n\nTo further illustrate Ripple’s XRP sales, the blockchain company sold $226.31 million worth of XRP in Q4 2022. It also sold $310.68 million worth of the coin in the previous quarter.\n\nIn Ripple’s third-quarter earnings report, the blockchain company showed that its XRP holding dropped below 50 billion or 50% of the total supply.\n\nNotably, Ripple’s XRP sales are made to cater to users of its On-Demand Liquidity (ODL), a solution that leverages cryptocurrency for cross-border settlements and payments."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNTkzLTAyMy0wMTMwNi030gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Inhibitory control of sharp-wave ripple duration during learning in hippocampal recurrent networks - Nature.com", "content": "(a) Spatial information for significantly tuned interneurons, broken down by both region and subtype. Immobility-active CB/SATB1- neurons were silenced during locomotion and were thus not considered in this analysis. Data from 188 cells in n = 9 mice. Significance values over individual violin plots show the results of (one-way) signed-rank tests. (b) Within-day spatial stability of all interneurons, broken down by both region and subtype. Plotted as in A. Significance values over individual violin plots show the results of (one-way) signed-rank tests. Significance values over pairs of violin plots show results from (two-way) ranked-sum tests (only significant differences are shown). Data from 152 cells in n = 9 mice. (c) Across-day spatial stability of all interneurons, broken down by both region and subtype. Plotted as in A. Significance values over individual violin plots show the results of (one-way) signed-rank tests. Significance values over pairs of violin plots show results from (two-way) ranked-sum tests (only significant differences are shown). Data from 142 cells in n = 9 mice. (d) Left: Summary of the fraction of positively tuned CA2 interneurons, broken down by subtype (PVBC: 0.343 ± 0.341, AAC: 0.455 ± 0.267, SOM: 0.405 ± 0.339, CCK: 0.685 ± 0.228, CB/SATB1 + : 0.0 ± 0.0). CA2 CCK cells were more likely to be positively spatially tuned cells than CA2 CB/SATB1+ cells (one-way ANOVA with post-hoc multiple testing correction, p = 0.048). Immobility-active CB/SATB1- neurons were silenced during locomotion and were thus not considered in this analysis. Each data point represents an imaging session. PVBC data from 20 imaging sessions, AAC data from 22 sessions, SOM data from 14 sessions, CCK data from 9 sessions, and CB/SATB1 + data from 2 sessions; data from n = 9 mice. Data reported as mean ± s.d. Right: Same data as on the left, but for negatively tuned CA2 interneurons (PVBC: 0.304 ± 0.261, AAC: 0.270 ± 0.293, SOM: 0.690 ± 0.226, CCK: 0.685 ± 0.228, CB/SATB1 + : 0.75 ± 0.25). Significant differences in the fraction of negatively tuned CA2 interneurons by subtype are indicated (one-way ANOVA with post-hoc multiple testing correction: p(CCK-PVBC) = 0.0075, p(CCK-AAC) = 0.0024, p(SOM-PVBC) = 0.0012, p(SOM-AAC) = 0.001). Immobility-active CB/SATB1- neurons were silenced during locomotion and were thus not considered in this analysis. Each data point represents an imaging session. PVBC data from 20 imaging sessions, AAC data from 22 sessions, SOM data from 14 sessions, CCK data from 9 sessions, and CB/SATB1 + data from 2 sessions; data from n = 9 mice. Data reported as mean ± s.d. (e) Example of 100 seconds of real interneuron activity during locomotion and the predicted activity from a GLM. The predicted activity for each cell was calculated based on 4 predictor behavioral variables: velocity, position, licking, and water delivery (see Methods). (f) Comparison of the GLM weights for each cell for the velocity predictor, separated by both subtype and region. Only cells for which the velocity predictor in the model was a significant predictor are shown. Significance values over individual violin plots show the results of (one-way) signed-rank tests. Significance values over pairs of violin plots show results from (two-way) ranked-sum tests. Only significant differences are shown. Data from 170 cells from n = 9 mice. (g) Same data as shown in F, but now for the licking predictor. Only cells for which the licking predictor in the model was significant are shown. No significance at the subtype or region level was found. Data from 20 cells in n = 9 mice. (h) Same data as shown in F, but now for the reward predictor. Only cells for which the reward predictor in the model was significant are shown. No significance at the subtype or region level was found. Data from 56 cells from n = 9 mice. (i) Same data as shown in F, now for the position predictor. Only cells for which the position predictor in the model was significant are shown. Significance values over pairs of violin plots show results from (two-way) ranked-sum tests. Only significant differences are shown. Data from 45 cells from n = 9 mice.\n\nSource data"}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNDY3LTAyMy0zNzczNi140gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 20 Apr 2023 07:00:00 GMT", "title": "Hippocampal sharp wave ripples underlie stress susceptibility in male mice - Nature.com", "content": "Approvals\n\nAll experiments were performed with the approval of the animal experimental ethics committee at the University of Tokyo (approval number: P29-14) and the committee on animal experiments at Tohoku University (approval number: 2022 PhA-004) and in accordance with the NIH guidelines for the care and use of animals.\n\nSubjects\n\nC57BL/6 J mice (10–15 weeks old) with preoperative weights of 22–35 g were used as intruder mice that received social defeat (SD) stress or as control mice. Mice were housed on a 12-h light/12-h dark schedule with lights off at 8:00 PM. In addition, CD-1 mice (more than 13 weeks old) with weights of 40–50 g were used as resident mice that imposed social defeat stress. They were individually housed and maintained on a 12-h light/12-h dark schedule under housing conditions at 23 ± 1 °C with relative humidity of 50 ± 5% with lights off at 8:00 AM. All mice were purchased from SLC (Shizuoka, Japan).\n\nGeneration of AAV shRNA\n\nRecombinant adeno-associated viruses (AAVs) were generated by triple transfection of the 293 AAV cell line (AAV-100; Cell Biolabs, Inc., San Diego, CA) with AAVdj rep-cap, pHelper from the AAV-DJ Helper Free Packaging System (VPK-400-DJ; Cell Biolabs, Inc.) and pAAV-U6-shRNA-CMV-mCherry using PEI-Max (24765; Polysciences, Inc., Warrington, PA). AAV vectors were purified using the AAVpro Purification Kit All Serotypes (6666; Takara Bio Inc., Shiga, Japan). Virus titers were determined by qPCR using the AAV2 ITR primer pair62, THUNDERBIRD Next SYBR qPCR Mix (QPX-201; TOYOBO, Osaka, Japan), and the LightCycler qPCR 2.0 system (DX400; Roche, Basel, Switzerland). The AAV shRNA hairpin sequences were Calb1-shRNA 53: 5′-GCT GGA TGC TTT GCT GAA AGA CTC GAG TCT TTC AGC AAA GCA TCC AGC TTT TT-3′ and Scramble-shRNA63: 5′-GCA TAC GGT CAA TCC TCA ACA CTC GAG TGT TGA GGA TTG ACC GTA TGC TTT TT-3′.\n\nSurgical procedures\n\nIn all surgeries, mice were first anesthetized with isoflurane gas (1–3%) and a midline incision was made from the area between the eyes to the cerebellum. Craniotomies with a diameter of 1.0–1.4 mm were created above target brain regions using a high-speed drill, and the dura was surgically removed.\n\nFor tissue collection, a unilateral stainless guide tube (3.4 mm length, 1.1 mm inner diameter (I.D.), 1.5 mm outer diameter (O.D.)) was implanted into the right ventral hippocampus (3.1 mm posterior and 3.6 mm lateral to bregma, depth of 3.5 mm). A wire with the same length as the guide tube and with the tip having a cross shape (1.1 mm diameter) was then inserted into the guide tube. By rotating the wire and pulling it out, the small brain area attaching to the tip was removed. This procedure cleaned the area above the tissue to be collected. Next, a wire with the tip having a cross shape (1.1 mm diameter) that protruded from the guide tube by 1 mm was inserted into the guide tube. By rotating the wire and pulling it out, a tissue sample of the ventral hippocampus was collected.\n\nFor virus injection, glass pipettes (ϕ = 30–40 µm) were bilaterally inserted into both sides of the ventral hippocampus (3.1 mm posterior and ±3.6 mm lateral to bregma, depth of 3.5 mm) and AAVdj-shCalb1-mCherry (1.14 × 1014 vg/ml)53, AAVdj-shScramble-mCherry (1.07 × 1013 vg/ml)63, and AAVdj-Calb1OE-GFP (3.4 × 1012 vg/ml) dissolved in phosphate-buffered saline (PBS; pH 7.4) was injected at a rate of 100 nl/min for 3 min. After the injection, the injection pipette was left in place for 1 min and then raised 50 µm and again left in place for 5 min.\n\nFor drug injection, guide tubes (inner diameter = 0.34 mm and outer diameter = 0.5 mm) were bilaterally implanted into both sides of the dorsal (1.8 mm posterior and ±1.8 mm lateral to bregma, depth of 1.5 mm) or ventral (3.1 mm posterior and ±3.6 mm lateral to bregma, depth of 3.5 mm) hippocampus. To prevent drying in the guide tubes, a dummy plastic cannula with a diameter of 0.33 mm was inserted into the guide tube until the muscimol injection described below.\n\nFor optogenetic experiments, glass pipettes (ϕ = 30–40 µm) were bilaterally inserted into both sides of the ventral hippocampus (3.1 mm posterior and ±3.6 mm lateral to bregma, depth of 3.5 mm) and AAV5-CaMKII-Arch3.0-EYFP or AAV5-CaMKII-EYFP (the UNC Vector Core service) dissolved in phosphate-buffered saline (PBS; pH 7.4) was injected at a rate of 100 nl/min for 3 min. After the injection, the injection pipette was left in place for 1 min and then raised 50 µm and again left in place for 5 min. After removing the pipette, optical fibers (ϕ = 200 µm) were implanted at the same coordinates.\n\nFor LFP recordings without spikes, a tetrode assembly created using a 3D printer (Form 2, Formlabs)64,65,66 was directly implanted into (i) the right ventral hippocampus (3.1 mm posterior and 3.6 mm lateral to bregma, depth of 3.5 mm, 3 tetrodes), (ii) both the right dorsal hippocampus (1.5 mm posterior and 2.0 mm lateral to bregma, depth of 1.5 mm, 3 tetrodes) and ventral hippocampus (3.1 mm posterior and 3.6 mm lateral to bregma, depth of 3.5 mm, 3 tetrodes), and (iii) the ventral hippocampus (4 tetrodes) and the amygdala (0.8 mm posterior and 3.0 mm lateral to bregma, depth of 4.4 mm, 3 tetrodes). For all recordings, an electrode was additionally implanted into the somatosensory cortex (i and ii; 3.1 mm posterior and 3.6 mm lateral to bregma, a tetrode at a depth of 0.5 mm) or prefrontal cortex (iii; 2.0 mm anterior and 0.8 mm lateral to bregma, a stainless-steel screw electrode on the brain surface) to serve as reference electrodes. Two stainless-steel screws were implanted in the bone above the cerebellum to serve as ground electrodes. The tetrodes were constructed from 17-μm-wide polyimide-coated platinum-iridium (90/10%) wires and the tetrodes tips were plated with platinum to lower electrode impedances to 150–300 kΩ at 1 kHz.\n\nFor spike recordings, an electrode assembly that consisted of 6 independently movable tetrodes was stereotaxically implanted above the ventral hippocampus (3.1 mm posterior and 3.6 mm lateral to bregma) at a depth of 2.0 mm. The tetrodes were advanced to the targeted brain regions over a period of at least one week following surgery (for more detail, see Adjusting electrode depth as described below). For some mice, an additional incision was made at the incised neck area, and one EMG electrode was sutured to the dorsal neck muscles.\n\nFor closed-loop feedback stimulation, in addition to implantation of a tetrode assembly into the ventral hippocampus, stainless bipolar electrodes with an impedance of 3 MΩ were implanted into the right side of the ventral hippocampal commissure (0.7 mm posterior and 0.5 mm lateral to bregma, depth of 1.7 mm).\n\nAll device was secured to the skull using stainless-steel screws and dental cement. After all surgical procedures were completed, anesthesia was discontinued, and the mice were allowed to awaken spontaneously. Following surgery, each animal was housed in a transparent Plexiglas cage with free access to water and food for at least one week.\n\nSocial defeat stress\n\nC57BL/6J mice were exposed to SD stress1,37. At least 1 week before beginning the social defeat experiment, all resident CD-1 mice more than 13 weeks of age were singly housed in a home cage. The bedding in the resident area was left unchanged during the period. First, resident CD-1 mice were screened as aggressors for social defeat experiments by introducing an intruder C57/BL6J mouse that was specifically used for screening into the home cage during three 3-min sessions on 2 subsequent days. Each session included a different intruder mouse. CD-1 mice were selected as aggressors in subsequent experiments based on three criteria: during the three 3-min sessions, (i) the mouse attacked in all three sessions, (ii) the latency to initial aggression was less than 20 s, and (iii) the above two criteria were met for the two consecutive days. This screening was especially crucial to select strong aggressor mice that could trigger behavioral deficit with a single stress load. After screening, an intruder C57BL/6J mouse was exposed to SD stress by introducing it into the home cage of the resident mouse with a light intensity of 20 lux for a 7–10-min interaction, termed a SD mouse. The interaction period was immediately terminated if the SD mouse had a wound and bleeding due to the attack, resulting in interaction periods of 7–10-min. After the physical contact, the SD mouse was transferred to a half compartment of a home cage of a resident mouse (42.5 cm × 26.6 cm × 15.5 cm) that was evenly divided by a Plexiglas partition (0.5 cm × 41.8 cm × 16.5 cm) with perforated holes, each with a diameter of 10 mm. The second resident mouse was placed in the opposite compartment for the following 24 h; this allowed the SD mouse to have sensory contact with the resident mouse without physical contact. Over the following 10-day period, the intruder mouse was exposed to a new resident mouse so that the animals did not habituate the same residents, unless otherwise specified. This general protocol of SD stress was applied to mice that underwent virus injection as in Fig. 1h and Supplementary Fig. 1b.\n\nIn Fig. 1a–f, mice that underwent vHC tissue sample collection were subjected to SD stress every 2 days for 10 days to reduce postsurgery physical damage. Similar to daily SD stress (e.g. Fig. 1h and Supplementary Fig. 1b), this protocol was sufficient to yield susceptible and resilient mouse types.\n\nIn Figs. 2–5, mice received a surgery and were implanted with recording device, injection cannula, or optical fibers on the brain. These mice were subjected to SD stress for 1 day and placed back to their own home cage and singly housed without aggressors. The next day, the mice were tested in a SI test (as described later). To more strongly induce the effects of 1-day stress responses, all SI tests were performed in the same room and same location and with the same experimental condition so that SD mice more strongly recalled an episode of SD stress.\n\nWhen electrophysiological recordings were performed from the SD mouse, the mouse was first transferred to its home cage and electrophysiological signals were obtained for several hours after SD stress. After recordings, the SD mouse was transferred in the opposite compartment of the second resident home cage for the following 20–24 h, as described above.\n\nIn a non-SD group, C57BL/6J mice were housed for 10 min in the same home cage but subjected to no physical contact with a CD-1 mouse by partitioning the cage with a transparent wall (termed non-SD mice).\n\nAs a control experiment, mice were placed in a novel open field (39.3 cm × 39.3 cm) without any aggressor mice for 10 min (Supplementary Fig. 2d–f).\n\nImmediately after receiving SD stress, some mice were placed on a rotarod apparatus with a constant rotation speed of 3 rpm and forced to walk for 30 min (walking group).\n\nSocial interaction test\n\nSocial interaction (SI) tests were performed in a square-shaped box (39.3 cm × 39.3 cm) enclosed by walls 27 cm in height with a light intensity of 20 lux. A PLA-mesh cage (6.5 cm × 10 cm × 24 cm) was centered against one wall of the arena during all social interaction sessions. Each SI test included two 150-s sessions (separated by an intersession interval of ~30 s) without and with the target CD-1 mouse present in the mesh cage; these sessions were termed no target and target sessions, respectively. In the no target session, a C57BL/6J mouse was placed in the box and allowed to freely explore the environment. The C57BL/6J mouse was then removed from the box. During the ~30-s break between sessions, the target CD-1 mouse was introduced into the mesh cage. The design of the cage allowed the animal to fit its snout and paws through the mesh cage but not to escape from the cage. In the target session, the same C57BL/6J mouse was placed beside the wall opposite to the mesh cage. In each session, the time spent in the interaction zone, a 14.5 cm × 26 cm rectangular area extending 8 cm around the mesh cage was analyzed. The social interaction (SI) ratio, or simply termed as “social interaction” in figures, was computed as the ratio of time spent in the interaction zone in the presence of the target (target session) to the time spent there in the absence of the target (no target session).\n\nLocal infusion of muscimol\n\nDrug injection was performed in a home cage. The dummy cannula was removed from the guide tube and replaced by a plastic injection cannula with a diameter of 0.34 mm so that the tip of the injection cannula reached above the hippocampus. The other side of the injection cannula was connected by polyethylene tubing to a 50-μl syringe mounted in an infusion pump (KDS LEGATO101, Muromachi, Japan). Through the injection cannula, 1.0 μg/μl muscimol dissolved in phosphate-buffered saline (PBS; pH 7.4) was then infused for 5 min into the hippocampus at a rate of 100 nl/min. After the infusions were completed, the injection cannula was left in place for 5 min. Then, the dummy cannula was again inserted into the guide tube. During the muscimol injection procedure, the animals did not show any sign of stress or discomfort. As a control experiment, saline was injected with the same procedure.\n\nPhotostimulation\n\nPhotostimulation was delivered to both sides of the ventral hippocampus. Continuous 11-min green light laser pulses (λ = 532 nm; 1–2 mW; COME2-LB473/532, Lucir, Japan) with intervals of 1 min were repeatedly applied ten times (in total, 2 h). The intervals were set to avoid phototoxicity due to long continuous photostimulation.\n\nAdjusting electrode depth\n\nThe mouse was connected to the recording equipment via Cereplex M (Blackrock), a digitally programmable amplifier, close to the mouse’s head. The output of the headstage was conducted via a lightweight multiwire tether and a commutator to the Cerebus recording system (Blackrock), a data acquisition system. Electrode turning was performed while the mouse was resting in a pot placed on a pedestal. The electrode tips were advanced slowly 25–200 μm per day for 10–20 days until spiking cells were encountered in the cell layer of the ventral hippocampus, which was identified on the basis of local field potential (LFP) signals and single-unit spike patterns. Once the tetrodes were adjacent to the cell layer, as indicated by the presence of multiunit activity, tetrodes were settled into the cell layer for stable recordings and recording commenced as described below.\n\nElectrophysiological recording\n\nFor recording electrophysiological signals, the EIB of the microdrive array was connected to a Cereplex M digital headstage (Blackrock Microsystems), and the digitized signals were transferred to a Cereplex Direct data acquisition system (Blackrock Microsystems). Electrical signals were sampled at 2 kHz and low-pass filtered at 500 Hz. The unit activity was amplified and bandpass filtered at 750 Hz to 6 kHz. Spike waveforms above a trigger threshold (60 μV) were time-stamped and recorded at 30 kHz in a time window of 1.6 ms.\n\nRecordings were continuously performed for up to 1 h before SD stress, 10 min during SD stress, and for up to 4 h after SD stress. In 3 of the 10 mice, a recording cable was disconnected during the 10-min SD session due to severe attack from the resident mouse. In that case, immediately after applying SD stress, the intruder defeated mouse was located inside a mesh-caged cylinder (ϕ = 10 cm, height = 20 cm) located at the center of an open field (39.3 cm × 39.3 cm × 27 cm). The resident mouse was then placed outside the cylinder and allowed to freely explore within the open field with interacting the intruder mouse through the cylinder for 5 min, termed a forced interaction period, which allowed us to stably record neuronal activity that encoded SD episodes without noise due to physical attacks. These spike signals were also considered to encode SD experiences and integrated into our analysis.\n\nVideo monitoring\n\nIn the SD period, all behavioral patterns of mice were monitored at 15 Hz using a video camera attached to the ceiling. In the SI test, the animal’s moment-to-moment position was recorded at 15 Hz using the CMOS camera (MCM4350, Gazo) attached on the ceiling. The frame rate of the movie was downsampled to 3 Hz, and the instantaneous speed of each frame was calculated based on the distance traveled within a frame (~333 ms). Animal’s trajectories in images were manually extracted by Image J1.45.\n\nClosed-loop electrical stimulation\n\nClosed-loop electrical stimulation upon online detection of vHC SWRs was performed using extension codes implemented on the Cerebus recording system (Blackrock) and custom-made C + + codes48. A tetrode implanted into the vHC was chosen and the envelop of its band-pass (100–400 Hz) filtered LFP signals was computed in real time. vHC SWRs were detected online when the envelop exceeded the detection threshold of 3 standard deviations above the mean computed from LFP signals during stop periods in the rest box. At the time of vHC SWR detection, an electrical pulse with a duration of 100 μs and an amplitude of 160–200 μA was applied to the vHC. Stimulation rate was limited to a maximum of 4 Hz. For delayed control stimulation, stimulation was applied with a latency of 250 ms after the onset of ripple detection so that the stimulation occurred outside the detected vHC SWRs.\n\nCresyl violet staining and immunohistochemistry\n\nThe mice were overdosed with urethane, perfused intracardially with 4% paraformaldehyde in PBS and decapitated. After dissection, the brains were fixed overnight in 4% PFA and equilibrated with 20% and 30% sucrose in phosphate-buffered saline for an overnight each. Frozen coronal sections (50 μm) were cut using a microtome, and serial sections were mounted and processed for cresyl violet staining. For cresyl violet staining, the slices were rinsed in water, counterstained with cresyl violet, and coverslipped with hydrophobic mounting medium (Marinol). The positions of all tetrodes were confirmed by identifying the corresponding tetrode tracks in histological tissue by using an optical microscope (All-in-One Fluorescence Microscope BZ-X710, Keyence Corporation, Osaka, Japan). In some animals, the tetrode tips were further advanced after recordings by passing the cell layer to confirm no cells were observed. In these cases, we estimated the location of tetrode tips at recording time based on records of the advancement of tetrodes during turning periods. For immunohistochemistry, the slices were rinsed with PBS and then permeabilized in 100 mM PBS with 0.3% Triton X-100 and 5% bovine serum albumin (BSA, 01863-77; Nacalai Tesque, Kyoto, Japan) at room temperature for 60 min. The slices were then incubated with a primary rabbit anti-NeuN antibody (1:2000, ab177487, Abcam, UK), primary goat anti-Calbindin antibody (1:400; Calbindin-Go-Af1040, Frontier Institute Co., Ltd., Hokkaido, Japan) in 100 mM PBS with 0.3% Triton X-100 and 5% BSA for one overnight period at 4 °C. After rinsing with PBS, they were then labeled with a secondary anti-rabbit IgG antibody Alexa 647 (1:1000; Thermo Fisher Scientific, Tokyo, Japan), anti-Goat IgG antibody Alexa 488 (1:1000; Thermo Fisher Scientific, Tokyo, Japan) in 100 mM PBS with 0.3% Triton X-100 and 5% BSA for 90 min. Images were acquired at a Z-depth interval of 0.775 μm using a confocal laser-scanning microscope (A1-HD25; Nikon, Tokyo, Japan) with an objective lens (×10, 0.45 NA; ×20, 0.75 NA).\n\nMicroarray experiments and data analysis\n\nThe collected tissue sample was immediately frozen in liquid nitrogen. Total RNA was prepared from each of the mice using a RNeasy Plus Mini Kit (Qiagen Inc., Valencia, CA, USA). The samples with a concentration of 100 pg/μl were used for analysis. RNAs were applied to microarray analysis performed by Affymetrix GeneChip Mouse Clariom S arrays (Kurabo Industries Ltd., Osaka, Japan). The prepared microarrays were preprocessed with Transcriptome Viewer (Kurabo Industries Ltd., Osaka, Japan). Raw signals were transformed to the log2 scale and then normalized. In cases where the probes for a given gene yielded a p-value (detection p-value) greater than 0.05, the gene was excluded from further analysis. Differentially expressed genes (DEGs) were identified with a false discovery rate-adjusted p-value cutoff of 0.05 (one-sided Student’s t-test). The expressions of significant DEGs were compared between susceptible and resilient mouse types. The functions of DEGs were classified by gene ontology (GO) analysis.\n\nReal-time quantitative reverse transcription‑polymerase chain reaction\n\ncDNA synthesis by ReverTra Ace (TOYOBO Co., Ltd.) was followed by qPCR with PowerUp SYBR Green Master Mix (Thermo Fisher Scientific) performed in 8-strip tubes on the Roche LightCycler® 96 thermocycler. The reaction conditions: 95 °C for 120 s, followed by 40 cycles (95 °C for 15 s, 60 °C for 60 s). Commercially available primers for the target gene, Calb1 (forward: 5′-CTTGCTGCTCTTTCGATGCCAG-3′, reverse: 5′-GTTCCTCGGTTTCGATGAAGCC-3′) and internal control gene, Actb (forward: 5′- GGCTGTATTCCCCTCCATCG −3′, reverse: 5′- CCAGTTGGTAACAATGCCATGT −3′) was obtained from Eurofins. The results were calculated by 2−ΔΔCt method. Calb1 expression levels were normalized by those from stress resilient mice in each experiment.\n\nSpike sorting\n\nSpike sorting was performed offline using the graphical cluster-cutting software MClust4.3.0267. Rest recordings before and after the behavioral paradigms were included in the analysis to assure recording stability throughout the experiment and to identify hippocampal cells that were silent during behavior. Clustering was performed manually in 2D projections of the multidimensional parameter space (i.e., comparisons between waveform amplitudes, the peak-to-trough amplitude differences, waveform energies, and the principal components of waveforms, each measured on the four channels of each tetrode). Cluster quality was measured by computing the L ratio and the isolation distance68. The L ratio was computed by the original equation, proposed by Schmitzer-Torbert et al. (2005), not normalized by the total number of spikes recorded on the tetrode. A cluster was considered as a cell when the L ratio was less than 0.20 and the isolation distance was more than 15 (average L ratio was 0.122 ± 0.009 and average isolation distance was 21.4 ± 0.8 in 36 isolated cells). In the auto-correlation histograms, cells with no clear refractory period (<3 ms) were excluded from analyses. In addition, in the cross-correlation histograms, putative cell pairs with a symmetrical gap around the center bins were considered to arise from the same cell and were merged. Finally, cells with spike waveforms longer than 300 μs and an average firing rate of <3 Hz throughout an entire recording period were considered putative pyramidal cells. On the other hand, cells with an average firing rate of more than 3 Hz throughout an entire recording period were considered putative interneurons.\n\nDetection of SWRs\n\nA hippocampal LFP signal was bandpass filtered at 150–250 Hz, and the root mean-square power was calculated in the ripple-band with a bin size of 10 ms. SWR events were detected if the power exceeded a threshold for at least 15 ms. The threshold for SWR detection was set to 3 standard deviations (SDs) above the mean of all envelopes computed from the prestress periods. When an EMG signal from the dorsal neck muscle was simultaneously recorded, the signal was bandpass filtered at 20–200 Hz, and its root mean-square power was calculated with a bin size of 1 s. Periods with EMG power exceeding (2 × Power mode −Power low ) were regarded as massive movement and excluded from this detection analysis, where Power mode and Power low represent the power giving the peak in the frequency distribution and the lowest power, respectively. When an EMG signal was not recorded, the same analysis for excluding periods with massive movement was applied to a LFP signal from a reference electrode placed in the neocortex.\n\nSpike rate analysis\n\nTo remove periods with massive electrical noise due to severe physical attack during a SD period, we computed the root mean-square power of prefrontal LFP signals with a bin size of 1 s as a reference signal including physical noise. Periods with transient huge increases in the power were manually detected from the power trace and average spike rate during SD periods were computed without these periods in each cell. In the prestress and poststress periods, instantaneous spike rates were computed with a bin size of 1 min. In each neuron, a neuron was considered significantly modulated (SD-excited or SD-inhibited) by SD stress if its average firing rate in a SD period was significantly higher or lower than a series of firing rates (bin = 1 min) in a prestress (baseline) period, respectively, defined by a paired t-test.\n\nIn the prestress and poststress periods, vHC SWR-triggered-firing rate changes were computed from all times of vHC SWRs observed in each rest period with a bin size of 20 ms. To compare the degree of SWR-related spike rate changes between the prestress and poststress periods, in each neuron, the triggered-firing rates were converted to z-scored firing rates based on the mean and SD of spike rate changes 100–500 ms before the vHC SWRs in the prestress period.\n\nLFP power analysis\n\nLFP power at frequency bands of 1–100 Hz was computed by a Fourier transformation analysis. To compute the time-frequency representation of LFP power, amygdalar LFP signals were subtracted from prefrontal LFP signals and downsampled to a sampling rate of 200 Hz. The downsampled LFP traces were convolved using complex Morlet wavelet transformation at frequencies ranging from 1 to 100 Hz using a Matlab function. The absolute power spectrum of the LFP during each 10-ms time window was calculated and the power at each frequency band was normalized by the corresponding power averaged over the 0–30-min prestress period. Coherence between two electrodes was computed using a Wavelet coherence by the Matlab with a sampling rate of 200 Hz.\n\nStatistical analysis\n\nAll electrophysiological data are presented as the mean ± standard error of the mean (SEM) and were analyzed using MATLAB2019b. For behavioral data, comparisons of two-sample data were analyzed by Mann–Whitney U test or Wilcoxon signed rank test. Multiple group comparisons were performed by post hoc Bonferroni corrections. For Calb1 expression data, one sample with an extreme value was excluded based on Grubbs test. For spike-rate and SWR-rate data, comparisons of two-sample data were analyzed by paired t-test. Multiple group comparisons were performed by post hoc Bonferroni corrections. The null hypothesis was rejected at the P < 0.05 level.\n\nReporting summary\n\nFurther information on research design is available in the Nature Portfolio Reporting Summary linked to this article."}]