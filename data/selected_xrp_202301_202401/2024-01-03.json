[{"id": 3, "url": "https://news.google.com/rss/articles/CBMibWh0dHBzOi8vd3d3Lmhhd2FpaW5ld3Nub3cuY29tLzIwMjQvMDEvMDMvaGF3YWlpLXJlc3RhdXJhbnRzLWJyYWNlLXJpcHBsZS1lZmZlY3RzLWZvbGxvd2luZy1taW5pbXVtLXdhZ2UtaGlrZS_SAXxodHRwczovL3d3dy5oYXdhaWluZXdzbm93LmNvbS8yMDI0LzAxLzAzL2hhd2FpaS1yZXN0YXVyYW50cy1icmFjZS1yaXBwbGUtZWZmZWN0cy1mb2xsb3dpbmctbWluaW11bS13YWdlLWhpa2UvP291dHB1dFR5cGU9YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Hawaii restaurants brace for ‘ripple effects’ following minimum wage hike - Hawaii News Now", "content": "HONOLULU (HawaiiNewsNow) - Hawaii’s minimum wage rose to $14 an hour on Jan. 1.\n\nThat’s good news for workers.\n\nBut for Hawaii eateries, it’s adding to an already challenging economic picture for 2024.\n\n<PERSON><PERSON>, executive Director for the Hawaii Restaurant Association, said members are reporting that labor costs could rise as much as 10% this year.\n\n“We don’t want to lose our valued, established employees, especially during this employee shortage. So the ripple effect is as the low the incoming employees now are coming in at a higher pay rate and now you’re going to have to raise the rest of those employee’s pay and those aren’t the only inflated costs that many of the businesses will face here in Hawaii,” said <PERSON><PERSON><PERSON>.\n\nHawaii Minimum Wage Hikes\n\n$14 per hour beginning Jan. 1\n\n$16 per hour beginning Jan. 1, 2026\n\n$18 per hour beginning Jan. 1, 2028\n\nLeaders in the industry are also preparing to pay more money for employee health insurance.\n\n“As a small business owner, whether you are a retail or restaurant, there are a lot of other compounding increases that’s taking place right now,” said <PERSON>, a Honolulu McDonald’s franchise owner and legislative lead for the Hawaii Restaurant Association.\n\n“At the beginning of the year, first and foremost, is the medical insurance. All the different carriers, the rates have gone up substantially.”\n\nCopyright 2024 Hawaii News Now. All rights reserved."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vd3d3LndyYWwuY29tL3N0b3J5LzctNS1tYWduaXR1ZGUtZWFydGhxdWFrZS1pbi1qYXBhbi1oYXMtcmlwcGxlLWVmZmVjdC1hY3Jvc3MtZ2xvYmUtaW4tbmMvMjEyMTg2MDYv0gEiaHR0cHM6Ly93d3cud3JhbC5jb20vYW1wLzIxMjE4NjA2Lw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "7.5 magnitude earthquake in Japan has ripple effect across globe & in NC - WRAL News", "content": "The massive earthquake that struck central Japan on Monday at 4:10 p.m. local time immediately registered on nearby seismometers. It was also recorded in the minutes that followed around the world, including here in North Carolina.\n\nEarthquakes occur with a sudden release of strain energy at the hypocenter below the Earth's surface and are first felt at the epicenter directly above. Monday's more than 6 miles beneath the Sea of Japan near the town of Suzu on the Noto Peninsula in Ishikawa prefecture.\n\nThat energy travels outward in all directions, in waves like ripples in a pond, both along the Earth's surface and through its core.\n\nPrimary (P) waves travel fastest and arrive first, compressing the rock horizontally. Shear (S) waves arrive next delivering destructive up and down motion. Seismometers record both the horizontal and vertical motion but the charts you generally see are of that most destructive vertical component.\n\nhow the vertical component of a seismometer works (courtesy IRIS)\n\nLooking across a sampling of seismometers provided by the Incorporated Research Institutions for Seismology (IRIS), the quake registered on a seismograph in northern Alaska 9 minutes after the quake struck Japan. The first seismic wave reached Casper, Wyoming, after 12 minutes, then Pittsboro 14 minutes later at 2:24 a.m. EST.\n\nBut, like waves moving outward from a pebble tossed in a pond, seismic waves are strongest nearest the source. The speed of motion recorded in Japan reduced by a factor of several thousand times as it reached North Carolina.\n\nseismic waves from the Jan 1, 2024 earthquake off western Japan traveled around the world in minutes.\n\nMotion continues after the first shock, along the surface as well as through the earth's core. Though aftershocks are always anticipated after a large earthquake like this, the waves in the chart below are likely still from that initial shock as the earth rings like a bell with energy reflected off the crust and through the core. The seismometer in Pittsboro, like others around the globe, continued to record this motion for several hours after the initial shock.\n\nA 7.5 magnitude off the western coast of Japan on Jan 1, 2024 was recorded by a seismograph nearly 6700 miles way in Pittsboro, NC\n\nThe Python source code used to retrieve waveforms from the four seismic stations to create the charts and maps in this story is available in this GitHub repository."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMid2h0dHBzOi8vd3d3LmZ4ZW1waXJlLmNvbS9mb3JlY2FzdHMvYXJ0aWNsZS94cnAtbmV3cy1zZWMtdnMtcmlwcGxlLXVuY2VydGFpbnR5LWFuZC1sZWdhbC1iYXR0bGVzLXdoYXQtbGllcy1haGVhZC0xMzk5NzU20gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "XRP News: SEC vs. <PERSON><PERSON><PERSON> Uncertainty and Legal Battles – What Lies Ahead? - FX Empire", "content": "English English Italiano Español Português Deutsch العربية Français\n\nImportant Disclaimers The content provided on the website includes general news and publications, our personal analysis and opinions, and contents provided by third parties, which are intended for educational and research purposes only. It does not constitute, and should not be read as, any recommendation or advice to take any action whatsoever, including to make any investment or buy any product. When making any financial decision, you should perform your own due diligence checks, apply your own discretion and consult your competent advisors. The content of the website is not personally directed to you, and we does not take into account your financial situation or needs.The information contained in this website is not necessarily provided in real-time nor is it necessarily accurate. Prices provided herein may be provided by market makers and not by exchanges.Any trading or other financial decision you make shall be at your full responsibility, and you must not rely on any information provided through the website. FX Empire does not provide any warranty regarding any of the information contained in the website, and shall bear no responsibility for any trading losses you might incur as a result of using any information contained in the website.The website may include advertisements and other promotional contents, and FX Empire may receive compensation from third parties in connection with the content. FX Empire does not endorse any third party or recommends using any third party's services, and does not assume responsibility for your use of any such third party's website or services.FX Empire and its employees, officers, subsidiaries and associates, are not liable nor shall they be held liable for any loss or damage resulting from your use of the website or reliance on the information provided on this website. Risk Disclaimers This website includes information about cryptocurrencies, contracts for difference (CFDs) and other financial instruments, and about brokers, exchanges and other entities trading in such instruments. Both cryptocurrencies and CFDs are complex instruments and come with a high risk of losing money. You should carefully consider whether you understand how these instruments work and whether you can afford to take the high risk of losing your money.FX Empire encourages you to perform your own research before making any investment decision, and to avoid investing in any financial instrument which you do not fully understand how it works and what are the risks involved."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vd3d3LnNwYWNlLmNvbS9ncmF2aXRhdGlvbmFsLXdhdmUtYmFja2dyb3VuZC1kYXduLW9mLXVuaXZlcnNl0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Spacetime ripples detected in 2023 continue to puzzle astronomers. Could they be from the dawn of the universe? - Space.com", "content": "Artist's interpretation of an array of pulsars being affected by gravitational ripples produced by a supermassive black hole binary in a distant galaxy.\n\nScientists are still hunting for the source of the faint, persistent hum of gravitational waves discovered reverberating through the Milky Way last year. Those waves could point to more than one tantalizing source, new research suggests.\n\nThe discovery team, the North American Nanohertz Observatory for Gravitational Waves, or NANOGrav, collaboration, strongly suspects the ripples in space - time were created from merging supermassive black holes , each a billion times more massive than our sun. These are known as binary pairs. If that's indeed the case, ongoing work would help estimate the locations of the celestial cosmic beasts, as well as their masses.\n\nHowever, \"finding one binary will not rule out the cosmological origin,\" study co-author <PERSON> of the National Institute of Chemical Physics and Biophysics in Estonia told Space.com. To that end, he and his colleagues studied the NANOGrav data and found that, in addition to the orbiting black hole hypothesis, three proposed cosmological sources seem to explain the data. More on all of those in just a bit; the big picture is that this suggests the gravitational wave signal could be a muddled mix of different sources.\n\n\"This is a big potential problem because many signals are quite similar.\"\n\nRelated: The universe is humming with gravitational waves. Here's why scientists are so excited about the discovery\n\nCosmological sources for spacetime ripples\n\nThe aforementioned exotic, high-energy cosmological processes that took place in the early universe include \"cosmic strings,\" \"phase transitions\" and \"domain walls.\"\n\nImportantly, the latter two are thought to have unfolded shortly after the Big Bang — yet before the event's leftover radiation diffused across the universe. Thus, if the new findings pan out, and one of the sources are those domain walls, scientists say the detected signal would actually be the closest we've gotten to accessing the beginning of the universe .\n\nGet the Space.com Newsletter Breaking space news, the latest updates on rocket launches, skywatching events and more! Contact me with news and offers from other Future brands Receive email from us on behalf of our trusted partners or sponsors\n\nFurther, the cosmological processes outlined by the new study could also help the ongoing hunt for dark matter and dark energy , which together make up 95% of the universe but remain invisible to human eyes.\n\n\"As [domain walls] move and evolve, they carry a lot of energy and emit gravitational waves ,\" said Urrutia. At some point, however, they decay and you end up with \"clumps\" of dark matter, he added.\n\nThe possibility that the detected signal could be from domain walls is especially intriguing, as these complex structures were originally proposed over 50 years ago as one way to explain why the universe contains far more matter than antimatter, the latter of which refers to sort of \"opposite\" matter. Unlike normal, or baryonic, matter that's composed of positive protons and negative electrons, antimatter is composed of negative protons and positive electrons .\n\nWhat's especially weird when it comes to antimatter is that because antimatter and baryonic matter are supposedly fully symmetrical, the Big Bang should've had a 50/50 chance of producing either. That means our universe, theoretically, should consist of equal amounts of both. But it doesn't. Baryonic matter totally dominates the cosmos .\n\nOn the other hand, studying phase transitions allows scientists to peer into many of the various phases that early universe went through to produce the baryonic electrons, protons and neutrons we know of today. Similar to how water boils when heated, cosmic phase transitions were triggered by the variation of temperatures in the universe, and \"bubbles\" interacted with each other to produce sound waves as well as gravitational waves, perhaps like the one recently detected.\n\nBecause the signals from the diverse sources seem to be similar, teasing them out of the detected gravitational waves is no easy task — made harder by the limits of our telescopes. The Laser Interferometer Gravitational-Wave Observatory ( LIGO ), a pair of research facilities in the United States and our current best gravitational wave detector, is designed to spot high-frequency waves.\n\nTo spot more of the low-frequency waves like the ones recently observed, scientists are gearing up for the Laser Interferometer Space Antenna ( LISA ), a European three-satellite network launching in 2037. According to a NASA description, LISA would measure changes in position \"that are less than the diameter of a helium nucleus over a distance of a million miles.\"\n\nAnother space experiment proposed in 2020, the Atomic Experiment for Dark Matter and Gravity Exploration, or AEDGE , may help in the search for gravitational waves in frequencies between those that can be \"heard\" by LISA and LIGO.\n\nFor these future detectors to deliver on their promise, it is crucial for scientists to have concrete predictions on what to look for and how to interpret the data, said Urrutia.\n\n\"There is a huge effort from the community to get all these calculations as precise as possible for when these experiments are ready to launch.\""}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiMmh0dHBzOi8vd3d3Lm5hdHVyZS5jb20vYXJ0aWNsZXMvczQxNDY3LTAyMy00NDI5NS040gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 Jan 2024 08:00:00 GMT", "title": "Awake ripples enhance emotional memory encoding in the human brain - Nature.com", "content": "Participants\n\nIntracranial electroencephalography (iEEG) recordings were obtained from 7 participants (3 females; mean age ± SD = 33 ± 16), undergoing presurgical monitoring of epileptic foci at the University of California Irvine Medical Center (UCIMC) Epilepsy Monitoring Unit. The individual participant demographic information is shown in Supplementary Table 1. Only the participants with the correct discrimination rate of Novel trials > = 85% (see Emotional memory encoding and discrimination task) were included in the analysis. The behavioral inclusion threshold (> 85% performance on Novel trials) was used as a sensitive indicator of participants attention level. Correct performance on Novel trials required classifying the stimuli encountered for the first time and with no similarity to previously encountered stimuli as New. One participant was excluded from the behavioral analysis due to low performance on Novel trials (60.5%, z = −2.34), while the rest of the participants performed at much higher level (93.3 ± 1.6%, mean ± SEM). In addition, this participant performed close to the chance level (50%) across all the trial types combined (51.7%, z = −2.43). Electrode placements were determined entirely based on clinical considerations. All the research procedures were approved by the UCI Institutional Review Board and data was collected following informed consent.\n\nStatistics\n\nAll the statistical tests were performed with the individual participant as the unit of analysis. Unless stated otherwise, all the statistical tests (e.g., <PERSON><PERSON> signed-rank test, t-test) were two-tailed. The effects of valence, stimulus-induced arousal and similarity on stimulus discrimination (Fig. 1c) were assessed using the logistic linear mixed-effect model (for details, see Behavioral Analysis). The association between post-encoding ripples and stimulus arousal/correct Lure discrimination was tested using ripple rates (number of ripples/sec, Fig. 2; Supplementary Figs. 4–7). Except for the Wilcoxon signed-rank test analysis shown in Fig. 2c, ripple rates were normalized at individual participant level using z-score. Conditional comparisons of ripple rates (correct/incorrect Lure discrimination or high/low arousal; Fig. 2c) were done using the Wilcoxon signed rank test (p < 0.05). Associations between the stimulus-induced arousal/later correct Lure discrimination and ripple rate were analyzed using two-way ANOVA (anovan.m function in Matlab, p < 0.05; Supplementary Figs. 4 and 6). The epoch-dependence of ripple association with correct Lure discrimination was tested using the two-way ANOVA (p < 0.05; Supplementary Fig. 7). Post-hoc tests were done using the multcompare.m function in Matlab. The non-parametric statistical test was used due to non-normal distribution of ripple numbers across the participants. The association between the post-encoding theta power on ripple channels and later correct Lure discrimination was tested using the logistic regression (p < 0.05).\n\nStatistical significance of ripple-locked post-encoding stimulus similarity (Fig. 3b) was assessed by comparing the real test statistics with empirical null distribution, obtained using Monte Carlo method (for details, see Representational Similarity Analysis). We implemented the non-parametric cluster-based permutation test47 to assess the conditional differences (correct/incorrect Lure discrimination or high/low arousal) of post-encoding stimulus similarity (Fig. 3c) and mutual information (Fig. 4b), by randomly shuffling the conditional trial labels 1000 times (for details, see Representational Similarity Analysis). Similarly, the significant temporal windows for the cross structure ripple-locked joint post-encoding stimulus similarity (Fig. 4a) were assessed by comparing to empirical null distribution (for details, see Joint post-encoding stimulus similarity analysis). The correction for multiple comparisons was performed using the Benjamini-Hochberg procedure48. To compare the timing of ripple-locked stimulus similarity between the hippocampus and amygdala, the peak similarity timings were computed during post-encoding ripple windows, following the encoding of later correctly discriminated stimuli. Next, the peak similarity timings were compared between the regions using the one-tail paired t-test (p < 0.05; Supplementary Fig. 13).\n\nEmotional memory encoding and discrimination task\n\nThe emotional memory encoding and discrimination (EMOP) task consists of encoding and discrimination blocks. During the encoding block (148 trials), each trial consists of a cross fixation (1000 msec), followed by stimulus encoding (2000 msec) and self-paced post-encoding response period (up to 2000 msec). During the post-encoding response period, participants are asked to classify the stimulus emotional valence as either negative, neutral or positive, using the corresponding laptop key. During the retrieval block (290 trials), trial time structure is identical to encoding phase. Following the cross fixation (1000 msec), the participants are presented for 2000 msec with a stimulus identical (Repeat, 54 trials), slightly different (Lure, 97 trials) or unrelated (Novel, 139 trials) to previously encoded stimuli. Next, during the self-paced memory discrimination epoch (up to 2000 msec), participants are asked to discriminate if the presented stimulus was seen during encoding (Old) or not (New). Correct discrimination is defined as classifying the Repeat stimuli as Old and Lure or Novel stimuli as New. The stimuli were selected from the continuous distributions across the valence and stimulus-induced arousal axes (Supplementary Fig. 1). The same set of stimuli was used across participants. In addition, the valence, arousal and lure pair similarity of each stimulus were rated by separate cohorts of healthy participants (also used in Leal et al.14). Specifically, a first cohort (N = 50, 32 females; age mean ± SD = 22 ± 5) rated the stimulus emotional valence on a continuous scale (range 1–9, with 1 denoting the most negative, 9 the most positive, and 5 neutral valence). Stimuli were assigned in Negative (valence < = 3.5), Neutral (3.5 > valence < 6) or Positive (valence > = 6) groups. Another cohort of healthy participants (N = 16, 4 females; age mean ± SD = 23 ± 5) rated the stimulus-induced emotional arousal on a scale 1–9 (1 being the least and 9 being the most arousing). Finally, a third cohort (N = 17, 11 females; age mean ± SD = 20 ± 1) examined relative lure pair similarity between the pair of lure images presented during encoding and retrieval stage on the scale 1–814. The rationale for obtaining the categorical ratings from study participants was the need of using the sliding scale for obtaining the continuous ratings, which would introduce systematic difference in response times, depending on the scale distance. The continuous ratings from healthy participants were used for behavioral/neurophysiological correlation based on the: a) better feasibility of continuous behavioral variables for correlation with neural signals and b) high correspondence between the continuous (healthy participants) and categorical (study participants) ratings (~85%; Supplementary Fig. 1b). The high correspondence of stimulus valence ratings obtained from study participants and healthy population suggests the intact emotional processing in study participants (Supplementary Fig. 1).\n\nBehavioral analyses\n\nTo assess the effects of valence, stimulus-induced arousal and similarity on Lure stimulus discrimination, we implemented the logistic linear mixed-effect model.\n\n$$y=\\beta X+{uZ}+\\varepsilon$$ (1)\n\nIn this model, y indicates the responses across the individual Lure discrimination trials (0-Old; 1-New), \\(X={\\left[{x}_{1},\\,{x}_{2},\\,{x}_{3}\\right]}^{T}\\) denotes three fixed effect regressors (encoded stimulus valence and arousal as well as similarity between the encoded and Lure stimulus), \\(Z={\\left[{z}_{1}\\right]}^{T}\\) denotes random effect regressor (participant identity), β and u denote the fixed and random-effect regression coefficients, and ε denotes the error term. The model includes random intercept to incorporate individual participant differences. We normalized the valence, stimulus-induced arousal and similarity values relative to the scale of 0 to 1. The statistics reported in Fig. 1c corresponds to the fixed-effect coefficients β. Lure discrimination index (LDI) is defined as the difference in the probability of Lure and Repeat stimuli being classified as New (p(New|Lure) – p(New|Repeat)). This procedure corrects for the general tendency of classifying the stimuli as New14. The effect of valence on LDI was tested using one-way ANOVA (p < 0.05). The LDI comparison between the low- and high-arousal stimuli was performed using the one-tailed paired t-test (p < 0.05). The association between the valence and response times was tested using one-way ANOVA (p < 0.05). The effects of stimulus arousal and valence on the correct discrimination were tested using the linear mixed-effects model (LME; p < 0.05).\n\nData collection\n\nThe behavioral experiment was administered using the PsychoPy2 software49 (Version 1.82.01). The laptop was placed at a comfortable distance in front of the participant. The iEEG signal was recorded using a Nihon Kohen system (256 channel amplifier, model JE120A) and NeuraLynx ATLAS acquisition system, with an analog high-pass filter (0.01 Hz cutoff frequency) and sampling frequency 5000 Hz.\n\nData analysis\n\nThe following softwares and packages were used: MATLAB (Version 9.7); Fieldtrip Toolbox; Advanced Normalization Tools(ANTs); Freely Moving Animal Toolbox(FMA); Wavelet Toolbox; Signal Processing Toolbox; Statistics and Machine Learning Toolbox; EEMD package (https://github.com/leeneil/eemd-matlab.git).\n\nElectrode localization\n\nWe localized each electrode using pre-implantation structural T1-weighted MRI scans (pre-MRI) and post-implantation MRI scans (post-MRI) or CT scans (post-CT). Specifically, we co-registered pre-MRI and post-MRI (or post-CT) scans by means of a rigid body transformation parametrized with three translation in x,y,z directions as well as three rotations using Advanced Normalization Tools (ANTs https://stnava.github.io/ANTs/). We implemented a high-resolution anatomical template with the label of medial temporal lobe subfields14 to guide the localization for individual electrodes. We resampled the template with 1 mm isotropic, and aligned it to pre-MRI by ANTs Symmetric Normalization50 to produce a participant-specific template. The electrode localization was identified by comparing the participant-specific template subfield area with electrode artifacts (Fig. 2a). The localization results were further reviewed by the neurologist (J.J.L.).\n\nPreprocessing\n\nThe signal preprocessing was done using the custom-written MATLAB code (Version 9.7) and Fieldtrip Toolbox51. The 60 Hz line noise and its harmonics were removed using a finite impulse response (FIR) notch filter (ft_preprocessing.m function in FieldTrip). The EEG signal was down-sampled to 2000 Hz, demeaned and high-passed filtered (cutoff frequency 0.3 Hz). The power spectrum density (PSD) was computed using the multitaper method with the Hanning window (ft_freqanalysis.m function in FieldTrip). All the channels were re-referenced to the nearest white matter channel from the same depth electrode, based on the electrode localization results. The interictal epilectic discharges were manually marked by an epileptologist (J.J.L.), using the ft_databrowser.m function in FieldTrip. The channels with severe contamination and trials containing epileptiform discharges were excluded from further analyses.\n\nRipple detection\n\nFollowing the removal of channels with excessive epileptic activity and individual trials containing visually identified interictal epilectic discharges, ripples were detected on the remaining hippocampal channels, using the Freely Moving Animal Toolbox (FMA; http://fmatoolbox.sourceforge.net/). Only the hippocampal channels were used in ripple detection for ripple-based analysis. First, the iEEG traces from the trials used in the analysis were concatenated. Next, concatenated traces were bandpass-filtered (80–150 Hz, Chebyshev 4th order filter, function filtfilt.m in Matlab). The analytical amplitude was obtained by computing the absolute value of Hilbert-transformed filtered trace (function hilbert.m in Matlab). The analytical amplitude values during periods ± 75 msec around the trial onsets/offsets were set to zero, to avoid the edge effects resulting from concatenating discontinuous traces. Finally, the envelope was z-scored and threshold-based ripple detection was performed on z-scored trace (Supplementary Fig. 15a). Detected events were considered ripples if the z-scored analytical amplitude remained above the lower threshold (z = 2) for 20–100 msec and if the peak value during this period exceeded higher threshold (z = 5). The prominence of the sharp-wave component in the ripple waveform depends on the optimal electrode position in the hippocampal layers52. As the electrode position in human participants can’t be optimized post-surgically, this likely accounts for the absence of prominent sharp-wave component in the ripple waveform in this study, similar to other published examples of human ripples25,27,38,53. As an additional control analysis, we compared the ripple detection from the hippocampal iEEG signal with the event detection from the synthetic signal of the same spectral characteristics, using the identical detection procedure (see Ripple detection). Only the channels with z-scored number of detected ripples > −2 and number of detected events higher than in the synthetic signal (see Ripple detection from synthetic signal) were used in the analysis. The participant 1 was excluded from the analysis based on the low number of detected putative ripple events (z-score < −2, relative to distribution of detected ripple numbers across the participants, which is not higher than chance level (Supplementary Fig. 16). If the multiple channels from a single participant passed this criteria, a channel with the highest number of detected ripples was selected for further ripple-related analysis. Ripple-locked windows for both the hippocampal and amygdala activity were performed relative to ripple timestamps from the hippocampal channel with the highest number of detected ripples within a given participant. Ripple-like activity in the amygdala was detected using the same algorithm. Coincidence between the hippocampal ripples and ripple-like activity in the amygdala was calculated as the percentage of hippocampal ripples accompanied by amygdala ripple-like activity within the ± 50 msec. Only the Lure trials were used in ripple-based analysis.\n\nTime-resolved ripple rates\n\nThe time-resolved ripple rates (Supplementary Fig. 5) were calculated for individual epochs (encoding, post-encoding) and conditions (low and high stimulus-induced arousal, correct and incorrect Lure discrimination), using the msec bin size and msec step size. The resulting time-resolved ripple rate was smoothed with a Gaussian kernel (σ = 150 msec) and averaged across the trials. The time-resolved ripple rate was compared based on the low vs. high stimulus-induced arousal and later correct vs. incorrect Lure discrimination contrasts, using non-parametric cluster-based permutation test (p < 0.05).\n\nRipple detection from synthetic signal\n\nThe power spectral density (PSD) was calculated for each hippocampal channel used in ripple detection. Next, the channel-specific filter was applied on a random signal with Gaussian distribution, resulting in a synthetic signal with identical spectral slope as the hippocampal signal. Specifically, the synthetic signal was first transformed to frequency domain by N-point Fourier transform (N denoting the number of datapoints in the signal), followed by multiplication of resulting spectrum by the PSD coefficients and application of inverse Fourier transform, to convert the signal back to time domain. The resulting synthetic signal mimicked the hippocampal channel-specific spectral characteristics. Next, the ripple detection procedure (see Ripple detection) was applied on the synthetic signal. Finally, as an additional control, the numbers of detected events were compared between the hippocampal iEEG signal and channel-specific synthetic signal. In all the 6 participants used in the ripple-based analysis (participants 2–7), the numbers of detected ripples were higher than the numbers obtained from the participant-specific synthetic signals (Supplementary Fig. 16). Participant 1 was excluded from the analysis based on the two criteria: a) low number of detected putative ripple events (z-score < −2, relative to distribution across the participants) and b) the number of detected events lower than chance level, defined as the number of detected events in synthetic signal (Supplementary Fig. 16).\n\nUnsupervised decomposition of iEEG signal\n\nTo assess the post-encoding stimulus similarity, high-frequency activity (HFA; 30–280 Hz) was used as an indirect measure of local population activity19,20,54. This frequency range is relatively broad and the simple bandpass-filtering might cause a disproportionate contribution of lower frequencies within the bandpass range to the overall HFA estimate, due to the 1/f nature of EEG power spectra. To avoid this confound, we applied the Ensemble Empirical Mode Decomposition20,55 (EEMD; https://github.com/leeneil/eemd-matlab.git), to identify the individual characteristic signal modes within the frequency range of interest. Time series representing each individual mode were normalized across time, resulting in a more balanced sampling across the broadband gamma range. Briefly, the EEMD decomposes a non-stationary signal into its elementary components, referred to as intrinsic mode functions55 (IMFs; Supplementary Fig. 17). The procedure iteratively applies an empirical mode decomposition algorithm, while adding white noise to prevent the mode mixing55,56. Using this approach, decomposition output entirely depends on the signal’s intrinsic properties, avoiding prior assumptions20,55,56. The resulting IMFs captured several canonical spectral features consistently across participants and anatomical structures (Supplementary Table 3). Finally, the HFA time-series on individual channels were reconstructed by summing the channel-specific IMFs with center frequencies > 30 Hz20.\n\nTime-frequency representation of the HFA\n\nThe instantaneous spectral power at each time-frequency bin was derived from the reconstructed HFA time series (x), using a wavelet transform57,58. This approach consists of convolving the time series x with a set of Morlet wavelets, parametrized by a range of cycle numbers (n = 2, 3, …, 10) at a given frequency f,\n\n$${P}_{f,n}\\left(t\\right)=\\left|{\\psi }_{f,n} \\,*\\, x\\left(t\\right)\\right|,\\, n=2,3,\\ldots,10$$ (2)\n\nwith \\({\\psi }_{f,n}\\) defined as\n\n$${\\psi }_{f,n}=\\frac{1}{{B}_{n}\\sqrt{2\\pi }}{e}^{-\\frac{{t}^{2}}{2{B}_{n}^{2}}}{e}\\,^{j2\\pi {ft}},\\, {where}\\,{B}_{n}=\\frac{n}{5f}$$ (3)\n\nand computing the geometric average \\((\\hat{P}(f,\\, t))\\) of resulting spectral power at each time- frequency bin:\n\n$$\\hat{P}\\left(f,\\, t\\right)=\\root 9 \\of {{\\prod }_{n=2}^{10}{P}_{f,n}\\left(t\\right)}$$ (4)\n\nThis approach results in a high temporal and frequency resolution, facilitating the detection of narrow-band, transient oscillatory events57,58. The wavelet center frequencies were within 30–280 Hz range, with 1 Hz increments. The wavelet cycle number range (2–10) is commonly used59. To avoid the edge effects, this procedure was applied on the entire individual recording sessions, and the resulting time-frequency response matrices were segmented into trial epochs (starting −1000 msec prior to stimulus onset and ending 1000 msec after the response time). The power within each trial epoch was then normalized by z-transforming each frequency bin and subtracting the average pre-trial baseline (−1000 to 0 msec, relative to stimulus onset59).\n\nRepresentational Similarity Analysis (RSA)\n\nThe representational similarity was quantified as the Spearman correlation between the HFA power spectral vectors (PSVs), for each combination of the encoding-response time bins from the same trial21,46,60,61 (Supplementary Fig. 17). Specifically, the instantaneous spectral power at each frequency was estimated for 100 msec time bins (10 msec step size, 90% overlap), producing the time bin - specific power spectrum vectors (PSV), spanning the encoding (2 sec time window after stimulus onset) and post-encoding response (time window after stimulus offset and before button press) periods:\n\n$${\\vec{{{{{{\\boldsymbol{P}}}}}}{{{{{\\bf{SV}}}}}}}}_{{encoding}}\\left({t}_{1}\\right)={\\left[{z}_{1}\\left({t}_{1}\\right),\\ldots,{z}_{{n}_{f}}\\left({t}_{1}\\right)\\right]}_{{encoding}}$$ (5)\n\n$${\\overline{{{{{{\\bf{PSV}}}}}}}}_{{response}}\\left({t}_{2}\\right)={\\left[{z}_{1}\\left({t}_{2}\\right),\\ldots,{z}_{{n}_{f}}\\left({t}_{2}\\right)\\right]}_{{response}}$$ (6)\n\nSimilar to previous studies21,22,46,60,61,62, we computed Spearman’s correlation as a measure of PSV similarity between the encoding time t 1 and response time t 2 for each encoded stimulus,\n\n$$r\\left({t}_{1},\\,{t}_{2}\\right)=\\frac{{Cov}\\left(r{g}_{{\\overline{{{{{{\\bf{PSV}}}}}}}}_{{encoding}}\\left({t}_{1}\\right)},\\, r{g}_{{\\overline{{{{{{\\bf{PSV}}}}}}}}_{{response}}\\left({t}_{2}\\right)}\\right)}{{\\sigma }_{r{g}_{{\\overline{{{{{{\\bf{PSV}}}}}}}}_{{encoding}}\\left({t}_{1}\\right)}}{\\sigma }_{r{g}_{{\\overline{{{{{{\\bf{PSV}}}}}}}}_{{response}}\\left({t}_{2}\\right)}}},\\,{t}_{1}\\in \\left[0,\\, 2\\right]{,\\, t}_{2}\\in \\left[0,\\, {RT}\\right]\\,\\sec,$$ (7)\n\nwith rg representing the ranking operator on the vector \\(\\overline{{PSV}}\\), and σ the variance of the vector. This produced a trial-specific two-dimensional similarity matrices, containing all the combinations of encoding (t 1 ) and response (t 2 ) time bins (Supplementary Fig. 15c). The correlation coefficients r were then Fisher transformed, with the resulting coefficients following Gaussian distribution. The region-specific (amygdala and hippocampus) similarity matrices were averaged across trials within individual participants (producing the participant/region-specific similarity maps) and used for group-level statistical analysis. The association strength between the post-encoding ripple-locked stimulus similarity and a) stimulus arousal or b) later correct Lure discrimination was compared between the amygdala and hippocampus. First, the regional t-values were computed, based on within-participant comparison between the post-encoding ripple-locked stimulus similarity for low- and high-arousal Lure stimuli or for the correctly or incorrectly discriminated Lure stimuli. The regional t-value time courses were then compared using the non-parametric cluster-based permutation test (1000 permutations, p < 0.05).\n\nRipple-locked stimulus similarity\n\nStimulus similarity during individual post-encoding time bins was computed by averaging the bin-specific similarity with the encoding period (200 time bins over 2 sec), resulting in a stimulus similarity time series. To obtain the ripple-locked stimulus similarity, we averaged the stimulus similarity within ± 250 msec around the individual ripple peak times, separately for amygdala and hippocampus (Fig. 3a). To avoid the leakage of encoding epoch activity, only the part of the ripple-locked windows non-overlapping with the encoding epoch was used in the ripple-locked analysis. We next tested whether the post-encoding stimulus similarity is locked to ripples (Fig. 3b), by comparing the grand-average ripple-locked stimulus similarity trace with an empirical null distribution obtained from Monte Carlo simulation. Specifically, we circularly randomly jittered the ripple peak times within ± 500 msec window for 1000 times, obtaining an empirical null distribution of stimulus similarity. Circular jittering denotes the method of event time shuffling within a limited time window. In the context of the present study, the time window is defined by the onset of post-encoding epoch and the offset of subsequent cross-fixation (Fig. 1a). For example, if the ripple occurred 200 msec after the post-encoding onset and the randomly generated shuffled distance is −500 msec, the assigned shuffled ripple timestamp would be −300 msec prior to offset of the cross-fixation epoch. Next, the stimulus similarity trace around jittered timestamps was averaged within participants and the grand average was calculated across participants. The procedure was repeated for 1000 times, resulting in an empirical null-distribution of stimulus similarity. Regional similarity trace windows exceeding 95th percentile of null-distribution were considered windows of statistically significant ripple-locked stimulus similarity.\n\nTo test whether the ripple-locked stimulus similarity is associated with stimulus-induced arousal and later discrimination (Fig. 3c), we first derived the ripple-triggered stimulus similarity, a metric taking the time-locked specificity relative to ripple peak time into account. For every stimulus similarity trace around ripple peak time, we circularly jittered the time as the procedure described above. This results in an empirical null distribution of stimulus similarity (i.e., correlation coefficient) for every time point around ripple. We normalized the real stimulus similarity by z-scoring with mean and standard deviation of the null distribution. We referred to the resulting z-value as ripple-triggered stimulus similarity and it follows Gaussian distribution. We quantified the ripple-locked stimulus similarity difference between the high/low arousal and between correct/incorrect Lure discrimination at every time point by t-test, and corrected for the multiple comparisons using non-parametric cluster-based permutation test. Specifically, we performed the group-level comparisons using paired t-test and identified contiguous time bins with the p < 0.05, defined as clusters. The t-values within each cluster were summed as the cluster statistics. We created an empirical null distribution by shuffling the conditional trial labels 1000 times where the maximum cluster statistics was identified for each permutation. It was considered as statistically significant if the real t-sum cluster statistics exceeded the 95% percentile of the null distribution. In addition, regional double-dissociation was tested by computing the region-specific (amygdala and hippocampus) t-value time series, obtained by comparing the low vs. high stimulus-induced arousal trials and correct vs. incorrect Lure discrimination trials within-participant. Next the condition-specific t-values were compared separately for each region, between the stimulus-induced arousal and later correct Lure discrimination, using the non-parametric cluster-based permutation (p < 0.05; Supplementary Fig. 9). As an additional control analysis, we averaged the post-encoding ripple times at participant level, using response times as reference points. The average post-encoding ripple times were used to compare the event-locked stimulus similarity on the trials not containing post-encoding ripples (non-parametric cluster-based permutation test, p’s > 0.05; Supplementary Fig. 11).\n\nStimulus-specific representational similarity\n\nThe stimulus-specificity of ripple-locked activity in the hippocampus was determined by first computing the similarity between the activity during encoding epoch on i-th trial (Enc i ) and post-encoding ripple window (Ripple) on the same trial (S same = r(Enc i , Ripple i )). Next, we computed the similarity between other stimuli encoding epochs and (Enc 1, 2, …n ) and post-encoding ripple window on the i-th trial (S diff = r(Enc 1, 2, …n , Ripple i ), n denoting the number of different stimuli in the experiment). As the S same might be inflated due to temporal proximity between the Enc i and Ripple i 63,64, we accounted for the difference in average post-encoding epoch similarity (S avg ) between the same and different trials. Specifically, S avg was defined as the difference in similarity between the encoding epoch and the entire post-encoding window on the same trial (S same_avg ) or different trials (S diff_avg ). For each individual stimulus, the stimulus-specific similarity (S spec ) was defined as following:\n\n$${{{{{\\rm{Sspec}}}}}}=({{{{{\\rm{Ssame}}}}}}-{{{{{{\\rm{S}}}}}}}_{{{{{{\\rm{diff}}}}}}})-({{{{{{\\rm{S}}}}}}}_{{{{{{\\rm{same}}}}}}\\_{{{{{\\rm{avg}}}}}}}-{{{{{{\\rm{S}}}}}}}_{{{{{{\\rm{diff}}}}}}\\_{{{{{\\rm{avg}}}}}}})$$ (8)\n\nNext, the S spec was averaged at participant level and the t-statistics was obtained by comparing the participant-averaged S spec with zero. The similarity null-distribution (S shuff ) was created by shuffling the stimulus identity (same vs. different) 1000 times and obtaining the t-statistics as described above. Finally, the cluster statistics was performed by comparing the t-values obtained from S spec with the distribution of t-values obtained from S shuff (non-parametric cluster-based permutation test; n = 1000 permutations, p < 0.05; Supplementary Fig. 12).\n\nCross-structure joint ripple-locked stimulus similarity\n\nThe cross-structure joint ripple-locked stimulus similarity was obtained by calculating the outer product between the structure-specific stimulus similarity traces (hippocampus and amygdala) during post-encoding ripple windows. The resulting joint stimulus similarity matrices were averaged across the individual ripples for each participant, separately for later correctly or incorrectly discriminated trials. To assess the statistical significance of joint cross-structure stimulus similarity, we performed a Monte Carlo simulation to generate an empirical null distribution by circularly jittering the ripple peak times. The stimulus similarity significance was defined as exceeding the 95% percentile of null distribution (Fig. 4a).\n\nDual states analyses\n\nRecorded periods were divided into low- and high-theta (3–10 Hz) or gamma (30–250 Hz) periods, based on the participant-specific power median split, resulting in an equal amount of time assigned to each state. The ripple proportion is defined as the proportion of the total number of ripples occurring during an individual state. The ripple proportion comparisons between the low- and high-theta or gamma periods were performed using one-tailed Wilcoxon signed-rank test (p < 0.05; Supplementary Fig. 8b).\n\nMutual information\n\nMutual information (MI)59,65 is a method for quantifying the amount of information shared between the variables of interest. In electrophysiology, MI is applied to test for the presence and directionality of information flow between the multiple time-series. We applied MI to assess the directional influence between the stimulus similarity in amygdala and hippocampus during the post-encoding ripple windows (Fig. 4b). First, the structure-specific stimulus similarity traces from the amygdala and hippocampus were obtained around each ripple event (±250 msec; see ripple-locked stimulus similarity). Next, we calculated the MI between the amygdala and hippocampal memory stimulus similarity traces, using the 200 msec bin size (10 msec step size), covering the ±250 msec window around ripple peaks. For each time bin, the stimulus similarity was binned into 10 bins (with uniform bin count), consistently across the participants and conditions. The MI between the time series X and Y was defined as\n\n$${MI}\\left(X{{{{{\\rm{;}}}}}} \\, Y\\right)=\\mathop{\\sum }\\limits_{i}^{n}\\mathop{\\sum }\\limits_{j}^{m}p\\left({x}_{i},\\, {y}_{j}\\right)p\\left({x}_{i},\\, {y}_{j}\\right)-\\mathop{\\sum }\\limits_{i}^{n}p\\left({x}_{i}\\right)p\\left({x}_{i}\\right)-\\mathop{\\sum }\\limits_{j}^{m}p\\left({y}_{j}\\right)p\\left({y}_{j}\\right),$$ (9)\n\nwhere \\(p\\left({x}_{i}\\right)\\) and \\(p\\left({y}_{j}\\right)\\) represented the marginal probability of signals X and Y, \\(p\\left({x}_{i},\\, {y}_{j}\\right)\\) indicated their joint probability, while m and n represented the numbers of stimulus similarity bins for time series X and Y59,65. To test the directionality of information flow, we calculated the time-lagged MI by shifting one time series relative to another across all the time bin combinations. The \\(M{I}_{{AMY}\\to {HPC}}\\) and \\(M{I}_{{HPC}\\to {AMY}}\\) at individual time bins were defined as the mean of all the subsequent time-lagged MI bins in the other region59,66. We defined the MI directional influence as the significant difference between the \\(M{I}_{{AMY}\\to {HPC}}\\) and \\(M{I}_{{HPC}\\to {AMY}}\\), assessed using Wilcoxon signed-rank test for each time bin. Correction for multiple comparisons was performed using the non-parametric cluster- based permutation test."}]