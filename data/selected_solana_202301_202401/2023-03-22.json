[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vbWVzc2FyaS5pby9yZXBvcnQvc29sYW5hLWFuYWx5emluZy1kb3dudGltZXMtc3RhdGlzdGljcy1hbmQtZWNvc3lzdGVtLWRldmVsb3BtZW500gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Solana: Analyzing Downtimes, Statistics, and Ecosystem Development - Messari", "content": "Upgrade to create screens\n\nProfessional-grade Research Reports covering the latest trends and assets in the crypto space\n\nFundraising Screener to track trends across 8,000+ crypto funding rounds, 500+ M&A Deals, and 10,000+ investors\n\nUnlocked Enterprise Research Reports 90 days after they are published\n\nFull access to advanced asset screening with custom filters, queries, and metrics\n\nReal-time Governance Tracker covering proposals from their initial stages through votes and implementation\n\nAdvanced AI Digests with all of the features in Lite plus Key Developments for major protocol changes"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vamluZ2RhaWx5Y3VsdHVyZS5jb20vZm9ybWZ1bmN0aW9uLWZpenpsZXMtb3V0LXNvbGFuYS1hcnQtcGxhdGZvcm0tc2h1dHRlcnMtYW1pZC1uZnQtbWFya2V0LXR1cm1vaWwv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Formfunction Fizzles Out: Solana Art Platform Shutters Amid NFT Market Turmoil - Jing Daily Culture", "content": "Formfunction, a Solana-based NFT marketplace specializing in single-edition artwork, is closing down a year after raising $4.7 million in seed funding. The company announced that it will shutter its platform on March 29, without providing specific reasons for the decision. Formfunction’s closure comes amid a turbulent NFT market, with falling trading volume and declining prices for assets.\n\nLaunched in February 2022, Formfunction boasted the third-largest weekly trading volume of any Solana NFT marketplace in November 2022. However, the total trading volume for Solana NFTs has been shrinking, with February 2023’s volume down over 50 percent from January and significantly lower than March 2022’s figure.\n\nThe NFT market has seen a shift in dynamics, with incumbent platforms having to compete with upstart marketplaces that have cut platform fees and artist royalties from secondary sales. This has made it increasingly challenging for NFT marketplaces like Formfunction to operate.\n\nWhile sales spiked again last month, much of this surge in activity came from traders on Ethereum NFT marketplace Blur, rapidly flipping assets to gain token rewards. This type of transaction has been dubbed “wash trading” by data startup CryptoSlam, though it does not precisely fit the traditional definition of wash trades."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiUGh0dHBzOi8vd3d3LmludmVzdGluZ2N1YmUuY29tL3NvbGFuYS1wcmljZS1wcmVkaWN0aW9uLWNhbi1zb2wtY3J5cHRvLWJyZWFrLTIyLTYv0gFUaHR0cHM6Ly93d3cuaW52ZXN0aW5nY3ViZS5jb20vc29sYW5hLXByaWNlLXByZWRpY3Rpb24tY2FuLXNvbC1jcnlwdG8tYnJlYWstMjItNi9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 10:03:50 GMT", "title": "Solana Price Prediction: Can SOL Crypto Break $22.6? - InvestingCube", "content": "Solana (SOLUSD) price has seen exponential growth in 2023. Since the start of the year, SOL crypto has surged by more than 135%, which makes it one of the best-performing cryptocurrencies of this year. However, after explosive growth in the first two months of the year, the price has been showing weakness for the past few weeks.\n\nOn Wednesday, Solana crypto is trading at $22.3 after losing 1.07% till the start of the London session. Bitcoin and Ethereum are also trading sideways after showing minor losses. BTC is still fondling with the $28,000 resistance, which is its highest level in the last nine months.\n\nSolana Crypto To Remain Volatile This Week\n\nCryptocurrencies are having the most volatile month of the year as the failure of multiple banks has drastically affected the liquidity dynamics of the market. Bitcoin price has been the biggest gainer of the current banking crisis as its price is up 40% in just two weeks. Most other altcoins like SOL crypto and Ethereum are still lagging in the positive price action as the investors await the FOMC meeting.\n\nToday’s FOMC meeting will conclude with the decision on interest rates and keep the Solana price in check. Many analysts are expecting another 25 basis points hike. It is also likely that the Federal Reserve will pause the rate hikes considering the recent bank failures in the US. However, considering the hawkish tone of the Fed in the last FOMC minutes, another rate hike seems to be more probable.\n\nSolana Price Is Retesting A Key Resistance\n\nAs shown in the following chart, the SOLUSD chart has formed a symmetrical wedge pattern. It is also visible on the chart that the price has failed to break out of the downward trendline despite multiple attempts. Furthermore, the price is also retesting the 200-day moving average, which currently lies at $22.60.\n\nThis is the most critical level and line in the sand for many traders. If today’s DOMC meeting results in a pause in rate hikes, then I expect the price to break above $22.6. This will make Solana price prediction very bullish, and the price can retest the November high of $38.79. On the other hand, a 50 bps hike can send the price toward its March 2023 low of $16."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiU2h0dHBzOi8vd3d3LmRlbG1hcnRpbWVzLm5ldC9saWZlc3R5bGUvZXZlbnRzL3N0b3J5LzIwMjMtMDMtMjIvbG9jYWwtZWdnLWh1bnQtYWN0aW9u0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "Local egg hunt action for kids and families - Del Mar Times", "content": "Ocean Air Spring Egg Hunt\n\nOcean Air Recreation Center will host a Spring Egg Hunt on Saturday, March 25 from 9-11 a.m. The event will include the egg hunt, arts and crafts, music and games.\n\n4770 Fairport Way, Carmel Valley\n\nEaster fun in PHR\n\nOn Sunday, April 2 from 1-3 p.m. the Village at Pacific Highlands Ranch will offer complimentary photos with the Easter bunny and a concert with Hullabaloo.\n\n13490 Pacific Highlands Ranch Parkway, Pacific Highlands Ranch\n\nCarmel Valley Egg-treme Egg Hunt\n\nCarmel Valley will host an egg hunt event on Saturday, April 8 from 9 a.m. to 12:30 p.m. at the Carmel Valley Community Park. The event features bunny photo opps, face painters, DJ arts and crafts, magic show, obstacle course and jumpers.\n\nThe egg hunt is split by age groups: 9 a.m.-2 and under; 9:30 a.m. 3-4; 10 a.m. 5-6; 11:20 a.m. 7-8; 11:50 a.m. 9-10; and 12:20 p.m. 11-12. The magic show will be held between egg hunt action at 10:30 a.m. Kids are encouraged to check their eggs for a golden ticket to win a prize basket.\n\n3777 Townsgate Drive, Carmel Valley.\n\nSolana Beach Children’s Spring Festival and Egg Hunt\n\nThe city of Solana Beach will host the Children’s Spring Festival and Egg Hunt on Saturday, April 8 at the La Colonia Community Center at 10 a.m.\n\nThe celebration will include games and refreshments but the major event is the Egg Hunt where boys and girls, in third grade or younger, will search for eggs filled with treats and prizes. Participants are being asked to bring their own basket or decorative bag to collect the goodies. Fun jumps, crafts, pictures with the Spring Bunny and piñatas will also be offered. Refreshments include lemonade, coffee, popcorn and cookies.\n\n715 Valley Ave, Solana Beach\n\nEgg Hunt at Piazza Carmel\n\nCarmel Valley’s Piazza Carmel will host a free Easter Egg Hunt on Saturday, April 8 from noon to 3 p.m. in the food court. The event will include a magic show, balloon artist, kids crafts, face painting, photos with the Easter Bunny and train rides around the outdoor shopping center. Free cotton candy and scavenger hunt. Visit piazza-carmel.com.\n\n3810 Valley Centre Drive, Carmel Valley\n\nBunny art at One Paseo\n\nStop by One Paseo on Wednesday, April 5 from 2- 5 p.m. for a creative art session with the New Children’s Museum . At the Log, kids can pick up a free art kit, giving them all the supplies and creative freedom they need to create a portrait of themselves with the Easter Bunny.\n\n3725 Paseo Place, Carmel Valley\n\nFree Community Easter Egg Hunt at Grace Point\n\nGrace Point Church will host a free Community Easter Egg Hunt on Saturday, April 8 from 9:30 a.m. to noon. The hunt will be divided up into age groups, and kids can participate in bounce houses, face painting, Easter crafts, a petting zoo, large group games. There will also be a family photo booth and light snacks.\n\nFor more information, email <EMAIL> or visit gracepointsd.com/fun\n\n13340 Hayford Way, Carmel Valley\n\n"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiNGh0dHBzOi8vd3d3LmNoYWluYWx5c2lzLmNvbS9ibG9nL3NvbGFuYS1jaGFpbmFseXNpcy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 22 Mar 2023 07:00:00 GMT", "title": "How We Built Chainalysis' Robust Knowledge Graph for Solana Transactions - Chainalysis Blog", "content": "The Solana blockchain has several uniquely sophisticated features, including an account structure with system, token, and stake accounts, the ability to transfer funds by changing account ownership, and distinctive treatment of collateral in smart contracts. These characteristics can present challenges for investigators, compliance analysts, and other ecosystem participants seeking easy-to-understand models for transaction activity. Without a precise knowledge graph, anyone involved in blockchain analysis could reach incorrect conclusions.\n\nIn this blog, we’ll highlight how our research and development team accounted for Solana’s defining characteristics to build out a robust knowledge graph of the blockchain and make investigation and compliance workflows more intuitive and fundamentally trusted.\n\nSolana’s unique account structure and fund transfers through account ownership changes\n\nFirst, let’s examine the account mechanics on Ethereum to better contextualize different account types in Solana.\n\nEthereum wallets support the storage of different types of assets, both fungible and non-fungible. An Ethereum wallet is represented by a single address, which is attributed to an entity or individual, and can be unlocked by a private key.\n\nUnlike Ethereum and other account-based blockchains, Solana has a unique account structure with multiple address types. Solana’s primary addresses are known as System Account Addresses and they have control over all addresses beneath them. System Account Addresses are unlocked by private keys; the other remaining addresses within the wallet do not have designated private keys.\n\nUnder each System Account Address, there are two types of Inventory Account Addresses:\n\nStake Accounts are used to delegate tokens to network validators to potentially earn rewards. Token Accounts come in two forms: Ancillary Token Accounts hold SPL tokens (the equivalent of ERC20s ) or NFTs. Users can create as many of these accounts as they want. For example, a user could create multiple accounts that each hold an amount of e.g. USDC.\n\nAssociated Token Accounts are addresses designated for particular token mints. These accounts simplify SPL token transfers if recipients have multiple Ancillary Token Accounts with the same token. Additionally, users cannot send SPL tokens if recipients do not have Ancillary Token Accounts for the SPL tokens in question. Associated Token Accounts allow users to send SPL tokens even if recipients do not have Ancillary Token Accounts of that mint.\n\nTo create a knowledge graph for Solana, we employed our sophisticated blockchain analytics engine to carry out the clustering process, which we use to group together addresses controlled by the same real-world entity or service. Clustering allows us to start with a single address attributed to a custodian and scale up to identify millions of addresses controlled by that custodian, allowing our users to view more of the activity associated with that custodian. The clustering process typically consists of the following steps (see the below image representing the Chainalysis Clustering Engine for a visual representation):\n\nChainalysis operates full archival nodes and transforms the raw blockchain data retrieved from those nodes into structured and indexed blockchain data. We monitor and detect every interaction on the blockchain that is related to a value transfer. Our investigative team collects ground-truth identifications on individual services and businesses that operate on-chain. We also supplement this data with identifications shared by our network. We feed all of this information into our clustering engine. Hundreds of algorithms detect the unique fingerprint of a given service by analyzing the data produced in steps 1 and 2. We compare the outputs of all the algorithms in a Graph Traversal and Collision Resolution Engine to ensure internal consistency.\n\nIn Solana, clustering maps activity conducted by Token and Stake Accounts to their corresponding System Accounts and consolidates all System Accounts if they are controlled by the same on-chain entity. However, this process also needs to address how funds are transferred to ensure that both historical and current transactions are attributed to the correct owners.\n\nSolana provides two methods for transferring funds. The first involves simply transferring the tokens in question from one account to another, similar to transfers on Ethereum. The second process involves changing the associated ownership of a Token or Stake Account, which is more complicated. Any Solana tracing solution that doesn’t account for this runs the risk of attributing the historical activity of an account’s old owner to the new owner, posing a major problem for investigations.\n\nThe diagram below demonstrates an example of this phenomenon. On the left, we see all transactions conducted by Alice’s and Bob’s respective token accounts before any ownership changes. On the right, after Alice changes the ownership of her account and clustering is applied normally, Bob appears to inherit all of Alice’s token account’s historical transactions. This conclusion is inaccurate because Bob is now falsely associated with Alice’s historical activity.\n\nFor SPL token transfers, changing account ownership is a rare phenomenon. We estimate that this occurs less than 1% of the time. For NFT transfers, we estimate that changing account ownership might occur more than 20% of the time, depending on the NFT marketplace involved. Even in the case of SPL token transfers, errors resulting from that 1% can have cascading effects on clustering.\n\nTo correctly map transactions and eliminate concerns regarding Solana’s account complexities, we created specialized components that:\n\nmonitor the creation, closure, and activity of all Token and Stake Accounts.\n\ntrack all of their current and historical owners.\n\nmodel ownership changes as value transfers and roll those transfers up to the System Account level.\n\nWith these modifications, our Solana model is now similar to what customers see for Ethereum and other account-based blockchains.\n\nHow Solana treats collateral\n\nAnother challenge within Solana is its treatment of collateral. Solana accounts still technically hold collateral used to receive new assets in addition to the new assets themselves, resulting in double counting when wallet wealth is computed.\n\nBy default, this is how transactions involving SOL and wrapped SOL (wSOL) appear on the blockchain:\n\nThe interpretation of the above diagram leads to double counting of a single transaction reflected in the total balance of Wallet A. The inflation of a wallet’s assets will lead to inaccurate conclusions about the amount of funds that can be recovered from an asset seizure or the size of exposure to a given entity. This is in stark contrast to Ethereum, where transactions involving deposited ETH to receive wETH are only counted once, and therefore do not require modifications.\n\nBlockchain Wallet Balance under native coin view Wallet Balance under wrapped token view Total Wallet Balance Ethereum 0 ETH 10 wETH 10 wETH Solana 10 SOL (collateral) 10 wSOL 10 SOL + 10 wSOL\n\nTo ensure collateral is not double-counted as part of a Solana wallet’s wealth, our R&D team created a synthetic address to represent the “escrow” (the collateral contract). In Step 2 in the below diagram, the SOL collateral is moved to an escrow address, which omits double-counting.\n\nAdditional modifications for user-friendly Solana analysis\n\nNinety percent of transfers in Solana involve reward-rent, vote, or non-vote transaction fees. When investigators and analysts graph the transfers of a given entity, this high number of transactions would make it very difficult to see the small number of value transfers relevant to their research.\n\nThe Chainalysis R&D team tackled this obstacle by compressing the aforementioned transfers on Solana into a single synthetic transaction for each transaction type every 24 hours. This preserves the intelligence on these transfers while reducing unnecessary information for the user.\n\nHow Solana support impacts our customers\n\nAs new blockchain protocols are developed – each with unique characteristics – it will be necessary to tweak corresponding research and transaction models for greater efficiency and understanding. Solana support in our investigative suite demonstrates the importance of simple and accurate models to improve investigation and compliance within an otherwise complex network. Our research and development team was able to remove complexity of token account ownership changes by modeling and visualizing changes as value transfers, and map transfers to historical owners to provide accurate investigative conclusions and reliable risk calculations.\n\nMany of our customers have already begun using our Solana support for enhanced blockchain analysis. Maxim Piessen, Co-founder and CTO of Solana-based decentralized credit marketplace Credix, told us about how our support for Solana will help them. “Working with Chainalysis throughout their onboarding of Solana, we were really impressed with the rigor they apply to make sure accurate, actionable findings are surfaced. Advanced transaction monitoring and risk assessment are key to the trust and transparency of our platform; therefore, the reliability of the underlying data is paramount. As we continue to innovate and expand, our vision remains – to build the future of global credit markets.”\n\nAt Chainalysis, we will continue to improve our product offerings and broaden our customer base for robust results. Please direct any questions about Solana support to [email protected].\n\nThis material is for informational purposes only, and is not intended to provide legal, tax, financial, or investment advice. Recipients should consult their own advisors before making these types of decisions. Chainalysis has no responsibility or liability for any decision made or any other acts or omissions in connection with Recipient’s use of this material.\n\nChainalysis does not guarantee or warrant the accuracy, completeness, timeliness, suitability or validity of the information in this report and will not be responsible for any claim attributable to errors, omissions, or other inaccuracies of any part of such material."}]