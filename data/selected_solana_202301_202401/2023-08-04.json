[{"id": 9, "url": "https://news.google.com/rss/articles/CBMiQ2h0dHBzOi8vd3d3LmhvdG5ld2hpcGhvcC5jb20vNzAwNDk4LXN6YS1iaWtpbmktcGhvdG8tZHVtcC1pbnN0YWdyYW3SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 04 Aug 2023 07:00:00 GMT", "title": "SZA Makes Jaws Drop With Her Latest Bikini-Clad Photo Dump On Instagram - HotNewHipHop", "content": "Whether it's her music, her style, or her curves, SZA's online presence is always turning heads and getting rabid fans in the comments. Moreover, she recently took to Instagram with a new photo dump that had fans going wild in their replies. As one fan rightfully pointed out, \"Posting mad random s**t looking mad good smh,\" as she typically does with her potential thirst trap content. In the first image, the 33-year-old basks in the sun in a cream and brown-colored bikini, showing off her abs and body to many's delight. Most people were blown away by her physique and charmed instantly by her fun collection of snaps, with <PERSON> commenting, \"it’s always summer when u r solana.\"\n\nFurthermore, the TDE signer also included a hoodied-up pic in the studio with sweatpants and a cap on, hopefully hinting at something dropping soon. In addition, she does the splits in front of an art piece, poses with <PERSON> and <PERSON> statues, and gets in front of a strange and futuristic mirror that looks like the chrome future from that one SpongeBob episode. Finally, there's an intense game of Jenga captured in real time with engineer <PERSON>. S<PERSON>A better stop playing games and follow up SOS with even more phenomenal music.\n\nRead More: <PERSON><PERSON><PERSON> Doubles Up With Stunning Photo Dumps\n\nSZA Stuns In Latest IG Photo Dump\n\nOf course, the \"Gone Girl\" vocalist is prone to dropping content like this on a pretty regular basis. Still, it doesn't make SZ<PERSON>'s snapshots any less cute, gorgeous, or fun; it feels like you're just watching your friend post as normal. Regardless, she has been spending a lot of time in the studio lately, whether just for fun or actively working on a release. Whatever the case, she can do whatever she wants after the last album's success. In any case, Solana has us engaged no matter what with content like this.\n\nMeanwhile, her recent feature alongside Future on <PERSON> <PERSON>'s \"<PERSON><PERSON><PERSON>K<PERSON>ESIS\" might mean that there's even more to come. Maybe all that studio time has been working on a new record to come out around this time. Even if it doesn't, she might as well be charging people to fawn all over her online at this point. On that note, come back to HNHH for the latest news and updates on SZA.\n\nRead More: SZA Responds To Being Included In Barack Obama’s Summer Playlist, Calls Him Her “Forever President”"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vY29pbmZvbWFuaWEuY29tL3NvbGFuYS1jby1mb3VuZGVyLXNheXMtd2Utd2FudC1yZWd1bGF0b3J5LWNsYXJpdHkv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 04 Aug 2023 07:00:00 GMT", "title": "Solana Co-Founder: <PERSON> Worried Over Robinhood Delisting SOL, We Just Want Regulatory Clarity - Coinfomania", "content": "Solana co-founder <PERSON> is not very worried about a recent move by Robinhood and other U.S.-based platforms to delist SOL as a tradable asset amid regulatory uncertainty. In a CNBC interview, <PERSON><PERSON> labeled Robinhood and other exchanges that took the same step as crypto “tourists.” He also weighed in on ongoing efforts to bring clarity to the U.S. crypto industry.\n\nRecall that Robinhood, with an estimated 23 million users, delisted SOL in June. The crypto asset was tagged alongside others as securities by the United States Securities and Exchange Commission (SEC) in lawsuits against Coinbase and Binance. Although the Solana co-founder hesitated to comment on the ongoing court case, he reckoned that the Robinhood delisting was not a significant concern for the Solana Foundation.\n\nThe Solana co-founder said, “I think it’s pretty understandable for U.S. businesses that are not primarily crypto businesses [to delist SOL]. We see this in every market cycle: crypto tourists and businesses that have very small product lines in crypto and kind of recede when it gets hot in the kitchen. My understanding of folks like Robinhood and other products is that these were really small product lines.” Focusing on the positives since the SEC saga with crypto exchanges, <PERSON> added, “The more important thing is that folks like Coinbase, Binance, and other exchanges that represent the largest majority of trading volume and activity for users are not taking any meaningful action that limits consumer access to these assets. That’s great to see and what I expect to continue to see.”\n\nWe Want Clarity: <PERSON>’s co-founder also expressed optimism that the U.S. crypto industry is on the verge of getting regulatory clarity. According to Gokal, there are a lot of “positive signs” and “more legislative pressure to create more clear and actionable guidelines for how to operate in crypto.”\n\nHe continued, “We just want clarity. I think the lack of regulatory clarity has held the industry back for several years, and it seems we’re on the verge of getting that, hopefully from Congress.”\n\nRaj Gokal praised U.S. Congressmen Patrick McHenry, Ritchie Torres, and others who are promoting the need for clear regulations before lawmakers. He also singled out Coinbase for praise, lauding the exchange’s ongoing effort to provide clear guidelines to the emerging industry."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMic2h0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzMwOTY3L3BvcnQzcy1zb3F1ZXN0LXBsYXRmb3JtLWludGVncmF0ZXMtd2l0aC1zb2xhbmEtYW5ub3VuY2VzLTUwMG0tZ2Vtc3RvbmUtYWlyZHJvcC_SAXZodHRwOi8vYW1wLmNvaW5jb2RleC5jb20vYXJ0aWNsZS8zMDk2Ny9wb3J0M3Mtc29xdWVzdC1wbGF0Zm9ybS1pbnRlZ3JhdGVzLXdpdGgtc29sYW5hLWFubm91bmNlcy01MDBtLWdlbXN0b25lLWFpcmRyb3Av?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 04 Aug 2023 07:00:00 GMT", "title": "Port3's SoQuest Platform Integrates With Solana, Announces 500M Gemstone Airdrop - CoinCodex", "content": "Key takeaways\n\nPort3 announces that SoQuest has now been integrated with Solana\n\nSoQuest Gemstone (SQT) tokens will soon be airdropped to the community\n\nPort3 has released a Gemstone Mining campaign that outlines the requirements for receiving the SQT airdrop\n\nPort3 has announced that its flagship product, SoQuest, has been integrated with the Solana network. This unlocks SoQuest’s seamless task-completion dashboard to be used by all projects in the Solana ecosystem for marketing purposes.\n\n? Exciting news! We have integrated with @solana\n\n\n\nOur quest platform #SoQuest now supports logging in via #Phantomwallet and launching #whitelist activities?\n\n\n\n?To celebrate, we prepared a campaign for #Solana community to explore⤵️https://t.co/Lk3QVVPODT… — Port3 Network ? SoQuest Widget is Out (@Port3Network) August 4, 2023\n\nProQuest enables projects to integrate Web3 tasks on their main site. It enriches community-led marketing campaigns by enabling users of dApps to complete quests and earn crypto rewards.\n\nPort3, the company that built SoQuest, aggregates off-chain and on-chain data to provide a Social Data Layer for projects in Web3. Port3’s SoQuest will be integrated with Solana’s Phantom wallet so that users can complete tasks set by projects and earn community rewards both quickly and easily, without needing to share additional information.\n\nThe Quest-as-a-Service (QaaS) product has a variety of key features, as outlined in a recent post from Port3:\n\nA toolkit that enables the customization and tracking of different community tasks\n\nAn AI bot that can be integrated with community groups on Discord or Telegram\n\nAn innovative artificial intelligence language that can interact with Blockchain Query Language (BQL)\n\nWallet integration via Phantom\n\nIt’s thought that Port3’s new partnership with Solana could unlock a more intuitive way to initiate a common marketing strategy for Web3 projects. Tasks and quests are often set by projects seeking to incentivize users, which can help as platforms seek to test out new features and spread the word about exciting updates.\n\nWhat is the new Gemstone Mining initiative and how can you earn SoQuest’s SQT tokens for the upcoming airdrop?\n\nIn addition to its recent integration with Solana, Port3 has launched SoQuest Mining. The initiative, using the SoQuest task set-up, will pave the way for a future SQT airdrop when the SoQuest Gemstone token is launched on the blockchain.\n\n?? #??????? ?????? Kicks off today!\n\n\n\nTo ensure an equitable distribution of future token #airdrops, we proudly present a six-month mining season⚒\n\n\n\nA total of 500 million #Gemstone supplies will be allocated??\n\n\n\n?https://t.co/f2qqtMvDQbhttps://t.co/BqURjKKMJJ — Port3 Network ? SoQuest Widget is Out (@Port3Network) July 6, 2023\n\nUsers will earn SQT tokens by trading in the Port3 DEX, completing sign-in tasks and user verifications, and creating custom workflows with the Port3 community. SQT is expected to go live on the blockchain later in the year, and the ongoing SoQuest mining initiative marks the beginning of the airdrop campaign.\n\nIn total, there are 500 million SQT available for users completing quests. SQT will be earned in direct proportion to how many tasks were completed, so make sure to check out the mining dashboard to find out exactly what you need to do to earn the airdrop.\n\nWhat is Solana?\n\nSolana (SOL) is a high-performance blockchain that offers lightning-fast transaction speeds, achieving block finality in under one second. It uses an innovative combination of proof-of-stake and proof-of-history to achieve its high performance via the Solana consensus protocol.\n\nSolana supports the development of dApps and custom tokens, including NFTs. Smart contracts on Solana use the Rust programming language, which enables more complex applications to be developed when compared to Solidity — the EVM-based smart contract language.\n\nTo learn more about Solana’s ecosystem, you can discover the 7 best Solana NFT marketplaces here.\n\nSOL price prediction\n\nThe current price of SOL is $22.89 after falling more than 29% since July 14th. However, the CoinCodex price prediction algorithm expects Solana to recover from its recent bear trend. Over the next 30 days, the SOL price prediction is $25.37 — an 11.7% increase from the current price level."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vd3d3LnNlY3VyaXRpZXMuaW8vdG9wLXNvbGFuYS1wbGF0Zm9ybXMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 04 Aug 2023 07:00:00 GMT", "title": "Top 10 Solana Platforms Every Trader Should Know - Securities.io", "content": "Solana entered the market as an open-source project with a focus on scalability and functionality. The network quickly garnered support from users and Dapp developers due to its performance and structure which enabled simultaneous smart contract executions. Today, the Solana ecosystem is a vibrant decentralized community. Here are the top 10 Solana platforms that every trader should know.\n\nSecretum (SER)\n\nSecretum keeps in line with the crypto theme as an OTC trading app and encrypted messenger. The Dapp enables Solana users to communicate securely and privately. You can discuss crucial elements of your digital assets enjoying full encryption.\n\nThe Secretum Dapp supports OTC trading with complete interoperability within Solanan's DeFI and metaverse ecosystems. The system includes features that streamline NFT trading and collecting. Best of all, it's a simplistic onboarding process that only requires you to connect a network wallet.\n\nHDOKI (OKI)\n\nGamers continue to flock to Solana's ecosystem due to its growing selection of titles. The HDOKI platform is Solana's premier gaming option. The network offers a combination of ways for users to secure returns. There's a selection of fun play-to-earn titles that reward users with tradeable tokens.\n\nHDOKI offers more than just 1 on 1 battles. You can join others to create massive clans and better your chances of survival. There are also other rewards-based options such as move-to-earn and learn-to-earn titles that incentivize health and education.\n\nSolice (SLC)\n\nSolice is Solana's answer to Decentraland and other popular blockchain-based metaverses. This Solana-based 3D world features plots of customizable lands that can be traded or collected as NFTs. Users can purchase land and develop it into businesses, rentals, and more.\n\nThe Solice metaverse makes it easy to engage with other Solana users and share ideas and fun times. This metaverse features a selection of entertainment options such as mini-games and the ability to raise pets. When you combine these options with NFT tokenization protocols, you could tokenize your furry friends and bring them with you on your next Solana adventure.\n\nStar Atlas (ATLAS)\n\nStar Atlas is another metaverse project that has the attention of Solana users. This virtual world exists in a digital universe. The intergalactic setting provides loads of entertainment and makes the storyline even more interesting than other metaverse concepts that lack any originality.\n\nThe goal of the game is to secure and trade assets for better spacecraft and eventually, worlds. Users can secure rewards for completing tasks. These rewards can then be used to improve vehicles or equipment. Once you're all stocked up you can take over a planet. Uniquely, users can mine crypto rewards during gameplay.\n\nALF Protocol (ALF)\n\nThe ALF protocol is a favorite among the farming community. This advanced farming aggregator enables you to improve your ROIs via a selection of added options. For example, you can use leverage to gain a stronger position before entering the farming pools.\n\nALF protocol users secure higher rewards on their farming. The system reduces the margin requirements needed for pool providers without reducing security. Additionally, the system features an enhanced liquidation system to protect LPs during volatility.\n\nSpace Falcon (FCON)\n\nThe Space Falcon is another intergalactic metaverse title that has drawn considerable attention in the ecosystem. The network enables users to go a step further than just customizing their ship or taking over planets. Space Falcon allows you to control entire galaxies.\n\nThe protocol features a vibrant in-game marketplace where you can barter for goods and services. Meet up and gather supplies to carry out your next big mission. You can also socialize with other players and build up clans to explore the universe together.\n\nPhantom (PHANTOM)\n\nThe Phantom wallet is a popular noncustodial option that Solana users can leverage to improve their security. This convenient mobile wallet was designed from day one to support advanced NFT and DeFi features. As such, many features are specific to these demands.\n\nFor example, you can easily showcase your NFT collection. The wallet can support multiple networks as well which makes it ideal for people who collect across blockchain. Additionally, you can trade, burn, and list new NFTs. There is even support for in-wallet staking.\n\nOrca (ORCA)\n\nThe Orca DEX has grown to become a popular destination for Solana users. This advanced high-performance DEX offers low fees and fast transaction times. The interface makes it easy for anyone to find and trade digital assets on the exchange,\n\nThe Orca DEX supports a variety of liquidity pools which adds to its usability. Users can secure passive rewards by providing liquidity to staking and farming pools. Notably, Orca is the first Solana-based DEX to focus heavily on onboarding and UX. As such, it operates as a vital liquidity layer within the Solana ecosystem.\n\nHelio (HELIO)\n\nHelio offers a variety of tools that make it simple for any platform to integrate blockchain assets. Primarily, the network is known for its Web3 payments APIs. These portals enable developers to integrate and streamline crypto assets without the need to recode their projects.\n\nHelio offers a suite of tools that include items like pre-built checkout windows that can be embedded in seconds into your web page. The platform supports a variety of e-commerce and has additional features geared toward the growing NFT collectors space. Currently, Helio supports Solana, Polygon, and Ethereum which makes it extremely versatile.\n\nAfflarium (AFF)\n\nAfflarium is a metaverse that leverages virtual reality and other advanced technologies to improve gameplay. The titles are currently active and available as a free download on both mobile and desktop platforms. The Afflarium metaverse is different from any of its competitors in that the user has complete control over the storyline.\n\nEach player can create their journey. This option means that you gain control over the designs, rules, and customizations of your new universe. This open approach to metaverse structure opens the door for more engaging gameplay and innovation.\n\nSolana Platforms You Now Know\n\nSolana is one of the most active Dapp ecosystems in the market. This advanced, high-performance, network continues to draw more users and creators thanks to its excellent options and support. The platforms listed here are some of the most popular options to choose from but are by no means the only Solana projects worth checking out."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiLGh0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvaG93LXRvLWJ1cm4tYW4tbmZ00gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 04 Aug 2023 07:00:00 GMT", "title": "How to Burn an NFT? - Watcher <PERSON>", "content": "Mastering the Art of NFT Annihilation: Unraveling the Mystery of How to Burn an NFT\n\n‍The non-fungible token (NFT) marketplace has become a bustling hub of digital art, innovation, and investment.\n\nBut what happens when you want to obliterate an NFT from existence? Enter the intriguing world of NFT burning.\n\nAlso read: Dogecoin Army Buzzing with Anticipation After <PERSON><PERSON>’s Tweet\n\nThis comprehensive guide will walk you through How to Burn a non-fungible token, including its purpose, steps on various blockchains, and implications for your digital asset’s future.\n\nSource: Information Age\n\nThe Art of NFT Destruction: Can NFTs Be Obliterated?\n\nNon-fungible tokens, once minted on the blockchain, become immutable digital assets.\n\nHowever, you can destroy or “burn” them through a process similar to sending the token into a digital black hole.\n\nThis act, known as “burning,” sends the token to an inaccessible, unspendable address, effectively wiping it from existence.\n\nAlso read: Shiba Inu: $1000 Investment on ShibaSwap Debut, Profit Revealed\n\nBurning an NFT: What Does It Entail?\n\nThe act of burning a non-fungible token is synonymous with completely deleting it. It involves the transfer of a token to an address that remains inaccessible to all.\n\nOnce a non-fungible token is burned, it is impossible to retrieve it. Hence, it is crucial to be sure that burning is your desired course of action.\n\nInterestingly, as the digital asset dissolves, its history and record remain imprinted on the blockchain, providing evidence of its existence and subsequent annihilation.\n\nThe Void Address: Understanding the Token Burn Address\n\nSource: Cointurk\n\nAn NFT burn address is a specialized wallet address that acts as a digital incinerator for tokens. Also known as an “address eater,” this null address is inaccessible and holds no keys.\n\nIt is the ultimate lock without a key.\n\nOn the Ethereum blockchain, the burn address is often represented as 0x0000000000000000000000000000dEaD.\n\nPolygon’s burn address appears as 0x0000000000000000000000000000, while Solana employs the burn address ‘ remove.sol.’\n\nDispatching an NFT to these addresses permanently eliminates it from circulation.\n\nThe Price of NFT Annihilation: Does Burning an NFT Cost Anything?\n\nYes, the process of burning an NFT incurs a cost.\n\nThis transaction fee, colloquially referred to as a “gas fee,” is required to process the burning of your non-fungible token.\n\nThe fee can fluctuate based on the current supply and demand on the blockchain.\n\nThe cost of burning an NFT can range anywhere from $0.00025 to $100.\n\nThe Step-by-Step Guide: How to Burn an NFT\n\nThe burning process is straightforward. Below is a step-by-step guide to burning your NFT on popular blockchains like Ethereum, Solana, and Polygon.\n\nIncinerating an Ethereum NFT\n\nHere’s your guide on How to Burn an NFT on Ethereum:\n\nAccess the wallet or marketplace holding your NFT. Choose the NFT you wish to burn and hit the “Transfer” button. Direct your NFT to Ethereum’s official burn address, which is 0x00000000000000000000000000000000dEaD. Settle the required gas fees to transfer your NFT and click “Confirm.”\n\nWith these steps, your NFT will be permanently dissolved and removed from the marketplace. Remember, gas fees can vary based on supply and demand on the Ethereum blockchain.\n\nBurning a Polygon NFT\n\nThe process of burning an NFT on the Polygon blockchain mirrors that of Ethereum, except for the burn address.\n\nHere’s how:\n\nNavigate to the item page for the NFT you wish to burn. Click on the transfer button. Enter the Polygon burn address: 0x000000000000000000000000000000000000. Hit “Transfer,” confirm the transaction with your wallet, pay the gas fees, and you’re done!\n\nObliterating a Solana NFT\n\nBurning an NFT on Solana differs slightly from other NFT blockchains.\n\nThe Phantom Wallet, a popular Solana wallet, has built-in burning features that reward users with SOL for burning certain NFTs.\n\nFor NFTs that aren’t supported by the built-in burning features, use the following steps:\n\nOpen your Solana wallet and locate the NFT you wish to burn. Add the ‘ remove.sol ‘ wallet address in the recipient field. Confirm the transaction and pay the associated gas fees.\n\nThe ‘remove.sol’ address is a graveyard for unwanted NFTs.\n\nOnce you send your NFT there, there’s no turning back; the burning process becomes irreversible.\n\nMonetary Compensation for Burning an NFT: Is It Possible?\n\nMonetary compensation for burning an NFT is platform-dependent.\n\nWhile Ethereum and Polygon offer no return, platforms like the Phantom Wallet provide a unique mechanism that rewards users with SOL for burning certain NFTs.\n\nHence, it’s advisable to check the specifics of the platform used to determine if it offers a reward.\n\nReasons to Burn an NFT: Why Would You Do It?\n\nThe act of burning an NFT serves several purposes.\n\nIt can be a strategic tool to control the value of an asset. Since NFTs appreciate when demand surpasses supply, reducing the supply via burning can potentially increase the price of the remaining tokens.\n\nBurning can also expunge flawed or redundant tokens from circulation, which is particularly useful when an excess number of tokens devalues individual tokens.\n\nMoreover, burning NFTs can help build trust and credibility, especially during the early phases of a token’s lifecycle.\n\nCreators often burn unsold tokens post-launch to demonstrate accountability and safeguard the interests of token buyers, enhancing their credibility.\n\nCertain token collections also incentivize holders to burn their tokens. For instance, Gary Vaynerchuk recently launched Burn Island, a burn-to-redeem program that rewards VeeFriends holders for burning their tokens.\n\nSecurity Measures: Can Someone Else Burn My NFT?\n\nNo one can burn your non-fungible token without your consent.\n\nUnless they have access to your wallet keys, your NFT is safe.\n\nHowever, some smart contracts might incorporate a “burn mechanism,” enabling you to destroy your token.\n\nThis implies that you can design a smart contract to permit burning your non-fungible token or even allow others to do so.\n\nThe Permanence of Burning: Can a Burned, Non-Fungible Token Be Recovered?\n\nThe burning is a permanent process.\n\nOnce you dispatch a unit to an unspendable address, it becomes irretrievable.\n\nHowever, it’s possible to mint a new one with similar properties and attributes, although it won’t be identical to the original one.\n\nThe Aftermath: What Happens to Tokens?\n\nBurning a token doesn’t physically destroy the token. Instead, it sends the NFT to a null address, rendering the token unattainable.\n\nOnce you send your token to a null address, it’s lost to the ether because private keys don’t exist for it.\n\nConclusion\n\nBurning a token might seem counterintuitive at first. However, it’s a strategic tool for managing the value of your tokens and your non-fungible token community.\n\nWhether you’re a creator seeking accountability or a non-fungible token buyer looking to capitalize on increasing values, understanding How to Burn a token can be a game-changer.\n\nOur comprehensive guide equips you to navigate the NFT inferno with confidence.\n\nSo torch those tokens, and let’s see what happens!"}]