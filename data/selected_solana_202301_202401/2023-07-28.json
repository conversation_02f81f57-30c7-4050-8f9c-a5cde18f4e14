[{"id": 3, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3Mvc29sYW5hL2J1bGxpc2gtcHJpY2UtZm9ybWF0aW9uLWZvci1zb2xhbmEtaXMtMzAtYXJvdW5kLXRoZS1jb3JuZXIv0gFjaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vbmV3cy9zb2xhbmEvYnVsbGlzaC1wcmljZS1mb3JtYXRpb24tZm9yLXNvbGFuYS1pcy0zMC1hcm91bmQtdGhlLWNvcm5lci9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 02:01:54 GMT", "title": "Bullish Price Formation For Solana – Is $30 Around The Corner? - NewsBTC", "content": "Solana (SOL) has experienced a notable downturn recently, unable to sustain itself above the $32 mark. The altcoin remains below a strong resistance level, but a potential breakthrough could lead to a 20% price appreciation.\n\nRelated Reading\n\nPEPE Whales Load Up Their Bags As Memecoin Jumps 13% 9 months ago\n\nIn the last 24 hours, SOL saw a modest surge of 2.2%, but its performance on the weekly chart shows minimal upward movement. Despite the challenges, there are two essential bullish signals to consider: the morning star reversal sign and a bullish pennant formation.\n\nThese signals suggest that if SOL surpasses the immediate resistance, the coin might aim for a rally back to levels it reached two weeks ago. In the broader market context, Bitcoin’s re-entry into the $29,000 price level has caused retracements in altcoins on their respective charts.\n\nTherefore, for SOL to surpass the immediate resistance, it will require strong support from the broader market. The market capitalization of SOL has also experienced a decline, indicating a slight weakening in buying strength.\n\nSolana Price Analysis: One-Day Chart"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtc2NhbGFibGUtZGVjZW50cmFsaXplZC1uZXR3b3JrLWRhcHBzLTIwMDI0Mjc5My5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "What is Sol<PERSON>? A Scalable, Decentralized Network for Dapps - Yahoo Finance", "content": "Decentralized applications, or dapps, are widely regarded as one of the key use cases for blockchain technology.\n\nIn the last few years, dapps have come on in leaps and bounds, with dapp developers launching everything from games to decentralized finance (DeFi) platforms on blockchain—and a concomitant explosion in user interest.\n\nBut there's a problem. The vast majority of these dapps run on Ethereum, which has struggled to keep up with rampant demand—leading to congestion on the network and soaring transaction fees.\n\nNow Solana, a blockchain platform that had its beginnings in 2017, is aiming to step into the breach, and succeed where Ethereum is currently struggling.\n\nWhat is Solana?\n\nSolana is an advanced open-source blockchain project that looks to leverage several breakthrough technologies to power the next generation of dapps.\n\nThe project is focused on providing a highly scalable, secure, and maximally decentralized platform that can support potentially thousands of nodes without sacrificing throughput—helping to avoid some of the challenges faced by competing systems.\n\nIt was founded in 2017 during the ICO boom and raised more than $25 million across various private and public sale rounds. The platform went to mainnet in March 2020, but is still operating as a beta release.\n\nHow does Solana work?\n\nOne of Solana’s key distinguishing features is its Proof of Stake (PoS) consensus system, which is reinforced by something known as Tower Consensus. This is a variant of a system that enables distributed networks to reach consensus despite attacks from malicious nodes, known as Practical Byzantine Fault Tolerance (PBFT).\n\nSolana's implementation of PBFT enforces a global source of time across the blockchain through a second novel protocol known as Proof of History (PoH). This essentially provides a chronicle of previous events on the blockchain, ensuring that there's a common record of what happened and when for permanent reference.\n\nTower Consensus leverages this synchronized clock to reduce the processing power needed to verify transactions, since the timestamps of previous transactions no longer need to be computed. This helps Solana achieve a throughput that dwarfs most competitors (more on this later).\n\nBeyond this, Solana includes a number of other innovations that help it stand out from the competition. Among these is its transaction parallelization technology, known as Sealevel. This allows for a parallel smart contracts runtime that optimizes resources and ensures that Solana can scale horizontally across GPUs and SSDs, which should help the platform scale to meet demands.\n\nStory continues\n\nSolana also completely nixes the mempool system used by other platforms, and instead forwards transactions to validators even before the previous batch of transactions is finalized. This helps to maximize confirmation speed and boost the number of transactions that can be handled both concurrently and in parallel. This technology is known as 'Gulf Stream'.\n\nWhat's so special about it?\n\nWhen it comes to decentralized applications, speed matters. As is evidenced by the bottlenecks currently faced by the Ethereum network. Solana, however, doesn't currently suffer from these issues due to its high throughput architecture.\n\nSolana claims that its blockchain is capable of sustaining more than 50,000 transactions per second (TPS) at peak load, which would make it arguably the fastest blockchain currently operating. To put this into perspective, this is close to 1,000 times faster than Bitcoin (max throughput ~5-7 TPS) and more than 3,000 times faster than Ethereum (max throughput ~15 TPS).\n\nMoreover, Solana claims an average block time of 400 to 800 milliseconds and an average transaction fee of 0.000005 SOL (or a tiny fraction of one cent). This, combined with its massive scalability, makes it well-positioned to serve up decentralized applications that can support potentially tens of thousands of simultaneous users without buckling under the load.\n\nSolana achieves this scalability without resorting to second-layer or off-chain technologies and doesn't use any form of sharding. This makes it one of the few layer 1 blockchains capable of achieving more than 1,000 TPS.\n\nUnlike some platforms, practically anybody can get up and running with a Solana validator and help to secure the network. The process is completely permissionless, though users will need to maintain some basic hardware to participate—namely a server that meets the minimum specifications outlined here. In total, the network currently boasts close to 1,000 validators, making it one of the more widely distributed blockchains.\n\nWhat is the SOL token?\n\nSolana, like the vast majority of smart contract platforms, features its own native gas token — known as SOL. As the gas token, all transactions and smart contract operations on Solana will consume SOL.\n\nThe SOL token can also be staked to help support the security of the network, allowing users to earn a proportion of the inflation as a reward. Though the feature isn't currently available, SOL tokens will also be used for on-chain governance eventually.\n\nThe token was first launched on the Solana beta mainnet in March 2020 and is currently one of the top 20 largest cryptocurrencies by market capitalization.\n\nThis story was updated to clarify the nature of Tower Consensus.\n\nThis story was originally published on May 14, 2021 and was updated on July 28, 2023"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtc2NhbGFibGUtZGVjZW50cmFsaXplZC1uZXR3b3JrLWRhcHBzLTIwMDI0Mjc5My5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "What is Sol<PERSON>? A Scalable, Decentralized Network for Dapps - Yahoo Finance", "content": "Decentralized applications, or dapps, are widely regarded as one of the key use cases for blockchain technology.\n\nIn the last few years, dapps have come on in leaps and bounds, with dapp developers launching everything from games to decentralized finance (DeFi) platforms on blockchain—and a concomitant explosion in user interest.\n\nBut there's a problem. The vast majority of these dapps run on Ethereum, which has struggled to keep up with rampant demand—leading to congestion on the network and soaring transaction fees.\n\nNow Solana, a blockchain platform that had its beginnings in 2017, is aiming to step into the breach, and succeed where Ethereum is currently struggling.\n\nWhat is Solana?\n\nSolana is an advanced open-source blockchain project that looks to leverage several breakthrough technologies to power the next generation of dapps.\n\nThe project is focused on providing a highly scalable, secure, and maximally decentralized platform that can support potentially thousands of nodes without sacrificing throughput—helping to avoid some of the challenges faced by competing systems.\n\nIt was founded in 2017 during the ICO boom and raised more than $25 million across various private and public sale rounds. The platform went to mainnet in March 2020, but is still operating as a beta release.\n\nHow does Solana work?\n\nOne of Solana’s key distinguishing features is its Proof of Stake (PoS) consensus system, which is reinforced by something known as Tower Consensus. This is a variant of a system that enables distributed networks to reach consensus despite attacks from malicious nodes, known as Practical Byzantine Fault Tolerance (PBFT).\n\nSolana's implementation of PBFT enforces a global source of time across the blockchain through a second novel protocol known as Proof of History (PoH). This essentially provides a chronicle of previous events on the blockchain, ensuring that there's a common record of what happened and when for permanent reference.\n\nTower Consensus leverages this synchronized clock to reduce the processing power needed to verify transactions, since the timestamps of previous transactions no longer need to be computed. This helps Solana achieve a throughput that dwarfs most competitors (more on this later).\n\nBeyond this, Solana includes a number of other innovations that help it stand out from the competition. Among these is its transaction parallelization technology, known as Sealevel. This allows for a parallel smart contracts runtime that optimizes resources and ensures that Solana can scale horizontally across GPUs and SSDs, which should help the platform scale to meet demands.\n\nStory continues\n\nSolana also completely nixes the mempool system used by other platforms, and instead forwards transactions to validators even before the previous batch of transactions is finalized. This helps to maximize confirmation speed and boost the number of transactions that can be handled both concurrently and in parallel. This technology is known as 'Gulf Stream'.\n\nWhat's so special about it?\n\nWhen it comes to decentralized applications, speed matters. As is evidenced by the bottlenecks currently faced by the Ethereum network. Solana, however, doesn't currently suffer from these issues due to its high throughput architecture.\n\nSolana claims that its blockchain is capable of sustaining more than 50,000 transactions per second (TPS) at peak load, which would make it arguably the fastest blockchain currently operating. To put this into perspective, this is close to 1,000 times faster than Bitcoin (max throughput ~5-7 TPS) and more than 3,000 times faster than Ethereum (max throughput ~15 TPS).\n\nMoreover, Solana claims an average block time of 400 to 800 milliseconds and an average transaction fee of 0.000005 SOL (or a tiny fraction of one cent). This, combined with its massive scalability, makes it well-positioned to serve up decentralized applications that can support potentially tens of thousands of simultaneous users without buckling under the load.\n\nSolana achieves this scalability without resorting to second-layer or off-chain technologies and doesn't use any form of sharding. This makes it one of the few layer 1 blockchains capable of achieving more than 1,000 TPS.\n\nUnlike some platforms, practically anybody can get up and running with a Solana validator and help to secure the network. The process is completely permissionless, though users will need to maintain some basic hardware to participate—namely a server that meets the minimum specifications outlined here. In total, the network currently boasts close to 1,000 validators, making it one of the more widely distributed blockchains.\n\nWhat is the SOL token?\n\nSolana, like the vast majority of smart contract platforms, features its own native gas token — known as SOL. As the gas token, all transactions and smart contract operations on Solana will consume SOL.\n\nThe SOL token can also be staked to help support the security of the network, allowing users to earn a proportion of the inflation as a reward. Though the feature isn't currently available, SOL tokens will also be used for on-chain governance eventually.\n\nThe token was first launched on the Solana beta mainnet in March 2020 and is currently one of the top 20 largest cryptocurrencies by market capitalization.\n\nThis story was updated to clarify the nature of Tower Consensus.\n\nThis story was originally published on May 14, 2021 and was updated on July 28, 2023"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtc2NhbGFibGUtZGVjZW50cmFsaXplZC1uZXR3b3JrLWRhcHBzLTIwMDI0Mjc5My5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "What is Sol<PERSON>? A Scalable, Decentralized Network for Dapps - Yahoo Finance", "content": "Decentralized applications, or dapps, are widely regarded as one of the key use cases for blockchain technology.\n\nIn the last few years, dapps have come on in leaps and bounds, with dapp developers launching everything from games to decentralized finance (DeFi) platforms on blockchain—and a concomitant explosion in user interest.\n\nBut there's a problem. The vast majority of these dapps run on Ethereum, which has struggled to keep up with rampant demand—leading to congestion on the network and soaring transaction fees.\n\nNow Solana, a blockchain platform that had its beginnings in 2017, is aiming to step into the breach, and succeed where Ethereum is currently struggling.\n\nWhat is Solana?\n\nSolana is an advanced open-source blockchain project that looks to leverage several breakthrough technologies to power the next generation of dapps.\n\nThe project is focused on providing a highly scalable, secure, and maximally decentralized platform that can support potentially thousands of nodes without sacrificing throughput—helping to avoid some of the challenges faced by competing systems.\n\nIt was founded in 2017 during the ICO boom and raised more than $25 million across various private and public sale rounds. The platform went to mainnet in March 2020, but is still operating as a beta release.\n\nHow does Solana work?\n\nOne of Solana’s key distinguishing features is its Proof of Stake (PoS) consensus system, which is reinforced by something known as Tower Consensus. This is a variant of a system that enables distributed networks to reach consensus despite attacks from malicious nodes, known as Practical Byzantine Fault Tolerance (PBFT).\n\nSolana's implementation of PBFT enforces a global source of time across the blockchain through a second novel protocol known as Proof of History (PoH). This essentially provides a chronicle of previous events on the blockchain, ensuring that there's a common record of what happened and when for permanent reference.\n\nTower Consensus leverages this synchronized clock to reduce the processing power needed to verify transactions, since the timestamps of previous transactions no longer need to be computed. This helps Solana achieve a throughput that dwarfs most competitors (more on this later).\n\nBeyond this, Solana includes a number of other innovations that help it stand out from the competition. Among these is its transaction parallelization technology, known as Sealevel. This allows for a parallel smart contracts runtime that optimizes resources and ensures that Solana can scale horizontally across GPUs and SSDs, which should help the platform scale to meet demands.\n\nStory continues\n\nSolana also completely nixes the mempool system used by other platforms, and instead forwards transactions to validators even before the previous batch of transactions is finalized. This helps to maximize confirmation speed and boost the number of transactions that can be handled both concurrently and in parallel. This technology is known as 'Gulf Stream'.\n\nWhat's so special about it?\n\nWhen it comes to decentralized applications, speed matters. As is evidenced by the bottlenecks currently faced by the Ethereum network. Solana, however, doesn't currently suffer from these issues due to its high throughput architecture.\n\nSolana claims that its blockchain is capable of sustaining more than 50,000 transactions per second (TPS) at peak load, which would make it arguably the fastest blockchain currently operating. To put this into perspective, this is close to 1,000 times faster than Bitcoin (max throughput ~5-7 TPS) and more than 3,000 times faster than Ethereum (max throughput ~15 TPS).\n\nMoreover, Solana claims an average block time of 400 to 800 milliseconds and an average transaction fee of 0.000005 SOL (or a tiny fraction of one cent). This, combined with its massive scalability, makes it well-positioned to serve up decentralized applications that can support potentially tens of thousands of simultaneous users without buckling under the load.\n\nSolana achieves this scalability without resorting to second-layer or off-chain technologies and doesn't use any form of sharding. This makes it one of the few layer 1 blockchains capable of achieving more than 1,000 TPS.\n\nUnlike some platforms, practically anybody can get up and running with a Solana validator and help to secure the network. The process is completely permissionless, though users will need to maintain some basic hardware to participate—namely a server that meets the minimum specifications outlined here. In total, the network currently boasts close to 1,000 validators, making it one of the more widely distributed blockchains.\n\nWhat is the SOL token?\n\nSolana, like the vast majority of smart contract platforms, features its own native gas token — known as SOL. As the gas token, all transactions and smart contract operations on Solana will consume SOL.\n\nThe SOL token can also be staked to help support the security of the network, allowing users to earn a proportion of the inflation as a reward. Though the feature isn't currently available, SOL tokens will also be used for on-chain governance eventually.\n\nThe token was first launched on the Solana beta mainnet in March 2020 and is currently one of the top 20 largest cryptocurrencies by market capitalization.\n\nThis story was updated to clarify the nature of Tower Consensus.\n\nThis story was originally published on May 14, 2021 and was updated on July 28, 2023"}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiWWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtc2NhbGFibGUtZGVjZW50cmFsaXplZC1uZXR3b3JrLWRhcHBzLTIwMDI0Mjc5My5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 28 Jul 2023 07:00:00 GMT", "title": "What is Sol<PERSON>? A Scalable, Decentralized Network for Dapps - Yahoo Finance", "content": "Decentralized applications, or dapps, are widely regarded as one of the key use cases for blockchain technology.\n\nIn the last few years, dapps have come on in leaps and bounds, with dapp developers launching everything from games to decentralized finance (DeFi) platforms on blockchain—and a concomitant explosion in user interest.\n\nBut there's a problem. The vast majority of these dapps run on Ethereum, which has struggled to keep up with rampant demand—leading to congestion on the network and soaring transaction fees.\n\nNow Solana, a blockchain platform that had its beginnings in 2017, is aiming to step into the breach, and succeed where Ethereum is currently struggling.\n\nWhat is Solana?\n\nSolana is an advanced open-source blockchain project that looks to leverage several breakthrough technologies to power the next generation of dapps.\n\nThe project is focused on providing a highly scalable, secure, and maximally decentralized platform that can support potentially thousands of nodes without sacrificing throughput—helping to avoid some of the challenges faced by competing systems.\n\nIt was founded in 2017 during the ICO boom and raised more than $25 million across various private and public sale rounds. The platform went to mainnet in March 2020, but is still operating as a beta release.\n\nHow does Solana work?\n\nOne of Solana’s key distinguishing features is its Proof of Stake (PoS) consensus system, which is reinforced by something known as Tower Consensus. This is a variant of a system that enables distributed networks to reach consensus despite attacks from malicious nodes, known as Practical Byzantine Fault Tolerance (PBFT).\n\nSolana's implementation of PBFT enforces a global source of time across the blockchain through a second novel protocol known as Proof of History (PoH). This essentially provides a chronicle of previous events on the blockchain, ensuring that there's a common record of what happened and when for permanent reference.\n\nTower Consensus leverages this synchronized clock to reduce the processing power needed to verify transactions, since the timestamps of previous transactions no longer need to be computed. This helps Solana achieve a throughput that dwarfs most competitors (more on this later).\n\nBeyond this, Solana includes a number of other innovations that help it stand out from the competition. Among these is its transaction parallelization technology, known as Sealevel. This allows for a parallel smart contracts runtime that optimizes resources and ensures that Solana can scale horizontally across GPUs and SSDs, which should help the platform scale to meet demands.\n\nStory continues\n\nSolana also completely nixes the mempool system used by other platforms, and instead forwards transactions to validators even before the previous batch of transactions is finalized. This helps to maximize confirmation speed and boost the number of transactions that can be handled both concurrently and in parallel. This technology is known as 'Gulf Stream'.\n\nWhat's so special about it?\n\nWhen it comes to decentralized applications, speed matters. As is evidenced by the bottlenecks currently faced by the Ethereum network. Solana, however, doesn't currently suffer from these issues due to its high throughput architecture.\n\nSolana claims that its blockchain is capable of sustaining more than 50,000 transactions per second (TPS) at peak load, which would make it arguably the fastest blockchain currently operating. To put this into perspective, this is close to 1,000 times faster than Bitcoin (max throughput ~5-7 TPS) and more than 3,000 times faster than Ethereum (max throughput ~15 TPS).\n\nMoreover, Solana claims an average block time of 400 to 800 milliseconds and an average transaction fee of 0.000005 SOL (or a tiny fraction of one cent). This, combined with its massive scalability, makes it well-positioned to serve up decentralized applications that can support potentially tens of thousands of simultaneous users without buckling under the load.\n\nSolana achieves this scalability without resorting to second-layer or off-chain technologies and doesn't use any form of sharding. This makes it one of the few layer 1 blockchains capable of achieving more than 1,000 TPS.\n\nUnlike some platforms, practically anybody can get up and running with a Solana validator and help to secure the network. The process is completely permissionless, though users will need to maintain some basic hardware to participate—namely a server that meets the minimum specifications outlined here. In total, the network currently boasts close to 1,000 validators, making it one of the more widely distributed blockchains.\n\nWhat is the SOL token?\n\nSolana, like the vast majority of smart contract platforms, features its own native gas token — known as SOL. As the gas token, all transactions and smart contract operations on Solana will consume SOL.\n\nThe SOL token can also be staked to help support the security of the network, allowing users to earn a proportion of the inflation as a reward. Though the feature isn't currently available, SOL tokens will also be used for on-chain governance eventually.\n\nThe token was first launched on the Solana beta mainnet in March 2020 and is currently one of the top 20 largest cryptocurrencies by market capitalization.\n\nThis story was updated to clarify the nature of Tower Consensus.\n\nThis story was originally published on May 14, 2021 and was updated on July 28, 2023"}]