[{"id": 0, "url": "https://news.google.com/rss/articles/CBMijwFodHRwczovL3d3dy5kZWxtYXJ0aW1lcy5uZXQvYXJ0L3N0b3J5LzIwMjMtMTAtMjkvc29sYW5hLWJlYWNoLXlvdXRoLXRvLXBlcmZvcm0ta2V5LXJvbGUtaW4tb2xkLWdsb2Jlcy1ob3ctdGhlLWdyaW5jaC1zdG9sZS1jaHJpc3RtYXMtcHJvZHVjdGlvbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 29 Oct 2023 07:00:00 GMT", "title": "Solana Beach youth to perform key role in Old Globe's 'How the Grinch Stole Christmas!' production - Del <PERSON>", "content": "<PERSON>\n\n(Inside The Style Studio\n\n)\n\nSolana Beach resident <PERSON>, 11, is excited to make her professional debut as <PERSON><PERSON><PERSON> in <PERSON><PERSON>’s “How the Grinch Stole Christmas!” in the Old Globe Theatre’s 26th annual production. The show will run from Nov. 8 to Dec. 31, with the opening on Sunday, Nov. 12, on the <PERSON> and <PERSON><PERSON> Shi<PERSON> Stage in the Old Globe Theatre, part of the Globe’s Conrad Prebys Theatre Center in San Diego’s Balboa Park.\n\n<PERSON>, a student at The Children’s School, has appeared in a number of productions, including Musical Theatre: <PERSON> in Annie Jr.; <PERSON> Anna in Frozen Jr.; <PERSON><PERSON><PERSON><PERSON> in Jungle Book Jr.; <PERSON> in Shrek, ensemble in Moana Jr.; ensemble in High School Musical Jr. (Junior Actors Company); and Bowery Brigade in Newsies Jr. (J* Company).\n\n<PERSON> has also been a member of dance, lyrical and competition teams (Fusion Dance Solana Beach), and been mentored by vocal coach <PERSON>.\n\nWhen asked via email what she likes best about performing <PERSON> stated, “I like being able to portray different characters. It’s interesting to step into someone else’s shoes. I really like who I am, but it’s also fun to get the chance to be someone else — someone I’d never otherwise be.”\n\nIn response to a question on what has been her favorite acting experience so far <PERSON> said, “Usually my favorite acting experience is one that I’m in or one that I just finished. I really get into the character and have such a good time with the cast. So right now, it’s definitely “How the Grinch Stole Christmas” because it’s amazing getting to be <PERSON>-<PERSON>, and working with the cast and creatives is so awesome.”\n\nAs for her future goals in life, <PERSON> shared, “I want to be on Broadway or something that involves acting. Last spring break, my parents took me to New York City for the first time, and I saw two Broadway shows. I even stood by the stagedoor after <PERSON> Girl and got <PERSON> <PERSON>’s autograph! That’s when I knew that I wanted to perform professionally.”\n\nTickets for “How the Grinch Stole Christmas!” are now on sale at www.TheOldGlobe.org, by phone at (*************, and in person at The Old Globe’s Box Office in Balboa Park.\n\n"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMidGh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzEwLzI5L2ludmVzdG1lbnQtZ2lhbnQtdmFuZWNrLXJldmVhbHMtbWFzc2l2ZS1idWxsLWNhc2Utc29sYW5hLXNvbC1wcmljZS10YXJnZXQtZm9yLTIwMzAv0gF4aHR0cHM6Ly9kYWlseWhvZGwuY29tLzIwMjMvMTAvMjkvaW52ZXN0bWVudC1naWFudC12YW5lY2stcmV2ZWFscy1tYXNzaXZlLWJ1bGwtY2FzZS1zb2xhbmEtc29sLXByaWNlLXRhcmdldC1mb3ItMjAzMC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 29 Oct 2023 07:00:00 GMT", "title": "Investment Giant VanEck Reveals Massive Bull Case Solana (SOL) Price Target for 2030 - The Daily Hodl", "content": "Digital assets manager <PERSON>Eck has unveiled its forecast for Ethereum (ETH) rival Solana (SOL) looking out into the year 2030.\n\nIn a new report, VanEck analysts <PERSON> and <PERSON> say that Solana’s founders have been a success story in blockchain scaling through experimentation and applied science.\n\n“While other chains have chosen scaling paths that cleverly circumnavigate the limitations of distributed ledgers, Solana has instead chosen to push to the limits of technological feasibility problems and work backward from there. The Ethereum ecosystem and many others have chosen a modular vision where different blockchains specialize in the core functions of a layer-1 chain.\n\nOn the other hand, Solana has plowed ahead, trying to wring greater transaction throughput by optimizing every component of its own blockchain to be hyper-efficient. Consequently, Solana is vastly more capable than any of its legacy competitors regarding blockchain processing capabilities. Parallel to this, but much more importantly, Solana has translated its pioneering spirit into an ecosystem philosophy of risk-taking and techno-optimism.”\n\nThe analysts lay out three potential scenarios for Solana that could play out over the remainder of the 2020s: a bear, base and bullish case.\n\nVanEck centers its base case around the idea that Solana accounts for about 30% of all crypto adoption in 2030. The analysts also forecast that SOL monetizes at about 20% the rate of ETH and achieves less than half of ETH’s market shares due to a “fundamental difference in community philosophy.”\n\nIf Solana manages to stand on its own feet and steal more market value away from Ethereum, VanEck has a bull case of $3,211 per SOL by 2030, about a 100x move from today’s prices.\n\n“Were Solana to avoid Ethereum’s event horizon and achieve Ethereum-like dominance, our bull case reveals $51.8 billion in revenues and a $3,211 price target in 2030.”\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on X Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any losses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/spainter_vfx"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vY3J5cHRvcG90YXRvLmNvbS9zb2xhbmEtc29sLXByaWNlLXByZWRpY3Rpb24tdmFuZWNrLXdpdGgtbWFzc2l2ZS1mb3JlY2FzdC1idXQtdGhlcmVzLWEtY2F0Y2gv0gFpaHR0cHM6Ly9jcnlwdG9wb3RhdG8uY29tL3NvbGFuYS1zb2wtcHJpY2UtcHJlZGljdGlvbi12YW5lY2std2l0aC1tYXNzaXZlLWZvcmVjYXN0LWJ1dC10aGVyZXMtYS1jYXRjaC8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 29 Oct 2023 07:00:00 GMT", "title": "Solana (SOL) Price Prediction: <PERSON><PERSON><PERSON> With Massive Forecast But There’s a Catch - CryptoPotato", "content": "TL;DR\n\nPrice Forecast: Analysts predict a price range from $9.81 (bearish) to $3,211.28 (bullish) for Solana by 2030.\n\nAnalysts predict a price range from $9.81 (bearish) to $3,211.28 (bullish) for Solana by 2030. Strengths: Solana boasts unmatched data throughput and is significantly more affordable than competitors for tasks like NFT minting.\n\nSolana boasts unmatched data throughput and is significantly more affordable than competitors for tasks like NFT minting. Growth Indicators: A notable 74% increase in assets under management (AUM) by Solana’s DeFi apps was observed in October.\n\nVanEck, the global asset manager and early adopter of ETFs and foreign equity investments, predicted a 10,000% rise for the Solana price in its most bullish 2030 scenario.\n\n2030 is not very far off for a 10,000% return on any asset. But to be fair to those with open short interest in Solana, the worst-case scenario from VanEck analysts pegs SOL at $9.81 in 2030.\n\nSolana Price Prediction 2030: $3,211\n\nHere’s what the VanEck analysts had to say:\n\n“By 2030, our Solana valuation scenarios project a SOL price ranging from a bearish $9.81 to a bullish $3,211.28, anchored by varied market shares and revenue estimations across key sectors.”\n\nThe note’s authors project Solana to host the first DeFi app with 100 million users. Meanwhile, they assume SOL monetizes at 20% of Ethereum’s take rate. Furthermore, they conservatively estimate SOL at less than half of ETH’s market share in 2030.\n\n“In this note, we model a scenario in which Solana is the first blockchain to host an application that onboards 100M+ users.”\n\nHere’s how they envision Solana nailing that killer app: Usability. The VanEck analysts write:\n\n“As it stands, Solana’s data throughput exceeds that of any other blockchain in existence. In fact, Solana’s data capacity exceeds that of most planned blockchains.”\n\nIn addition to usability, or because of it, the way the VanEck authors define it in their note, there’s Solana’s affordability.\n\nSolana Affordable DeFi Could Scale Fast\n\nA Nansen analyst recently compared Solana with Ethereum and Polygon for minting a million NFTs. Ethereum’s costs were $33.6 million, and Polygon’s were $32,800. On Solana, it would cost $113.\n\nBecause of its speed and costs, the fundamentals of its business model are promising, with massive inflows from institutional investors as well as die-hard crypto devs and users.\n\nAs Solana’s price rose in October, the total number of assets under management (AUM) by Solana DeFi apps rose by 74%.\n\nNo stranger to innovation, VanEck has long been bullish on cryptocurrencies. In February, the crypto-friendly global fund noted in a report that a 3% Bitcoin allocation substantially improves portfolio diversification and performance."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiUmh0dHBzOi8vYW1iY3J5cHRvLmNvbS9zb2xhbmFzLTIwMzAtdmFsdWF0aW9uLWNvdWxkLWJlLWEtc2lnaHQtdG8tYmVob2xkLWhlcmVzLXdoeS_SAVZodHRwczovL2FtYmNyeXB0by5jb20vc29sYW5hcy0yMDMwLXZhbHVhdGlvbi1jb3VsZC1iZS1hLXNpZ2h0LXRvLWJlaG9sZC1oZXJlcy13aHkvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 29 Oct 2023 07:00:00 GMT", "title": "Solana’s 2030 valuation could be a sight to behold – here’s why - AMBCrypto News", "content": "Solana may need to attract more developers despite the recent recovery.\n\nDepending on the state of the market in 2030, SOL may either be worth tens or thousands of dollars.\n\nIn the crypto market, it is not unusual for a project that was once pronounced irrelevant to revive. In Solana’s [SOL] case, the recovery since the FTX contagion has been one that may not be forgotten for a long time. For this reason, asset management firm VanEck, released a report discussing the future potential of the blockchain.\n\nHow much are 1,10,100 SOLs worth today?\n\nSolana rides on the SCP waves\n\nAccording to VanEck, Solana’s strides with respect to Smart Contract Platforms (SCPs) is one factor that has kept the project influential. By definition, SCPs are systems that do not require any third-party interference when executing contracts on a blockchain.\n\nWhile there are other SCP-compatible blockchains like the BNB Chain and Ethereum [ETH], none has been able to match Solana’s dominance in the sector. The VanEck report noted that the sector is positioned to experience a surge in adoption going forward.\n\nBut it also noted that to grow exponentially, it would need a killer application. <PERSON><PERSON>ck explained no other chain fits that position other than Solana.\n\n“And the chain that hosts that killer application stands to benefit immensely from the activity generated by that app. In this note, we model a scenario in which Solana is the first blockchain to host a single application that onboards 100M+ users”, VanEck noted.\n\nNot yet at Ethereum or Polkadot’s level\n\nAs a result of the SCP dominance, Solana’s reign in terms of developer market share over other blockchains like Aptos [APT] has been very wide. The report noted that Solana’s increasing use case and “ground-breaking” technology have been responsible for the hike.\n\nHowever, when compared with the likes of Polkadot [DOT] and Ethereum, the Anatoly Yakovenko-led blockchain lagged behind. One reason Solana trailed here was because of the numerous times the network has experienced outages.\n\nVanEck also mentioned the complexity of the Solana Virtual Machine (SVM) as one of the reasons the blockchain has been behind. The SVM is the system powering Solana’s ability to handle thousands of transactions per second.\n\nThe report explained the reason for the circumstance, highlighting that:\n\n“This is partly due to the need for Solana developers to be familiar with Rust, a language with 2.2M active developers, compared to Ethereum which can draw from the 17.4M JavaScript developers.”\n\nThe future could be brighter for SOL\n\nHowever, the asset management firm said it expected Solana to improve going forward especially as it has had a 100% uptime since March 2023. Meanwhile, Solana’s recovery and glaring potential were not just evident in VanEck’s report.\n\nArtemis, an institutional digital asset data platform, also noticed the project’s growth.\n\nAccording to a 26 October post on X (formerly Twitter), Artemis noted that it was “wowed” by Solana’s resurgence. Some of the metrics used in coming to this conclusion include the TVL, the DEX volume, active addresses, and transactions.\n\nOver the past week L1s have started to see a shift in sentiment.$SOL activity for the month has translated to sharp increased in DEX volume activity as TVL and users have seen an uptick. Is this the start of a new trend? pic.twitter.com/wfFyYlvBVS — Artemis (@artemis__xyz) October 26, 2023\n\nRealistic or not, here’s SOL’s market cap in ETH terms\n\nRegarding its price action, VanEck predicted that it could have an average trading value of $335 by 2030.\n\nBut in a bull phase, the report projected a jump to $3,211 in the same year. And in a torrid market condition, it noted that SOL won’t trade below $10."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMikwFodHRwczovL3d3dy5jcnlwdG9nbG9iZS5jb20vbGF0ZXN0LzIwMjMvMTAvc29sLXdoYXQtaXMtZmlyZWRhbmNlci1hbmQtd2h5LWl0LWlzLXNvLWltcG9ydGFudC1mb3ItdGhlLWZ1dHVyZS1vZi1zb2xhbmEtYmxvY2tjaGFpbi1hbmQtaXRzLWVjb3N5c3RlbS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 29 Oct 2023 07:00:00 GMT", "title": "What is Firedancer and Its Importance for Solana's Future - CryptoGlobe", "content": "Introduction\n\nFiredancer represents a fresh validator client designed for the Solana blockchain. At present, the entire Solana network relies on the original validator client, developed by Solana Labs. While this existing client is effective, it was created in a fast-paced startup setting, leaving room for optimization.\n\nJump Crypto, a division of the Jump Trading Group, which is a global leader in Web3 infrastructure development and investment, is developing this new client for Solana. Leveraging lessons learned from past experiences, the aim is to optimize performance within the given hardware constraints.\n\nCrucially, introducing an additional validator client significantly minimizes the risk of software glitches causing a network-wide outage. This added layer of security is likely to offer peace of mind to Solana Labs’ Co-Founder and CEO <PERSON><PERSON><PERSON>.\n\nWhat Alchemy Says About Firedancer\n\nAccording to a report by Alchemy published back in January 2023, Firedancer is a groundbreaking validator client being developed for the Solana blockchain by Jump Crypto. Unlike Solana’s current single validator client, Firedancer aims to diversify the client ecosystem, enhancing the blockchain’s resilience. Alchemy notes that Firedancer is engineered for heightened performance and scalability, as evidenced by a November 2022 live demo where it processed 1.2 million transactions per second. Although still under development, Alchemy says that early tests suggest Firedancer could bring both redundancy and performance improvements to Solana.\n\nAlchemy states that the client is coded in C and C++, languages renowned for hardware performance and reliability. Initial releases will also be Rust-compatible, aligning with the language commonly used for Solana’s smart contracts. Firedancer is designed to tackle several existing limitations of Solana’s validator clients, such as limited concurrent transaction processing and an absence of sharding support. Alchemy highlights that Firedancer employs enhanced transaction processing techniques and includes sharding capabilities to boost scalability. Additionally, the client incorporates optimized networking and P2P communication protocols, achieved through methods like data compression and batching.\n\nAccording to Alchemy, Firedancer utilizes a modified version of Solana’s existing proof-of-stake consensus protocol, aiming for increased efficiency and reliability. The client’s advantages include improved performance and scalability, enhanced reliability, and the potential for reduced operating costs for node operators due to its efficiency. Its open-source framework allows for community input, offering avenues for further enhancements. However, Alchemy also identifies challenges, such as the client’s newness, which may come with bugs, and a learning curve for infrastructure providers adapting to this new client. Overall, Alchemy suggests that Firedancer’s features and initial test results indicate its transformative potential for the Solana network.\n\nWhat Messari Says About Firedancer\n\nMessari identifies scalability as a crucial issue in the cryptocurrency sector. While modular approaches are gaining traction, Solana has opted for a monolithic strategy. The network that solves this challenge will have a significant edge in achieving widespread adoption.\n\nAccording to Messari, Firedancer is a validator client in development by Jump Crypto, aiming to bolster Solana’s monolithic approach. Its first milestone, “fd_quic,” has shown impressive initial results, achieving a transaction throughput of 1 million TPS. However, Messari notes that further work is needed to realize Firedancer’s full potential.\n\nMessari elaborates that Firedancer aims to rewrite each component of Solana’s architecture. It addresses transaction propagation through its QUIC implementation, fd_quic, and achieves load balancing through hardware and software optimizations. Firedancer’s performance demo surpassed Solana’s recommended 12-core setup, achieving 1.08 million TPS with just four CPU cores.\n\nIf successful, Messari believes Firedancer could bring several benefits, including maturing the DeFi space by reducing latency and enabling Web2 applications on-chain with high throughput. This could benefit financial and consumer apps like social media.\n\nMessari points out that Firedancer could increase Solana’s chain robustness by introducing client diversity. With backup clients like Solana Labs and Jito Labs, the network can mitigate the risk of bugs and maintain performance even if Firedancer encounters issues.\n\nWhile Firedancer presents exciting opportunities, Messari also highlights the risks it faces. These include challenges in execution, timing, and competition. The ability of Jump Crypto to deliver on its roadmap and Solana’s ability to compete against Ethereum are significant factors to consider."}]