[{"id": 2, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vcmVmcmVzaG1pYW1pLmNvbS9pcm9uZm9yZ2UtbmFicy0yLTZtLXRvLWJ1aWxkLXBsYXRmb3JtLXRvLW1ha2Utc29sYW5hLWRldmVsb3BtZW50LWVhc2llci10aGFuLWV2ZXIv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 13 Jun 2023 07:00:00 GMT", "title": "Ironforge nabs $2.6M to build platform to make Solana development easier than ever - Refresh Miami", "content": "By <PERSON> played a protagonistic role in last week’s $1 trillion crypto crash after regulators spooked digital asset investors by charging major exchanges Coinbase and Binance with a smattering of violations.\n\nThe SEC also classified Solana – alongside fellow cryptocurrencies Cardano and Polygon – as unregistered securities. That led Robinhood to stop supporting these currencies and Crypto.com to halt services for US-based institutional traders.\n\nIt was a veritable “bloodbath,” asserted trader <PERSON><PERSON>. “SEC protecting investors again!” he quipped.\n\nBut amid this dire situation, the blockchain continues to grow and a ray of hope came this week, from Miami no less.\n\nServerless Solana development platform Ironforge yesterday announced that it has raised a $2.6 million pre-seed round led by Reciprocal Ventures. Other investors include Miami-based Hash3, 6th Man Ventures, Alchemy, Monoceros Ventures and Portage Ventures. These funds will enable Ironforge to build their platform and expand their team.\n\nThe startup, which also released its product in a private beta yesterday, aims to make it easier for developers to integrate the blockchain powering Solana into existing systems. The idea is that the novel nature of Solana makes developing on its blockchain tricky. Ironforge reduces this complexity by simplifying app integrations and giving developers shortcuts to popular features.\n\n“I’ve had the longstanding conviction that blockchain’s potential won’t be unlocked until web3 devs have access to frameworks and tooling that are similar to what we’ve grown accustomed to in web2,” Ironforge CEO <PERSON><PERSON> said in a statement.\n\n“We created Ironforge to ensure developers need to do as little undifferentiated work as possible, devoting as much of their time and energy towards the thing that makes their product unique.”\n\nCasas founded Ironforge in August 2022. Previously, he founded MetaSoftware Labs and was a senior software engineer at Lavu, Bankrate, and FlexShopper – on top of being on the Node.js core team.\n\nDespite the recent drop in Solana as a cryptocurrency, the ecosystem surrounding is growing. As of the end of May, the chain saw over 12.67 million active addresses, representing 64.1% month-on-month growth. Additionally, Solana has recorded consistent month over month growth in active addresses on the network in 2023.\n\n“The Ironforge platform has the potential to become the global standard for creating applications on Solana,” asserted Craig Burel, partner at Reciprocal Ventures. “By providing a seamless and delightful developer experience, we expect Ironforge will accelerate the utilization of web3 across the broader developer ecosystem.”\n\n“Ironforge lowers the barrier to configure, deploy and maintain production grade programs on Solana, accelerating innovation cycles and enabling developers to spend more time on feature development,” added Carl Vogel from 6th Man Ventures. “We are thrilled to back Ironforge as they make Web3 development as seamless as Web2.”\n\nInterested in checking out Ironforge’s platform? Join the waitlist here.\n\nREAD MORE IN REFRESH MIAMI:\n\nSUPPORT LOCAL NEWS: Refresh Miami is proud to keep our news articles paywall free and accessible to all. If you enjoy our content, please consider supporting us by becoming a paid subscriber or making a donation."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zdWktbGF0ZXN0LXNvbGFuYS1raWxsZXItZm9ybWVyLTE2NDIyMzU1NC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 13 Jun 2023 07:00:00 GMT", "title": "What Is <PERSON><PERSON>? The Latest 'Solana Killer' From Former <PERSON><PERSON> - Yahoo Finance", "content": "In the crypto and blockchain space, <PERSON><PERSON> refers to the Sui network or its native token of the same name. Sui is a Layer 1 blockchain with a mainnet launch on May 3, 2023. The native token launches on a variety of crypto exchanges including Bybit and Kucoin as of that day.\n\nEven before the mainnet launch, however, Sui attracted attention from many in the crypto community. Below, we take a closer look at what <PERSON><PERSON> is and why it is notable in the ever-growing field of blockchains and crypto tokens.\n\nWhat is the Sui Network?\n\nThe Sui blockchain is Layer 1, meaning it provides the underlying infrastructure for a system of validations and transactions in much the same way as the foundational Bitcoin or Ethereum networks. Layer 1 blockchains are the fundamental architecture which support a token—or, in some cases, a broader network of different tokens.\n\nSui sets itself apart from other Layer 1 chains with its focus on instant transaction finality, reduced latency in smart contract deployment, and overall transaction speed. One of the ways it aims to achieve these goals is through Move, a native programming language based on the Rust crypto programming language. Move hopes to make the development of smart contracts easier for developers, thereby expanding access and functionality within the Decentralized Finance (DeFi) industry.\n\nSui’s validators are akin to miners in other blockchain ecosystems. However, its validation system aims to set itself apart from rivals by providing parallel processing of transactions, which Sui developers believe will increase throughput, reduce latency, and enhance scalability. The potential use cases of a parallel-process validation system including gaming, retail payments, and physical points of sale using Sui.\n\nTo make possible this type of transaction processing, Sui scales horizontally, meaning it has no upper limit to meet application demand, and it can maintain low operating costs with each transaction. Sui does not require global consensus on an ordered list of transactions, which is a key bottleneck area for many existing blockchains.\n\nSui's validation system sets itself apart by providing parallel processing of transactions\n\nSui was developed by Mysten Labs, led by several former senior executives and architects for Meta’s now-defunct digital wallet program, Novi. It utilizes a delegated proof-of-stake consensus mechanism, with a new set of token holders picking validators for token staking every 24-hour period. Staked tokens are locked during each of these periods, or epochs, but can be pulled or changed to a new delegated validator at the time the epoch changes.\n\nStory continues\n\nPrior to its mainnet launch, Sui’s developers announced a successful $300 million Series B funding round in support of development.\n\nWhat is the SUI Token?\n\nSui’s native token, known as SUI, will facilitate the network’s proof-of-stake consensus mechanism, on-chain voting for upgrades to the blockchain, and gas fees. It will also allow Sui users to participate in the system’s DeFi activities.\n\nThe May 3 launch of the Sui mainnet is accompanied by a SUI token sale, with each participating crypto exchange offering 225 million tokens. Each user is eligible for up to 10,000 SUI tokens at a price of $0.10 each. For the time being, U.S. residents are not eligible to participate in the sale.\n\nSui tokens will be distributed both to a community reserve for research and development, grants, and subsidies, and to early contributors to the project, app testers, and other stakeholders. There is a hard cap of 10 billion SUI tokens in total. 14% of tokens released at launch are available for investors.\n\nWhy is Sui in the News?\n\nShortly before Sui’s launch in May 2023, the Binance exchange announced it would make SUI available through Launchpool, a system for users to contribute crypto assets to a liquidity pool to earn rewards. This announcement brought increased attention to Sui, including by Tron founder Justin Sun. Sun deposited a substantial volume of $56 million in TrueUSD stablecoins into Binance, likely for the purpose of farming SUI tokens. This transaction was picked up by the platform Whale Alert, designed to track significant crypto asset transfers.\n\nChangpeng Zhao, CEO of Binance, responded to Sun’s move by tweeting that the Launchpool was designed for retail users, not just whales. He warned that Binance would take action against Sun if he attempted a SUI token grab. Sun replied that the deposit aimed to facilitate market-making between exchanges, not to participate in any promotion.\n\nLater, Sun said that he had arranged a full refund of the Binance transfer. Binance said it would reallocate the corresponding 279,000 or so farmed SUI tokens to its TUSD liquidity pool."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zdWktbGF0ZXN0LXNvbGFuYS1raWxsZXItZm9ybWVyLTE2NDIyMzU1NC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 13 Jun 2023 07:00:00 GMT", "title": "What Is <PERSON><PERSON>? The Latest 'Solana Killer' From Former <PERSON><PERSON> - Yahoo Finance", "content": "In the crypto and blockchain space, <PERSON><PERSON> refers to the Sui network or its native token of the same name. Sui is a Layer 1 blockchain with a mainnet launch on May 3, 2023. The native token launches on a variety of crypto exchanges including Bybit and Kucoin as of that day.\n\nEven before the mainnet launch, however, Sui attracted attention from many in the crypto community. Below, we take a closer look at what <PERSON><PERSON> is and why it is notable in the ever-growing field of blockchains and crypto tokens.\n\nWhat is the Sui Network?\n\nThe Sui blockchain is Layer 1, meaning it provides the underlying infrastructure for a system of validations and transactions in much the same way as the foundational Bitcoin or Ethereum networks. Layer 1 blockchains are the fundamental architecture which support a token—or, in some cases, a broader network of different tokens.\n\nSui sets itself apart from other Layer 1 chains with its focus on instant transaction finality, reduced latency in smart contract deployment, and overall transaction speed. One of the ways it aims to achieve these goals is through Move, a native programming language based on the Rust crypto programming language. Move hopes to make the development of smart contracts easier for developers, thereby expanding access and functionality within the Decentralized Finance (DeFi) industry.\n\nSui’s validators are akin to miners in other blockchain ecosystems. However, its validation system aims to set itself apart from rivals by providing parallel processing of transactions, which Sui developers believe will increase throughput, reduce latency, and enhance scalability. The potential use cases of a parallel-process validation system including gaming, retail payments, and physical points of sale using Sui.\n\nTo make possible this type of transaction processing, Sui scales horizontally, meaning it has no upper limit to meet application demand, and it can maintain low operating costs with each transaction. Sui does not require global consensus on an ordered list of transactions, which is a key bottleneck area for many existing blockchains.\n\nSui's validation system sets itself apart by providing parallel processing of transactions\n\nSui was developed by Mysten Labs, led by several former senior executives and architects for Meta’s now-defunct digital wallet program, Novi. It utilizes a delegated proof-of-stake consensus mechanism, with a new set of token holders picking validators for token staking every 24-hour period. Staked tokens are locked during each of these periods, or epochs, but can be pulled or changed to a new delegated validator at the time the epoch changes.\n\nStory continues\n\nPrior to its mainnet launch, Sui’s developers announced a successful $300 million Series B funding round in support of development.\n\nWhat is the SUI Token?\n\nSui’s native token, known as SUI, will facilitate the network’s proof-of-stake consensus mechanism, on-chain voting for upgrades to the blockchain, and gas fees. It will also allow Sui users to participate in the system’s DeFi activities.\n\nThe May 3 launch of the Sui mainnet is accompanied by a SUI token sale, with each participating crypto exchange offering 225 million tokens. Each user is eligible for up to 10,000 SUI tokens at a price of $0.10 each. For the time being, U.S. residents are not eligible to participate in the sale.\n\nSui tokens will be distributed both to a community reserve for research and development, grants, and subsidies, and to early contributors to the project, app testers, and other stakeholders. There is a hard cap of 10 billion SUI tokens in total. 14% of tokens released at launch are available for investors.\n\nWhy is Sui in the News?\n\nShortly before Sui’s launch in May 2023, the Binance exchange announced it would make SUI available through Launchpool, a system for users to contribute crypto assets to a liquidity pool to earn rewards. This announcement brought increased attention to Sui, including by Tron founder Justin Sun. Sun deposited a substantial volume of $56 million in TrueUSD stablecoins into Binance, likely for the purpose of farming SUI tokens. This transaction was picked up by the platform Whale Alert, designed to track significant crypto asset transfers.\n\nChangpeng Zhao, CEO of Binance, responded to Sun’s move by tweeting that the Launchpool was designed for retail users, not just whales. He warned that Binance would take action against Sun if he attempted a SUI token grab. Sun replied that the deposit aimed to facilitate market-making between exchanges, not to participate in any promotion.\n\nLater, Sun said that he had arranged a full refund of the Binance transfer. Binance said it would reallocate the corresponding 279,000 or so farmed SUI tokens to its TUSD liquidity pool."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zdWktbGF0ZXN0LXNvbGFuYS1raWxsZXItZm9ybWVyLTE2NDIyMzU1NC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 13 Jun 2023 07:00:00 GMT", "title": "What Is <PERSON><PERSON>? The Latest 'Solana Killer' From Former <PERSON><PERSON> - Yahoo Finance", "content": "In the crypto and blockchain space, <PERSON><PERSON> refers to the Sui network or its native token of the same name. Sui is a Layer 1 blockchain with a mainnet launch on May 3, 2023. The native token launches on a variety of crypto exchanges including Bybit and Kucoin as of that day.\n\nEven before the mainnet launch, however, Sui attracted attention from many in the crypto community. Below, we take a closer look at what <PERSON><PERSON> is and why it is notable in the ever-growing field of blockchains and crypto tokens.\n\nWhat is the Sui Network?\n\nThe Sui blockchain is Layer 1, meaning it provides the underlying infrastructure for a system of validations and transactions in much the same way as the foundational Bitcoin or Ethereum networks. Layer 1 blockchains are the fundamental architecture which support a token—or, in some cases, a broader network of different tokens.\n\nSui sets itself apart from other Layer 1 chains with its focus on instant transaction finality, reduced latency in smart contract deployment, and overall transaction speed. One of the ways it aims to achieve these goals is through Move, a native programming language based on the Rust crypto programming language. Move hopes to make the development of smart contracts easier for developers, thereby expanding access and functionality within the Decentralized Finance (DeFi) industry.\n\nSui’s validators are akin to miners in other blockchain ecosystems. However, its validation system aims to set itself apart from rivals by providing parallel processing of transactions, which Sui developers believe will increase throughput, reduce latency, and enhance scalability. The potential use cases of a parallel-process validation system including gaming, retail payments, and physical points of sale using Sui.\n\nTo make possible this type of transaction processing, Sui scales horizontally, meaning it has no upper limit to meet application demand, and it can maintain low operating costs with each transaction. Sui does not require global consensus on an ordered list of transactions, which is a key bottleneck area for many existing blockchains.\n\nSui's validation system sets itself apart by providing parallel processing of transactions\n\nSui was developed by Mysten Labs, led by several former senior executives and architects for Meta’s now-defunct digital wallet program, Novi. It utilizes a delegated proof-of-stake consensus mechanism, with a new set of token holders picking validators for token staking every 24-hour period. Staked tokens are locked during each of these periods, or epochs, but can be pulled or changed to a new delegated validator at the time the epoch changes.\n\nStory continues\n\nPrior to its mainnet launch, Sui’s developers announced a successful $300 million Series B funding round in support of development.\n\nWhat is the SUI Token?\n\nSui’s native token, known as SUI, will facilitate the network’s proof-of-stake consensus mechanism, on-chain voting for upgrades to the blockchain, and gas fees. It will also allow Sui users to participate in the system’s DeFi activities.\n\nThe May 3 launch of the Sui mainnet is accompanied by a SUI token sale, with each participating crypto exchange offering 225 million tokens. Each user is eligible for up to 10,000 SUI tokens at a price of $0.10 each. For the time being, U.S. residents are not eligible to participate in the sale.\n\nSui tokens will be distributed both to a community reserve for research and development, grants, and subsidies, and to early contributors to the project, app testers, and other stakeholders. There is a hard cap of 10 billion SUI tokens in total. 14% of tokens released at launch are available for investors.\n\nWhy is Sui in the News?\n\nShortly before Sui’s launch in May 2023, the Binance exchange announced it would make SUI available through Launchpool, a system for users to contribute crypto assets to a liquidity pool to earn rewards. This announcement brought increased attention to Sui, including by Tron founder Justin Sun. Sun deposited a substantial volume of $56 million in TrueUSD stablecoins into Binance, likely for the purpose of farming SUI tokens. This transaction was picked up by the platform Whale Alert, designed to track significant crypto asset transfers.\n\nChangpeng Zhao, CEO of Binance, responded to Sun’s move by tweeting that the Launchpool was designed for retail users, not just whales. He warned that Binance would take action against Sun if he attempted a SUI token grab. Sun replied that the deposit aimed to facilitate market-making between exchanges, not to participate in any promotion.\n\nLater, Sun said that he had arranged a full refund of the Binance transfer. Binance said it would reallocate the corresponding 279,000 or so farmed SUI tokens to its TUSD liquidity pool."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMid2h0dHBzOi8vZm9ydHVuZS5jb20vY3J5cHRvLzIwMjMvMDYvMTMvc29sYW5hLXNhbS1iYW5rbWFuLWZyaWVkLWZ0eC1nYXJ5LWdlbnNsZXItc2VjLXNlY3VyaXR5LWxhd3N1aXRzLWJpbmFuY2UtY29pbmJhc2Uv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 13 Jun 2023 07:00:00 GMT", "title": "<PERSON><PERSON> survived <PERSON> Bankman-Fr<PERSON>. Can it survive <PERSON>? - Fortune", "content": "<PERSON>, the now disgraced founder of bankrupt crypto exchange FTX, loved Solana. Launched in 2020 by cofounders <PERSON><PERSON><PERSON> and <PERSON>, the high-speed blockchain promised to be an “Ethereum killer.”\n\nIt “has a shot at becoming one of the core layers on which most financial and informational transfers happen between applications in a natively Web3 world,<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> once told Fortune.\n\nThe FTX founder, who’s now facing criminal charges for fraud, lauded Solana so much that Alameda Research, his crypto hedge fund, had more than $1 billion in Solana’s native cryptocurrency on its balance sheet right before it and FTX suddenly collapsed in November.\n\nIn the days after <PERSON><PERSON><PERSON>ied’s crypto empire crumbled, the total market capitalization of SOL, Solana’s token, plummeted—from above $13 billion to about $5 billion, according to CoinMarketCap. But it quickly rebounded, and <PERSON><PERSON>, the Solana cofounder, told Fortune in May that the FTX meltdown was “in the rearview mirror” as SOL’s market cap rose back to about $8 billion.\n\nNow, Solana is facing a new threat. On Monday and Tuesday of last week, the Securities and Exchange Commission claimed that SOL and at least 12 other tokens are unregistered securities in lawsuits against Binance, the world’s largest cryptocurrency exchange, and Coinbase, the U.S.’s largest. After the lawsuits were filed, SEC Chair <PERSON> said the crypto industry was populated with “hucksters, fraudsters, scam artists,” and “Ponzi schemes.”\n\nSince then, the price of SOL dropped from about $22 to $15—nearly 30% as of Monday evening. Its market cap, the second largest among tokens deemed securities by the SEC, has dropped from near $8.5 billion to about $6 billion. And Robinhood, a stock-trading app that also lets users buy and sell cryptocurrencies, said it will delist SOL from its platform come the end of June.\n\nSolana weathered the fall of Bankman-Fried, but despite developers’ continued belief in the blockchain, the pall of future litigation hangs over the cryptocurrency.\n\n“It’s a black mark on their forehead,” Arthur Jakoby, a former SEC prosecutor who now cochairs the securities litigation and enforcement team at Herrick, Feinstein LLP, told Fortune. And, he added, it’s “potentially an existential threat” for the Solana ecosystem.\n\nIs SOL a security?\n\nThe core of the SEC’s lawsuits boils down to the following: U.S. securities exchanges need to register with the SEC, and because Binance and Coinbase failed to register, they broke the law.\n\nTo make its case, the agency needs to first prove that both companies effectively operated as securities exchanges, which is why it’s arguing in both lawsuits that SOL and more than 12 other tokens, including Polygon’s MATIC and Cardano’s ADA, are unregistered securities. Both exchanges listed SOL.\n\nThe Solana Foundation, the legal entity that Yakovenko and Gokal launched to promote the cryptocurrency, strenuously disagrees with the SEC. After remaining quiet for days after both suits were filed, it responded to the SEC’s allegations.\n\n“The Solana Foundation strongly believes that SOL is not a security,” a spokesperson said in a statement to Fortune. “We welcome the continued engagement of policymakers as constructive partners on regulation to achieve legal clarity on these issues for the thousands of entrepreneurs across the U.S. building in the digital assets space.”\n\nThe Solana Foundation disagrees with the characterization of SOL as a security. We welcome the continued engagement of policymakers as constructive partners on regulation to achieve legal clarity on these issues for the thousands of entrepreneurs across the U.S. building in the… — Solana Foundation (@SolanaFndn) June 10, 2023\n\nThe SEC’s allegations have not translated to a direct legal threat for Solana since the agency has only sued Binance and Coinbase, not the Solana Foundation, its founders, or its promoters. Legal experts, however, say that the risk for Solana and other singled-out cryptocurrencies is unequivocally heightened.\n\n“I would be very surprised if we didn’t see some actions in the near future against individual companies and/or their founders,” David Rosenfeld, an associate law professor at Northern Illinois University and former SEC prosecutor, wrote in an email to Fortune. “Crypto is a big priority for the SEC right now.”\n\nRosenfeld said that it’s impossible to predict what litigation the agency will bring, but he suspects that the SEC chose the tokens it can best prove are securities. “That doesn’t necessarily mean that the SEC will be pursuing these companies individually, but it does indicate that the agency thinks the case against them would be compelling,” he noted.\n\nSpecifically, he mentioned that Solana Labs, another entity created by Yakovenko and Gokal, filed multiple reports with the agency from 2018 through 2020 to argue that SOL was exempt from registration, which, Rosenfeld told Fortune, “is something that could be seen as a concession that they are securities.”\n\nSolana Labs did not release tokens publicly, as a deluge of companies did during the ICO craze of 2016, but rather sold them to venture capitalists to raise funds. It drummed up almost $340 million from private sales, according to the SEC’s Binance and Coinbase complaints. These private sales of SOL are a potential legal escape clause, as Bloomberg’s Matt Levine described them last week.\n\n“If you look at the underlying technology and what SOL does…I think it’s a utility. It is not a security at all,” a cofounder of a prominent blockchain company, who declined to be named, told Fortune.\n\nHowever, he added: “In terms of how it was promoted, I think the Solana Foundation and FTX treated it just like it was a security.”\n\nYesha Yadav, a law professor at Vanderbilt University who specializes in securities and cryptocurrency regulation, similarly told Fortune that the case against Solana isn’t simple. “There are some definite complexities here,” she said, pointing out, for example, how developers pay with SOL to use the blockchain’s computing power.\n\nMoreover, while the SEC has recently expanded its recent crypto litigation strategy to target exchanges, that doesn’t mean it will abandon enforcement actions against individual tokens, Jakoby, the former SEC prosecutor at Herrick, told Fortune. “It’s hard to imagine,” Yadav added, “that if the SEC is serious about this, they’re not going to potentially bring a case against some of the very prominent coins that are listed.”\n\n‘Continue to build’\n\nWhile legal experts emphasized the potentially existential risks of the lawsuits for the singled-out cryptocurrencies, many Solana community members were comparatively optimistic.\n\nThe day after the SEC sued Coinbase, they flocked to New York City for one of Solana’s Hacker Houses, a frequent event the foundation hosts for developers, artists, and investors.\n\nSolana’s NYC Hacker House ran from June 7 to June 11. Ben Weiss—Fortune\n\n“​​For us and for the devs that we know, none of us are lawyers, so we’re still going to continue to build on cool tech,” the founder of payments app Sphere, which builds on Solana and other blockchains, told Fortune. (He declined to give his full name, citing the regulatory uncertainty.)\n\nAidan Neil, cofounder of Ladder Labs, which has built a Solana-based video game as well as a referral system, had a similarly blasé reaction once the lawsuits became public. “We didn’t budge for a second,” he told Fortune at the Hacker House. “Truthfully, we’ve been through so much that it was like, ‘Oh, great, here we go again.’”\n\nAnd venture capitalists were also undeterred. “I guess we are gratified to see Solana again on the national stage,” an investor in Solana, who declined to give his full name given regulatory concerns, told Fortune. “Solana was an extraordinarily compliant fundraise from the beginning.”\n\nBut despite investor and developer insistence on the lawsuits’ non-impact, potential discord from the litigation has breached the Solana community. Days after the SEC sued Binance and Coinbase, some broached the idea of “forking,” or irrevocably updating, Solana to erase the tokens that FTX and Alameda continue to have on its balance sheet. No plans for a fork are imminent.\n\nDo not fork Solana\n\n\n\n1) The SOL belongs to creditors, not FTX\n\n\n\n2) Shared state is the product of social consensus, which is incredibly valuable and the product of agreement from millions of users. Incredibly powerful. Break it at your own risk\n\n\n\n3) Dangerous precedent — Keone (@keoneHD) June 11, 2023\n\nAt the NYC Hacker House, talk of whether SOL was or wasn’t a security was noticeably absent—until Amira Valliani, the Solana Foundation’s head of policy, broke the silence during a panel titled “WTF is going on in crypto policy.”\n\nNearly 10 minutes into a discussion on the broader crypto regulatory context in Washington, D.C., Valliani, under the purple lights of a popular NYC concert venue, decided to “address the elephant in the room.”\n\n“SOL is not a security,” she said. There was a smattering of applause among the crowd of developers, artists, and investors. Discussion quickly returned to the state of potential crypto legislation in Congress. Once the approximately 30-minute panel ended, Valliani asked the panelists to offer parting words to the audience.\n\n“Keep building,” said one. Said another: “Don’t panic.”"}]