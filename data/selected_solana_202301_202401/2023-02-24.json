[{"id": 0, "url": "https://news.google.com/rss/articles/CBMibGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDIvMjUvc29sYW5hLW5ldHdvcmtzLXRyYW5zYWN0aW9uLXByb2Nlc3NpbmctY3JhdGVycy1hZnRlci1mb3JraW5nLWV2ZW50L9IBcGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS90ZWNoLzIwMjMvMDIvMjUvc29sYW5hLW5ldHdvcmtzLXRyYW5zYWN0aW9uLXByb2Nlc3NpbmctY3JhdGVycy1hZnRlci1mb3JraW5nLWV2ZW50L2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Solana Network Stumbles, On-Chain Trading Slows After ‘Forking’ Incident - CoinDesk", "content": "Within hours a supermajority of validators had switched back to the old software in their attempt to restore Solana’s operations. But it did little to solve the still unknown problem that was weighing down performance. The effort then turned to a more drastic solution: restarting the chain to the point immediately prior to the forking."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9jb25zZW5zdXMtbWFnYXppbmUvMjAyMy8wMi8yNC9zb2xhbmEtZGV2cy1nYXRoZXItZm9yLWEtbW9udGgtbG9uZy11dGFoLXJldHJlYXQtY29pbmRlc2sv0gF1aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2NvbnNlbnN1cy1tYWdhemluZS8yMDIzLzAyLzI0L3NvbGFuYS1kZXZzLWdhdGhlci1mb3ItYS1tb250aC1sb25nLXV0YWgtcmV0cmVhdC1jb2luZGVzay9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Inside the mtnDAO: <PERSON><PERSON>ather for a Month-Long Utah Retreat - CoinDesk", "content": "Calling mtnDAO a “hacker house” is a bit of a misnomer. Sure, some of the attendees are hackers (though most are indeed coders and some such) but <PERSON> and <PERSON> haven’t hosted them in a house since the event’s precursor in 2021. That was when the former college roommates stuffed 25 “kids” into a six-bedroom house owned by a producer of the high-octane dirt bike TV show \"Nitro Circus.\" It was called mtnCompound, and it was a lot."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMilAFodHRwczovL2VuZ2xpc2guamFncmFuLmNvbS9idXNpbmVzcy9jcnlwdG9jdXJyZW5jeS1wcmljZXMtdG9kYXktZmVicnVhcnktMjQtYml0Y29pbi1wb2x5Z29uLWV0aGVyZXVtLXNvbGFuYS1kb2dlY29pbi1zaGliYS1pbnUtbWFya2V0LXJhdGVzLTEwMDY2NTI30gGZAWh0dHBzOi8vZW5nbGlzaC5qYWdyYW4uY29tL2xpdGUvYnVzaW5lc3MvY3J5cHRvY3VycmVuY3ktcHJpY2VzLXRvZGF5LWZlYnJ1YXJ5LTI0LWJpdGNvaW4tcG9seWdvbi1ldGhlcmV1bS1zb2xhbmEtZG9nZWNvaW4tc2hpYmEtaW51LW1hcmtldC1yYXRlcy0xMDA2NjUyNw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "Cryptocurrency Prices Today: Bitcoin Slips Below USD 23,900; Solana, Ethereum Also Decline - Jagran English", "content": "Your browser does not support the audio element.\n\nThe cryptocurrency market traded lower on Friday (24 February) as major cryptocurrencies traded in the red amidst renewed interest rate hike fear among investors following the FOMC meeting minutes. The global crypto market cap fell 1.45% at $1.09 Trillion and the total crypto market volume declined 3.48% over the last 24 hours.\n\nMajor cryptocurrencies including Bitcoin and Ethereum were down 1.94% and 1.06% at USD$23,857.39 and USD$1,648.11 respectively.\n\nThe 24-hour trading volume of Bitcoin is USD$28,679,144,580. It has a circulating supply of 19,300,518 BTC coins and a maximum supply of 21,000,000 BTC coins. The world’s second-largest cryptocurrency Ethereum which commands a market capitalization of USD$201,508,909,009 has a 24-hour trading volume of USD 8,360,787,363.\n\nHow are other cryptocurrencies performing today?\n\nSolana:\n\nToday, Solana was seen trading at USD$23.85, a decrease of 2.07% in the last 24 hours. With a 24-hour trading volume of USD$515,024,251, Solana commands a market capitalization of USD$9,022,141,054, according to CoinMarketCap data.\n\nPolygon:\n\nToday Polygon price was down 3.63% at USD$1.33 per token with a 24-hour trading volume of USD$458,731,008. With a live market cap of USD$11,656,672,040, Polygon’s current CoinMarketCap ranking is 8. It has a maximum supply of 10,000,000,000 MATIC coins.\n\nPrice of popular meme currencies:\n\nDogecoin:\n\nMeme-based digital currency, Dogecoin, slumped 1.51% on Friday. At the time of writing this article, the market value of Dogecoin stands at USD$11,169,632,577. The meme coin has a 24-hour trading volume of USD$322,736,431.\n\nShiba Inu Price:\n\nAt the time of drafting this article, Shiba Inu price is spotted trading at USD$0.000013, down 1.35% in the last 24 hours. The meme coin has a 24-hour trading volume of USD247,023,307 and a live market cap of USD$7,210,972,622.\n\nUS indices close higher on Thursday:\n\nUS indices closed lower Thursday (23 February). The Dow Jones Industrial Average rose 0.33% at 33,153.91 points, the S&P 500 was up 0.53% to 4,012.32 points, and the Nasdaq Composite gained 0.72% to 11,590.40 points."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtdXNlcnMtcHJpb3JpdHktZmVlcy1tZWFuLTIwMzkzNTgyOC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "For Solana Users, 'Priority Fees' Mean Paying Up to Skip the Line - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nDoing business on the Solana blockchain is more expensive than ever. For the network and its users, that might be a good thing.\n\nPriority fees, a key feature among major tech upgrades Solana developers pushed last year to fight crippling congestion issues, are going mainstream among the ecosystem’s wallets and trading protocols, with heavyweights including exchange aggregator Jupiter and trading pool operator Orca joining in.\n\nUsers can pay the additional fees to validators on the Solana network to have their transactions prioritized – so they go through faster. The extra boost can make all the difference in high-traffic situations, like a fleeting crypto-trading arbitrage opportunity or in the scramble for a hot non-fungible token (NFT) mint.\n\nIn one recent epoch (the blockchain’s time period for validators) nearly three-quarters of all nonvote transactions had priority fees attached, per data from validator statistics service Solana Compass. That rate set a new high water mark for priority fee adoption, which has been growing all year.\n\nThe average transaction fee Solana users paid in that epoch (epoch 402) was 0.000014641 SOL, a 67% increase over rates in early July but still only fractions of a penny based on the current market price. That added up to 963 SOL spent on priority fees last epoch, or nearly $24,000 – a rounding error based on the overall value moving across the network.\n\n“Most users can pay 100x current costs and not care since it's still sub-cent,” the pseudonymous 7Layer, who runs Overclock validator service, said in a Twitter DM.\n\nThe priority fees are helping bring order to a network that’s had its share of chaos, most memorably and infamously at the hands of trading bots who in 2021 spammed the network into oblivion. Developers say such things shouldn’t happen under the new tech regime.\n\nTrade-Offs\n\nSolana’s trade-off between price and speed isn’t nearly as poignant as on Ethereum, where congestion in one corner of the network makes doing business expensive for everyone. That’s because Solana’s architecture is built to handle many transactions at once, said Austin Federa, head of strategy for the Solana Foundation.\n\nFor example: If lots of traders are trying to swap BONK tokens for the stablecoin USDC on the same exchange at once, they might start paying priority fees to ensure their trades go through at the prices they want. But even during that high traffic time, the local fee market for minting an NFT elsewhere on Solana won’t get more expensive. On Ethereum it would.\n\nStory continues\n\nJonny Platt, CEO of Solana Compass, told CoinDesk priority fees are changing the economics of running his validators on Solana. He said average block rewards are 20% higher year over year.\n\nHalf of the value of priority fees go to validators such as Platt as reward for lending their compute power to the network; the other half of the priority fee gets burned. Platt said this burn mechanism should drive more value into SOL tokens over time by making the tokens more scarce.\n\n“So many normal everyday users of Solana are excited to see this because there’s recognition that we’ve improved over last year,” Platt said of priority fees.\n\nOutrageous outages\n\nSolana suffered multiple well-documented (and excoriated) network outages in 2022 and 2021, some of which were driven by bots pummeling one corner of the blockchain simultaneously. In one such outage, in September 2021, bots crowded a token sale on exchange Raydium with more transactions than Solana could handle. Solana basically broke, going offline for 17 hours – an eon in 24/7 cryptocurrency markets.\n\nTo address the spam issue, core developers began a year-long effort to change how Solana handles transactions. It would ditch the “first-come-first-served” transaction execution model for “fee markets” that gave favor to those who paid for priority.\n\nSolana transaction fees are the sum of a static base fee plus a dynamic computational fee. With priority fees, users who want faster execution can opt to pay some extra SOL. Bots who previously spammed the network for dominance won’t win on transaction volume alone, validators said.\n\n“It reduces the incentive because priority based on fee matters more instead of just spamming hard to get in front of the queue,” 7Layer said. “There haven't been any spam issues close to what they used to be.”\n\nPriority fees haven’t completely solved Solana’s spam issue, said St. Gnu, another pseudonymous validator. The network is extremely cheap to do business on, extra fees or not. He said Solana’s core developers will need to build out more fee features that make spamming less economical.\n\n“The issue is that if you want to just ‘spray and pray’ a bunch of transactions there's not really a big cost to do that,” he said in a Telegram message."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtdXNlcnMtcHJpb3JpdHktZmVlcy1tZWFuLTIwMzkzNTgyOC5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 24 Feb 2023 08:00:00 GMT", "title": "For Solana Users, 'Priority Fees' Mean Paying Up to Skip the Line - Yahoo Finance", "content": "Join the most important conversation in crypto and web3! Secure your seat today\n\nDoing business on the Solana blockchain is more expensive than ever. For the network and its users, that might be a good thing.\n\nPriority fees, a key feature among major tech upgrades Solana developers pushed last year to fight crippling congestion issues, are going mainstream among the ecosystem’s wallets and trading protocols, with heavyweights including exchange aggregator Jupiter and trading pool operator Orca joining in.\n\nUsers can pay the additional fees to validators on the Solana network to have their transactions prioritized – so they go through faster. The extra boost can make all the difference in high-traffic situations, like a fleeting crypto-trading arbitrage opportunity or in the scramble for a hot non-fungible token (NFT) mint.\n\nIn one recent epoch (the blockchain’s time period for validators) nearly three-quarters of all nonvote transactions had priority fees attached, per data from validator statistics service Solana Compass. That rate set a new high water mark for priority fee adoption, which has been growing all year.\n\nThe average transaction fee Solana users paid in that epoch (epoch 402) was 0.000014641 SOL, a 67% increase over rates in early July but still only fractions of a penny based on the current market price. That added up to 963 SOL spent on priority fees last epoch, or nearly $24,000 – a rounding error based on the overall value moving across the network.\n\n“Most users can pay 100x current costs and not care since it's still sub-cent,” the pseudonymous 7Layer, who runs Overclock validator service, said in a Twitter DM.\n\nThe priority fees are helping bring order to a network that’s had its share of chaos, most memorably and infamously at the hands of trading bots who in 2021 spammed the network into oblivion. Developers say such things shouldn’t happen under the new tech regime.\n\nTrade-Offs\n\nSolana’s trade-off between price and speed isn’t nearly as poignant as on Ethereum, where congestion in one corner of the network makes doing business expensive for everyone. That’s because Solana’s architecture is built to handle many transactions at once, said Austin Federa, head of strategy for the Solana Foundation.\n\nFor example: If lots of traders are trying to swap BONK tokens for the stablecoin USDC on the same exchange at once, they might start paying priority fees to ensure their trades go through at the prices they want. But even during that high traffic time, the local fee market for minting an NFT elsewhere on Solana won’t get more expensive. On Ethereum it would.\n\nStory continues\n\nJonny Platt, CEO of Solana Compass, told CoinDesk priority fees are changing the economics of running his validators on Solana. He said average block rewards are 20% higher year over year.\n\nHalf of the value of priority fees go to validators such as Platt as reward for lending their compute power to the network; the other half of the priority fee gets burned. Platt said this burn mechanism should drive more value into SOL tokens over time by making the tokens more scarce.\n\n“So many normal everyday users of Solana are excited to see this because there’s recognition that we’ve improved over last year,” Platt said of priority fees.\n\nOutrageous outages\n\nSolana suffered multiple well-documented (and excoriated) network outages in 2022 and 2021, some of which were driven by bots pummeling one corner of the blockchain simultaneously. In one such outage, in September 2021, bots crowded a token sale on exchange Raydium with more transactions than Solana could handle. Solana basically broke, going offline for 17 hours – an eon in 24/7 cryptocurrency markets.\n\nTo address the spam issue, core developers began a year-long effort to change how Solana handles transactions. It would ditch the “first-come-first-served” transaction execution model for “fee markets” that gave favor to those who paid for priority.\n\nSolana transaction fees are the sum of a static base fee plus a dynamic computational fee. With priority fees, users who want faster execution can opt to pay some extra SOL. Bots who previously spammed the network for dominance won’t win on transaction volume alone, validators said.\n\n“It reduces the incentive because priority based on fee matters more instead of just spamming hard to get in front of the queue,” 7Layer said. “There haven't been any spam issues close to what they used to be.”\n\nPriority fees haven’t completely solved Solana’s spam issue, said St. Gnu, another pseudonymous validator. The network is extremely cheap to do business on, extra fees or not. He said Solana’s core developers will need to build out more fee features that make spamming less economical.\n\n“The issue is that if you want to just ‘spray and pray’ a bunch of transactions there's not really a big cost to do that,” he said in a Telegram message."}]