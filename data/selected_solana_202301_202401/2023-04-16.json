[{"id": 6, "url": "https://news.google.com/rss/articles/CBMiJ2h0dHBzOi8vY29pbjk4Lm5ldC90aGUtY29pbjk4LWVjb3N5c3RlbdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 16 Apr 2023 07:00:00 GMT", "title": "The Coin98 ecosystem - Constantly evolving and expanding! - Coin98 Insights", "content": "Coin98 Finance is a technology company that helps people gain access to the Blockchain and Web3 markets in the simplest way possible. Coin98 Finance consists of three divisions: Coin98 Ventures, Coin98 Labs, and Coin98 Network.\n\nvuongdt Published Apr 16 2023 Updated Dec 18 2023 15 min read"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vd3d3LmJsb2NrY2hhaW5nYW1lci5iaXovbmV3cy8yMzc0NC90aGUtYm9ybmxlc3MtcGFydC1vZi14Y3Mtd2ViMy10b3VybmFtZW50L9IBWGh0dHBzOi8vd3d3LmJsb2NrY2hhaW5nYW1lci5iaXovYW1wL25ld3MvMjM3NDQvdGhlLWJvcm5sZXNzLXBhcnQtb2YteGNzLXdlYjMtdG91cm5hbWVudC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 16 Apr 2023 07:00:00 GMT", "title": "The Bornless part of XCS web3 tournament - Blockchain Gamer", "content": "Horror FPS The Bornless, by UK based Cathedral Studios, has joined XBorg’s Xtreme Championship Series.\n\nXCS is the first multi-game web3 esports league. Created by XBorg and sponsored by Brave Software, SwissBorg and Community Gaming, the tournament will run for four months, featuring five games and players will compete for a prize pool of $100,000. Other games included are EV.io, Cross the Ages, Gods Unchained and <PERSON><PERSON>.\n\n<PERSON>, head of marketing at XBorg commented, “Showcasing the best games and players web3 has to offer was central in our mission for the Xtreme Championship Series, thus The Bornless was an easy pick.”\n\nDespite still being in its early alpha stage, The Bornless has over 45 esports and guild partnerships and more than 1,000 active players. While currently available on PC, the game is planned to be released on other platforms in the near future, and will be released on Ultra Games and Epic Games, 25th of April 2023.\n\n“We strongly believe in the power of esports and shared experiences to bring people together. We’re humbled to share this early glimpse of the game with our community, and we can’t wait to see how it’ll shape the future of Bornless.” said <PERSON>, co-founder and CEO of Cathedral Studios.\n\nFor more details, check out the event here."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vY3J5cHRvbW9kZS5jb20vYWx0Y29pbnMvY2FyZGFuby1hZGEtYW5kLXNvbGFuYS1zb2wtYXJlLXR3by1jcnlwdG9zLWNvbWluZy10by1hdm9yYWstYWktdHJhZGUtYXNzaXN0YW50L9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 16 Apr 2023 07:00:00 GMT", "title": "Card<PERSON> (ADA) and <PERSON><PERSON> (SOL) are two cryptos coming to Avorak AI trade assistant - Crypto Mode", "content": "nb The following content is a paid release. It is provided for informational purposes only and should not be interpreted as financial advice. The statements, views and opinions expressed in this column are solely those of the content provider and does not reflect the views or opinions of CryptoMode.\n\nMore often, cryptocurrencies demonstrate a solid continuous existence through beneficial projects and developments, as dormancy is regarded as stagnation and bad for the coin. As Avorak AI makes waves in the crypto space, many projects are attracted to its AI trading solutions. There’s excitement as Solana and Cardano onboard the Avorak AI trade assistant. Let’s explore why Avorak AI is trending and the benefits of Avorak Trade AI.\n\nWhat Is Avorak Trade Assistant?\n\nAvorak Trade assistant is an AI tool that automates cryptocurrency trading on behalf of its users through their preferred input parameters. Trading bots use AI algorithms to analyze market data, identify trends and patterns, and execute sell or buy decisions per the rules and parameters set by the user.\n\nTrade assistants make trading more efficient and profitable by monitoring markets 24/7 and reacting faster to market movements at certain price points or when specific market indicators are triggered. Avorak trade assistants are easy for all users, whether novice or experienced. The critical feature of trade bots in the cryptocurrency market is eliminating emotional trading and biases like greed and fear that sometimes cloud objective judgment. In addition, humans cannot monitor markets 24/7 without fatigue and need rest, and through trade bots, they can get updates when they are not actively monitoring the market.\n\nAvorak AI is running a record-breaking ICO currently in phase four, featuring one AVRK token at $0.180, a rise of 200% from the initial value. ICO holders benefit from discounted token price, 7% bonus yield, and priority to staking pools when Avorak lists on the Azbit exchange after launch. A significant feature is the expected value increase by 350% on launch, which will see one AVRK going at $0.27.\n\nAvorak AI bundled tools also feature text generation tools that aim to produce original, nonrepetitive, reader-friendly content. Users can key in the style and form of the content output they prefer, and the Avorak Write will deliver any output specified. It came as a solution to the shortfalls of the existing content and image generators in the market.\n\nSolana (SOL)\n\nSolana (SOL) is a high-performance blockchain platform striving for fast and scalable transactions with low fees. Solana has been gaining traction due to its innovative technology, which combines PoH and PoS consensus mechanisms, making it an attractive platform for developers to build dApps and NFTs. The Solana price prediction for the near future is positive, with many experts and analysts forecasting further growth in the coming months. e Avorak trade assistants can monitor multiple cryptocurrencies and trading pairs, allowing Solana traders to diversify their portfolios and reduce risk. Solana has been gaining popularity due to its fast and scalable blockchain and has gained support from major players in the crypto space.\n\nCardano (ADA)\n\nCardano (ADA) is a proof-of-stake blockchain platform aiming for a secure, sustainable, and scalable ecosystem. Its multi-layer architecture, which separates the settlement and computation layers, facilitates flexibility and security.\n\nThe latest Cardano news of integrating AI signifies a gigantic step in its goal of providing blockchain solutions with a strong focus on sustainability and scalability. Its growing adoption and partnerships enhance its potential as a blockchain platform for the future. Avorak Trade bots allow Cardano investors to backtest their strategies using historical market data, helping to optimize their trading strategies and improve performance.\n\nConclusion\n\nWith the emergence of innovative technologies like Avorak AI, the crypto space is witnessing an exciting transformation as more projects join the platform. This is a positive sign for the adoption and growth of AI crypto. With Solana and Cardano onboard, the Avorak AI trade assistant is proving to be a promising tool for stakeholders in the cryptocurrency market.\n\nFor more information on Avorak AI and its ICO:\n\nWebsite: https://avorak.ai\n\nBuy AVRK: https://invest.avorak.ai/register"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMib2h0dHBzOi8vY3J5cHRvbW9kZS5jb20vYWx0Y29pbnMvY2FyZGFuby1hZGEtYW5kLXNvbGFuYS1zb2wtYXJlLXR3by1jcnlwdG9zLWNvbWluZy10by1hdm9yYWstYWktdHJhZGUtYXNzaXN0YW50L9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 16 Apr 2023 07:00:00 GMT", "title": "Card<PERSON> (ADA) and <PERSON><PERSON> (SOL) are two cryptos coming to Avorak AI trade assistant - Crypto Mode", "content": "nb The following content is a paid release. It is provided for informational purposes only and should not be interpreted as financial advice. The statements, views and opinions expressed in this column are solely those of the content provider and does not reflect the views or opinions of CryptoMode.\n\nMore often, cryptocurrencies demonstrate a solid continuous existence through beneficial projects and developments, as dormancy is regarded as stagnation and bad for the coin. As Avorak AI makes waves in the crypto space, many projects are attracted to its AI trading solutions. There’s excitement as Solana and Cardano onboard the Avorak AI trade assistant. Let’s explore why Avorak AI is trending and the benefits of Avorak Trade AI.\n\nWhat Is Avorak Trade Assistant?\n\nAvorak Trade assistant is an AI tool that automates cryptocurrency trading on behalf of its users through their preferred input parameters. Trading bots use AI algorithms to analyze market data, identify trends and patterns, and execute sell or buy decisions per the rules and parameters set by the user.\n\nTrade assistants make trading more efficient and profitable by monitoring markets 24/7 and reacting faster to market movements at certain price points or when specific market indicators are triggered. Avorak trade assistants are easy for all users, whether novice or experienced. The critical feature of trade bots in the cryptocurrency market is eliminating emotional trading and biases like greed and fear that sometimes cloud objective judgment. In addition, humans cannot monitor markets 24/7 without fatigue and need rest, and through trade bots, they can get updates when they are not actively monitoring the market.\n\nAvorak AI is running a record-breaking ICO currently in phase four, featuring one AVRK token at $0.180, a rise of 200% from the initial value. ICO holders benefit from discounted token price, 7% bonus yield, and priority to staking pools when Avorak lists on the Azbit exchange after launch. A significant feature is the expected value increase by 350% on launch, which will see one AVRK going at $0.27.\n\nAvorak AI bundled tools also feature text generation tools that aim to produce original, nonrepetitive, reader-friendly content. Users can key in the style and form of the content output they prefer, and the Avorak Write will deliver any output specified. It came as a solution to the shortfalls of the existing content and image generators in the market.\n\nSolana (SOL)\n\nSolana (SOL) is a high-performance blockchain platform striving for fast and scalable transactions with low fees. Solana has been gaining traction due to its innovative technology, which combines PoH and PoS consensus mechanisms, making it an attractive platform for developers to build dApps and NFTs. The Solana price prediction for the near future is positive, with many experts and analysts forecasting further growth in the coming months. e Avorak trade assistants can monitor multiple cryptocurrencies and trading pairs, allowing Solana traders to diversify their portfolios and reduce risk. Solana has been gaining popularity due to its fast and scalable blockchain and has gained support from major players in the crypto space.\n\nCardano (ADA)\n\nCardano (ADA) is a proof-of-stake blockchain platform aiming for a secure, sustainable, and scalable ecosystem. Its multi-layer architecture, which separates the settlement and computation layers, facilitates flexibility and security.\n\nThe latest Cardano news of integrating AI signifies a gigantic step in its goal of providing blockchain solutions with a strong focus on sustainability and scalability. Its growing adoption and partnerships enhance its potential as a blockchain platform for the future. Avorak Trade bots allow Cardano investors to backtest their strategies using historical market data, helping to optimize their trading strategies and improve performance.\n\nConclusion\n\nWith the emergence of innovative technologies like Avorak AI, the crypto space is witnessing an exciting transformation as more projects join the platform. This is a positive sign for the adoption and growth of AI crypto. With Solana and Cardano onboard, the Avorak AI trade assistant is proving to be a promising tool for stakeholders in the cryptocurrency market.\n\nFor more information on Avorak AI and its ICO:\n\nWebsite: https://avorak.ai\n\nBuy AVRK: https://invest.avorak.ai/register"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZW4uY3J5cHRvbm9taXN0LmNoLzIwMjMvMDQvMTYvaG93LXVwbG9hZC1jcnlwdG8tcHJpY2VzLWdvb2dsZS1zaGVldHMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sun, 16 Apr 2023 07:00:00 GMT", "title": "How to upload crypto prices to Google Sheets - The Cryptonomist", "content": "Google Sheets is Google’s alternative to Microsoft’s Excel, and among other things it allows importing crypto prices.\n\nIndeed, Google Sheets is used online, as SaaS (Software-as-a-Service), so it is by definition connected to the network. Excel, on the other hand, is stand-alone software that runs on the user’s machine even offline.\n\nBeing online, Google Sheets can connect to other data sources, even updated in real time.\n\nHow to import crypto prices into Google Sheets\n\nAlthough it is not straightforward, data from external sources can be imported into Google Sheets, and among the data that can be imported in this way are up-to-date prices from crypto markets.\n\nIt is necessary to use a workaround that takes advantage of the IMPORTXML feature, and there is also a video tutorial that shows how to proceed.\n\nThis tutorial is short, but not complete, so much so that there is also another one that is more complete but also longer.\n\nThe data source\n\nThe source of the data is the CoinMarketCap (CMC) website, from which XML data can be extracted that can be imported into Google Sheets.\n\nIn order to proceed, it is necessary to open one by one the individual CMC sheets of all the cryptocurrencies whose prices you want to import.\n\nOf these you need to copy the URL, which for Bitcoin, for example, is https://coinmarketcap.com/en/currencies/bitcoin/.\n\nUsing the URL of the CMC card, it is possible to import the data contained in the card into any Google Sheets document, using the IMPORTXML function.\n\nThis function has two arguments, separated by commas, that allow you to specify the URL of the data source, and the data you intend to import.\n\nTo import the updated price, for example, you would use the argument “//div[contains(@class,’priceValue’)]”.\n\nIn other words, to display in a specific cell within a Google Sheets document the updated price of Bitcoin you have to write the following code in that cell:\n\n=IMPORTXML(“https://coinmarketcap.com/en/currencies/bitcoin/”, “//div[contains(@class,’priceValue’)]”)\n\nThis is a workaround because the data source is actually an HTML page, and not an XML file as the IMPORTXML function would like. However, that function can also read an HTML document.\n\nIndeed, the argument that is used to retrieve the updated price is nothing more than telling the IMPORTXML function to fetch the contents of a specific DIV element of the page’s HTML code, and in particular the one marked with the priceValue class.\n\nIn this way you could theoretically tell the IMPORTXML function to display in the cell in which it is inserted, the contents of any HTML element contained in the page that is found online at the indicated url.\n\nThis workaround actually allows any content on that page to be imported, as long as the container can be uniquely identified. HTML elements called DIVs are containers, and to view the HTML code of any web page you typically only need to type ctrl+u.\n\nIn the specific case of CMC, HTML code is created dynamically from JavaScript code, so with ctrl+u you see the JavaScript code, and not the HTML. However, usually by right-clicking over any element on the page you can choose the “Inspect Element” option to see the HTML code for that element in the sidebar.\n\nHow to automatically upload updated crypto data to Google Sheets\n\nAfter using this workaround, every time the document in Google Sheets is opened, the updated data will be imported.\n\nHowever, by clicking on File/Settings, and then selecting the Calculation tab you can set an update timer of one minute or one hour to have the data update even without having to close and reopen the file.\n\nIn this way it is possible to have data updated every minute regarding all the prices of all the cryptocurrencies on CMC, assuming you include the specific url of its tab for each one.\n\nThe problem is that sometimes CMC changes the HTML code of its pages, and should it do so, the workaround would stop working and the data would no longer show up.\n\nSince it is quite likely that this may happen sooner or later, in the event the data that was imported in this way disappears on the Google Sheets document, it will be necessary to update all the IMPORTXML functions used.\n\nIn particular there may be a need to update the url, or to update the class name of the DIV whose content you want to retrieve.\n\nAlternative data sources\n\nThis technique also works with any other updated HTML file that can be found online, as long as it is public.\n\nThe tricky thing is to go and find a way to be able to uniquely tell the IMPORTXML function which HTML element to go and retrieve, but thanks to the “Inspect Element” option, it is not that difficult to go looking for a name, ID, or class that allows you to accurately identify an element.\n\nHowever, there remains the problem of whether the HTML code has been changed, because it is not all that uncommon for HTML code to be updated, or even distorted."}]