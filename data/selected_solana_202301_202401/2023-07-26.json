[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiemh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9tYXJrZXRzLzIwMjMvMDcvMjYvY3J5cHRvLWxlbmRlci1jcmVkaXgtYnJpbmdzLW9wZW5zLXByaXZhdGUtY3JlZGl0LXBvb2wtb24tc29sYW5hLXdpdGgtMTEteWllbGQv0gF-aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL21hcmtldHMvMjAyMy8wNy8yNi9jcnlwdG8tbGVuZGVyLWNyZWRpeC1icmluZ3Mtb3BlbnMtcHJpdmF0ZS1jcmVkaXQtcG9vbC1vbi1zb2xhbmEtd2l0aC0xMS15aWVsZC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 26 Jul 2023 07:00:00 GMT", "title": "Crypto Lender Credix Brings Additional Private Credit Pool on Solana With 11% Yield - CoinDesk", "content": "The new offering comes as DeFi and traditional finance are becoming increasingly intertwined, with crypto native firms working with legacy financial institutions to bring old-school instruments such as private credit and bonds – often referred to as real-world assets (RWAs) – to the blockchain. The tokenization of RWAs could disrupt finance by making capital markets more efficient, transparent and accessible, and could be a $4 trillion-$16 trillion market by 2030, a Boston Consulting Group report said."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiRWh0dHBzOi8vdS50b2RheS9jYW4tc29sYW5hLXNvbC1iZWNvbWUtYmVzdC1wZXJmb3JtaW5nLWFzc2V0LXRoaXMteWVhctIBSWh0dHBzOi8vdS50b2RheS9jYW4tc29sYW5hLXNvbC1iZWNvbWUtYmVzdC1wZXJmb3JtaW5nLWFzc2V0LXRoaXMteWVhcj9hbXA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 26 Jul 2023 07:00:00 GMT", "title": "<PERSON> <PERSON> (SOL) Become Best Performing Asset This Year? - U.Today", "content": "Disclaimer: The opinions expressed by our writers are their own and do not represent the views of U.Today. The financial and market information provided on U.Today is intended for informational purposes only. U.Today is not liable for any financial losses incurred while trading cryptocurrencies. Conduct your own research by contacting financial experts before making any investment decisions. We believe that all content is accurate as of the date of publication, but certain offers mentioned may no longer be available.\n\nSolana (SOL) has emerged as the best-performing coin of the year, showing remarkable resilience amid market volatility. The altcoin has outperformed several of its contemporaries, gaining an impressive 65% in value over the past few weeks, despite losing around 20% from its recent local peak.\n\nAdvertisement\n\nThe bullish forecast for Solana comes from a detailed comparison with Ethereum's early days. Analysts have noted striking similarities between Ethereum's initial trajectory and the current path of Solana, leading to predictions that Solana might follow a comparable growth pattern. The comparisons have led to the belief that, like Ethereum, Solana could become a prominent player in the blockchain ecosystem.\n\nA statement from a renowned analyst read, \"So long as you don't mid-curve it, you can see the inevitable from a few years away.\" The comment highlights the importance of analyzing long-term trends and not getting distracted by temporary fluctuations. Despite Solana's recent 20% drop, the overall momentum and growth narrative remain undeterred.\n\nSolana's rise as the darling of this cycle can be attributed to its robust technological infrastructure and growing ecosystem. It is renowned for its high throughput and low transaction costs, making it a highly scalable blockchain platform for decentralized applications and crypto projects.\n\nOne significant trend observed during this rally is the substantial increase in cheers and support for Solana in the crypto community. As more investors turn their attention toward this rising star, the network effect could play a crucial role in sustaining Solana's growth.\n\nHowever, as always with cryptocurrencies, investors are urged to approach them with caution. While historical patterns and technological advantages provide optimism, market dynamics and crypto volatility are factors that could affect future performance."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vZmludGVjaG1hZ2F6aW5lLmNvbS9hcnRpY2xlcy9maW50ZWNoLW1hZ2F6aW5lcy10b3AtMTAtY3J5cHRvY3VycmVuY2llc9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 26 Jul 2023 07:00:00 GMT", "title": "Top 10 Cryptocurrencies - FinTech Magazine - FinTech Magazine", "content": "Cryptocurrencies have long been touted as the digital payment method of choice for the future, as we move into a world of metaverse and AI. With a vast array of cryptocurrencies now on the market, it’s hard to sift through what different cryptocurrencies have to offer.\n\n\n\nFind out more from FinTech Magazine’s Top 10 cryptocurrencies on the market today.\n\nPeer-to-peer cryptocurrency and open-source software project Litecoin is among the earliest alternative coins starting in 2011. Inspired by Bitcoin, Litecoin’s main blockchain is slightly modified from Bitcoin’s codebase to offer lower transaction fees, faster confirmation of a transaction, and faster mining difficulty retargeting. In addition, Litecoin uses Scrypt, an alternative proof-of-work algorithm from that used by Bitcoin (SHA-256 algorithm). The cryptocurrency has added optional privacy features in recent times as a soft fork, through the MimbleWimble extension block upgrade.\n\nSolana is a crypto platform that operates a new proof-of-stake model, as opposed to traditional proof-of-work models, providing smart contract functionality through its native cryptocurrency SOL. First opening in March 2020, Solana’s blockchain was designed for supporting decentralised apps and smart contracts. In 2021, the company sold US$314m of its native SOL to funding groups led by Polychain Capital and <PERSON><PERSON><PERSON>. While crypto markets have fluctuated following FTX’s bankruptcy, Solana’s last reported market valuation stood at US$7bn.\n\nCreated by software engineers <PERSON> and <PERSON>, <PERSON>ecoin is an altcoin traded against fiat and other cryptocurrencies. Initially founded as a joke, or the world’s first “meme coin”, Dogecoin has developed into a legitimate investment prospect. The altcoin saw its value boom in 2021, which rose over 800% in the space of 24 hours – and has been subject to turbulent market conditions surrounding crypto ever since. This even includes a price increase when Twitter owner Elon Musk temporarily changed the Twitter app logo to a Doge logo.\n\nPublic blockchain platform Cardano operates an open-source, decentralised blockchain platform that facilitates peer-to-peer transactions with its internal cryptocurrency ADA. Overseen by the Cardano Foundation in Switzerland, the platform’s development was led by the co-founder of Ethereum Charles Hoskinson. Much like Solana, Cardano employs the use of a proof-of-stake blockchain, a more sustainable and environmentally friendly option than proof-of-work models. Launched in 2017, Cardano reached a record-high market capitalisation of US$77bn in 2021, the fourth highest of any blockchain platform at the time.\n\nRipple operates a cryptocurrency exchange, remittance network and real-time gross settlement system that employs its native cryptocurrency XRP. With a mission to build crypto solutions for an economically borderless world, Ripple provides its customers with solutions to source crypto and facilitate instant payments, including for XRP. Although Ripple’s XRP has faced scrutiny after the US Securities and Exchange Commission (SEC) launched a class action against the crypto platform provider for falling foul of US Howey requirements, Ripple’s currency remains robust in many global markets.\n\nUSD Coin (USDC) is a stablecoin attached to the US dollar. Founded by Circle, USDC is managed by a consortium called Centre, and includes Coinbase members as well as from Bitmain, a Bitcoin mining company with investment in Circle. Unlike central digital banking currencies (CBDCs), USDC is a private crypto stablecoin, despite its affiliation with centralised US currency (USD). Available as an Ethereum ERC-20 token, USDC can be bought, traded and held on blockchains including Solana, Stellar, Polygon and Avalanche."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzMwNjcwL25vLW1vcmUtb3V0YWdlcy1zb2xhbmEtYmxvY2tjaGFpbi1yZWNvcmRzLWltcHJvdmVkLXBlcmZvcm1hbmNlLWluLWZpcnN0LWhhbGYtb2YtMjAyMy_SAXxodHRwOi8vYW1wLmNvaW5jb2RleC5jb20vYXJ0aWNsZS8zMDY3MC9uby1tb3JlLW91dGFnZXMtc29sYW5hLWJsb2NrY2hhaW4tcmVjb3Jkcy1pbXByb3ZlZC1wZXJmb3JtYW5jZS1pbi1maXJzdC1oYWxmLW9mLTIwMjMv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 26 Jul 2023 07:00:00 GMT", "title": "No More Outages? Solana Blockchain Records Improved Performance in First Half of 2023 - CoinCodex", "content": "Key takeaways:\n\nSolana suffered 8 outages in 2022 caused by instability issues, hurting confidence in the blockchain's reliability.\n\nIn the first half of 2023, Solana only had one outage in February and zero outages in Q2, showing major improvements in uptime nearing 100%.\n\nTransaction speeds continue to increase on the Solana blockchain in 2023, with average block times getting faster, according to reports, pointing to even faster transaction processing.\n\nFor anyone who wrote off Sol<PERSON> after the unfortunate events of last year, it may be time to give this high-speed blockchain another look. With developers rapidly building new dApps and integrations, we could soon see Solana's revived technical foundations lead to the real user and transaction growth. Developers have likely identified and resolved many of the causes behind past network disruptions.\n\nHowever, skeptics will want to see sustained stability from Solana over a longer period before fully accepting that the network's problems are in the past. Outages often have complex root causes, so more work is likely needed.\n\nNetwork performance upgrades\n\nUpgrading critical infrastructure: Things like improved RPC nodes, expanded validator monitoring tools, and redundancies to prevent single points of failure.\n\nThings like improved RPC nodes, expanded validator monitoring tools, and redundancies to prevent single points of failure. Fixing bugs - Squashing bugs in the core Solana software that caused things like memory leaks.\n\n- Squashing bugs in the core Solana software that caused things like memory leaks. Better incentivization of validators: Adjusting validator rewards and penalties to encourage better behavior.\n\nAdjusting validator rewards and penalties to encourage better behavior. Growing the validator community: Expanding the pool of validators increased the decentralization and reliability of the network.\n\nThe Solana network by the numbers. Image source: Solana Foundation\n\nSolana still has work to do\n\nWhile these improvements are certainly good news for Solana, the network still has work to do to match the consistency and reliability of some other major layer 1s. Ethereum, for example, runs smoothly even with 4-5x more daily active users and transactions than Solana currently handles.\n\nTo catch up, Solana Labs says priorities include continued infrastructure upgrades, establishing decentralized governance, and further decentralizing the validator network. The company also plans to implement optimizations like sharding to boost throughput and introduce features like state proofs to reduce storage costs.\n\nAdditionally, some critics argue that Solana's focus on speed and low fees comes at the expense of true decentralization. Solana uses a centralized development model led by Solana Labs. It also tends to favor large validator nodes, with the top 7 validators controlling 33% of voting power at times. Addressing these concerns could strengthen Solana against censorship risks.\n\nThe road ahead\n\nIf Solana can maintain and build upon the momentum of the first half of 2023, it will go a long way toward shedding lingering doubts about the network's reliability. While Solana still has a lot of ground to cover to be as battle-tested as some competitors, its base layer performance and capabilities continue to improve.\n\nWith developer interest remaining high thanks to fast speeds and low costs, Solana looks poised to be a major player in the future of Web3. But delivering a stable, outage-free experience even as usage grows will ultimately determine if Solana can fulfill its lofty ambitions.\n\nIn the meantime, Solana's SOL token faces regulatory challenges that could dampen enthusiasm. The SEC has deemed SOL a security, causing some platforms to delist it. A stable network could be news leading the SOL price to rise. Solana's price forecast predicts that the price could rise 10.5% in 30 days to reach $26.02.\n\nIf you want to learn more about where SOL could be headed in the future, check our SOL price prediction for 2040 and 2050."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiX2h0dHBzOi8vZGVjcnlwdC5jby8xNDk4NzEvYWxjaGVteS1iYXR0bGUtYW5raG9zLXJwZy1tYXNoZXMtdXAtZGlzY29yZC1nZW5lcmF0aXZlLWFpLXNvbGFuYS1uZnRz0gFlaHR0cHM6Ly9kZWNyeXB0LmNvLzE0OTg3MS9hbGNoZW15LWJhdHRsZS1hbmtob3MtcnBnLW1hc2hlcy11cC1kaXNjb3JkLWdlbmVyYXRpdmUtYWktc29sYW5hLW5mdHM_YW1wPTE?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 26 Jul 2023 07:00:00 GMT", "title": "'Alchemy: Battle for Ankhos' RPG Mashes Up Discord, Generative AI, and Solana NFTs - Decrypt", "content": "Decrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nHow's this for a unique concoction? A video game that is playable within a Discord chat server, is being built using generative AI tools, and even taps into Solana NFTs and tokens for player-owned assets and rewards.\n\nWhat it adds up to is Alchemy: Battle for Ankhos, a game that's being built using both AI and blockchain to deliver engaging, persistent experiences within the familiar, interactive chat window of a Discord server. Decrypt saw the game in action at the recent Solana PlayGG event in San Diego.\n\n“Discord has a few advantages for building social games,” Creative Director <PERSON> told Decrypt. “One of those is that all of the game activities happen in this interactive environment that people are already very comfortable with,” he added, further explaining that the platform makes it easy for players to spectate and challenge each other.\n\nAD\n\nAD\n\nAlchemy: Battle for Ankhos is a text and turn-based role-playing game (RPG), which holds its battles in a fantasy world via the game's official Discord server. It also uses the Solana blockchain for its NFT-based items, including in-game characters.\n\n\"Nearly every in-game asset earned or purchased is optionally mintable as an NFT on Solana,\" <PERSON><PERSON> said. \"Any player can mint their assets from in-game menus with no blockchain knowledge or experience required,\" he said, adding that a launch date for the assets has not yet been determined.\n\nImage: Alchemy: Battle for Ankhos\n\nThe inaugural NFT collection for the game will be called the Alchemy: Gods collection. Players can collect one of 5,000 unique Alchemy Gods, and accrue the soon-to-be-introduced Solana-based token SPIRIT through a new faction-based gameplay mode.\n\nAlchemy: Battle for Ankhos was among five winners of the Solana Foundation’s NFT Showdown in June. At SolanaPlayGG, Finden unveiled its Land Shard NFTs.\n\nSolana was already a popular destination for NFTs and digital collectibles. Now, the Solana Foundation and Solana Labs aim to make it the destination for gaming, thanks to initiatives like last week’s PlayGG event in San Diego and the upcoming GameShift toolkit for developers.\n\nAD\n\nAD\n\nDiscord gaming\n\nAlchemy: Battle for Ankhos players take on the role of a Descendant as a mage, squire, cleric, or rogue, and can play against an AI-controlled opponent or join player-vs-player (PvP) battles against other users.\n\nGaming via Discord servers is not a totally new concept. Other text-based games on the platform include IdleRPG, PokeMeow, and the classic game of chess. However, Alchemy: Battle for Ankhos opts for a distinctive hybrid approach, looping in crypto assets while also tapping into generative AI smarts.\n\nDiscord offers several tools for developers, Finden explained, including APIs that make creating and deploying games quickly very easy. An application programming interface (API) is a way for two or more computer programs to communicate with each other.\n\n“We can turn around game modes and our fantasy RPG in about two weeks,” Finden said, “because of all of the tools that Discord allows us.”\n\nImage: Alchemy: Battle for Ankhos\n\nFinden said two types of players are most interested in Alchemy: Battle for Ankhos—the classic turn-based strategy player that wants to be competitive with the game, and the Web3 die-hard that wants to come in, have power over the world, influence the players, and see if they can capitalize on some value that the ecosystem has generated.\n\n“We've designed the game modes and the tradable assets in the game in a way where the Web3 players and the Web2 players create this circular economy,” Finden said, emphasizing that no cryptocurrency needs to be spent to play on Discord.\n\nIt's time to Descend. 🔻 pic.twitter.com/gTdQXchMfe — Alchemy: Battle for Ankhos (@play_alchemy) June 27, 2023\n\nMuch of the computational work is done on a server that operates in the background, and the team leverages Discord’s scalability to circumvent any network congestion on the platform.\n\nAD\n\nAD\n\n“The demand is minimal as the files are extremely small, especially when compared to larger 3D-model games,” Finden said. “As such, server capacity and scalability are not an issue for us.”\n\nAI assistance\n\nSince the launch of OpenAI’s ChatGPT chatbot and the generative art platform Midjourney, advances in generative AI have enabled creators to generate dialogue and images for adventure games quickly with minimal skill. Finden said AI has allowed the small team to produce a volume of content that traditionally would have taken a robust squad of artists.\n\n“We have two options. We can either hire a full art studio, which takes a lot of time and iteration to nail our vision,” he said, “or I can just get really good at using the prompts to allow AI to create the vision that I have.”\n\nAI has become integral to the game, Finden said. Midjourney is considered a co-creator because some of the creative ideas the AI tool spits out end up finding their way into the world that the Alchemy: Battle for Ankhos team is creating.\n\n“We're using Midjourney prompts to create art on demand; we also pre-create a lot of the art and layer it in based on the decisions that the player makes,” Finden said. “On the chat side, we use OpenAI to power our NPCs in a way that gives them some creativity in their character.”\n\nAlchemy: Battle for Ankhos uses AI prompts to hone the character's behavior and ensure the story progresses in the desired direction.\n\n“We give them some creative control,\" Finden said, \"so that each time a player goes into this world, they get a slightly different experience.\"\n\nFinden said that what intrigues him about the potential of AI in gaming is the idea of having a fully generative dungeon master-type entity that takes players on quests. That's the long-term goal, but even with a tiny team, Finden believes it's possible using this unique development model.\n\nAD\n\nAD\n\n“I'm really excited to see what happens when we allow for that,” he said. “I think hopefully we can see that in the future. But it's going to be an adventure to get there.”"}]