[{"id": 4, "url": "https://news.google.com/rss/articles/CBMiiQFodHRwczovL2JpdGNvaW5pc3QuY29tL3NoaWJhLWludS1zaGliLXVudmVpbHMtc2hpYmFyaXVtLXBvcnRhbC1zb2xhbmEtc29sLWF0dGVtcHRzLXNlY29uZC1yZXN0YXJ0LWFuZC10bXMtbmV0d29yay10bXNuLXByZWRpY3RlZC10by1zb2FyL9IBjQFodHRwczovL2JpdGNvaW5pc3QuY29tL3NoaWJhLWludS1zaGliLXVudmVpbHMtc2hpYmFyaXVtLXBvcnRhbC1zb2xhbmEtc29sLWF0dGVtcHRzLXNlY29uZC1yZXN0YXJ0LWFuZC10bXMtbmV0d29yay10bXNuLXByZWRpY3RlZC10by1zb2FyL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Mar 2023 10:36:00 GMT", "title": "<PERSON><PERSON> (SHIB) Unveils Shibarium Portal, Solana (SOL) Attempts Second Restart and TMS Network (TMSN) - Bitcoinist", "content": "Coins like Shiba Inu (SHIB) and Solana (SOL) have had noticeable changes in the last week. And while they are slowly attempting to recover from a disastrous 2022, a project recently finished Stage 1 of presale and has gained the attention of numerous prominent analysts.\n\nThat project is TMS Network! This article will analyze what all three coins bring to the table and how they might fare soon!\n\n<PERSON><PERSON> Inu (SHIB)\n\nThe Dogecoin-inspired meme token based on Ethereum, Shiba Inu (SHIB), was introduced in 2020. Shiba Inu (SHIB) is intended for transactions and other services like other tokens.\n\nThe portal for the much anticipated Shibarium solution for Shiba Inu (SHIB) was recently released. This portal will enable Shiba Inu (SHIB) stakeholders to collaborate and become validators.\n\nHowever, the price for Shiba Inu (SHIB) leaves a lot to be desired as it currently trades for $0.00001233. This is a decrease of 5% in the past week, and experts are predicting this bearish trend to continue as a $1 valuation may not come anytime soon for Shiba Inu (SHIB).\n\nSolana (SOL)\n\nCreators of decentralized apps can get various advantages from Solana (SOL), a Layer-1 decentralized platform. The Solana (SOL) token can cover transaction fees within the broad ecosystem.\n\nIn the last week, while validators prepared for a second restart attempt that they thought would bring back service for users, the Solana (SOL) network remained locked. But after the second restart, Solana (SOL) again went online, and the issue was resolved.\n\nAt the moment, Solana (SOL) has a value of $22.68 and a market cap of $8.5B, an increase of 0.61% in the past 24 hours. Even if Solana (SOL) may see a good price trajectory in the coming months, investing in other projects with more room for growth would be more profitable in the long run!\n\nTMS Network (TMSN)\n\nThanks to its cutting-edge innovation and future development potential, TMS Network is getting popular and capturing the attention of crypto investors. The TMS Network will be a decentralized trading platform built on Ethereum that enables users to trade all derivatives using only crypto payments, no FIAT needed!\n\nTMS Network provides traders with access to the knowledge and perceptions of more seasoned traders by allowing them to follow and mimic the transactions of successful traders. Blockchain technology will also bring comprehensive data on market trends and price changes through real-time and past on-chain analytics to all traders!\n\nWant to start online trading but are hesitant due to high service fees? Know that TMS Network provides quicker transaction speeds and reduced costs than its rivals. With these features under its belt, TMS Network may become one of the most powerful entities in the trading industry!\n\nThe presale for the TMSN token is currently in Stage 2, rising to a price of $0.032, with investors flocking to it. This token will provide holders with commission fees from every platform trade, governance, and more for a meager cost! Analysts have even hinted at the possibility of TMSN becoming a blue-chip token valued at $2.30 by December 2023!\n\nWe believe TMS Network could be one of the most profitable investment options due to its solid fundamentals and foundation, so follow the presale below and do not miss out on this once-in-a-lifetime opportunity!\n\nPresale: https://presale.tmsnetwork.io\n\nWebsite: https://tmsnetwork.io\n\nTelegram: https://t.me/TMSNetworkIO\n\nTwitter: https://twitter.com/tmsnetworkio\n\nDisclaimer: This is a paid release. The statements, views and opinions expressed in this column are solely those of the content provider and do not necessarily represent those of Bitcoinist. Bitcoinist does not guarantee the accuracy or timeliness of information available in such content. Do your research and invest at your own risk."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiZ2h0dHBzOi8vd3d3Lmdpem1vdGltZXMuY29tL2Jsb2NrY2hhaW4vbmZ0L3dvcm1ob2xlLWFwdG9zLW5mdC1icmlkZ2UtaG93LXRvLXNlbmQtbmZ0LWZyb20tdG8tYXB0b3MvNDU1OTbSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Mar 2023 08:00:00 GMT", "title": "Wormhole Launches Aptos NFT Bridge - How to send NFT from/to Aptos - Gizmo Times", "content": "Wormhole has now made it possible for anyone to bridge their NFTs to Aptos, and from Aptos to the other chains as well. The Wormhole bridge was already working for the tokens, i.e. you could always convert your tokens like SOL, ETH, and others to Aptos and get them sent onto the Aptos network in the form of either APT or the other tokens that are available on the Aptos network. But this new update brings the possibility of bridging NFTs and getting them sent from other networks to Aptos, and vice-versa.\n\nWhile the NFT can be transferred from one chain to the other, <PERSON><PERSON><PERSON> makes sure that the metadata is preserved and shows up on the new chain where it ends up. When an NFT is transferred out of any other chain to Aptos, here is what happens.\n\nThe NFT gets locked up in the NFT bridge smart contract.\n\nAn equivalent wrapped NFT is minted into a corresponding collection on the destination chain.\n\nThis wrapped NFT has the same name, looks the same as the original one, and behaves exactly like other NFTs on the chain. On EVM chains, the wrapped NFTs are ERC721 tokens, on Solana, they’re SPL tokens with Metaplex metadata, and on Aptos, they’re instances of the Aptos Token standard.\n\nIn addition to the name and look, what makes the wrapped NFTs unique is the ability to send them back to the original chain and unlock the original NFT. This means, for example, that an NFT that originates from Aptos can be bridged to Ethereum, subsequently sold on Opensea and then transferred back to Aptos by the new owner.\n\nWhich chains support the NFT bridging via Wormhole to Aptos?\n\nThe chains that support bridging of NFTs from/to Aptos include Arbitrum, Aurora, Avalanche, BSC, Celo, Ethereum, Fantom, Karura, Klaytn, Moonbeam, Oasis, Optimsm, Polygon, and Solana.\n\nHow much are the charges for bridging NFTs to Aptos?\n\nIt is the usual transaction charges that are spent for the bridging to happen on both chains. For example, if the NFT is on the Ethereum chain, the charges depend on the time of bridging and the network congestion, and it can take a few dollars as the normal transaction fee, and the second transaction is to be done on the Aptos network to redeem the NFT on Aptos, and that is the transaction fee charged to complete the process.\n\nHow to bridge an NFT from another chain to Aptos?\n\nOpen Wormhole NFT Bridge platform\n\nThe first step is to decide which NFT and from which chain are you going to bridge to Aptos. Choose the chain, and then connect the wallet and you will see the list of available NFTs to bridge from that wallet. Select the NFT, and it shows up on the page. As an example, we tried bridging Macks NFT from the Solana chain.\n\nGo to the next step where you choose the target, i.e. the recipient chain and address. Here, click on Aptos and connect the Aptos wallet where you want to send the NFT.\n\nIn the next step, tap on “Transfer” and it would open your Solana wallet transaction pop-up with the details mentioning that the NFT will be gone from your Solana wallet and there will be a transaction fee that will be charged. Confirm it, and you will see the confirmations on the screen.\n\nNow, the NFT is sent once all the needed confirmations are done. The final step is here. Redeem the NFT on the recipient chain wallet. In this case, you have to do it on the wallet that you used earlier as the Aptos recipient wallet. Click on Redeem and you will see the transaction pop-up on the Aptos wallet. Confirm it and you will see the NFT on your Aptos wallet.\n\nIt’s easy, but you might need that confidence and guidance for the first time if you are new to bridging. Wormhole has made the interface very easy for users with any level of knowledge and experience.\n\nCan I send NFT back from Aptos to the original chain?\n\nThe best part about wrapped NFTs is that they have the ability to get sent back to the original chain as they were, and the NFT can be unlocked in the original chain. Even if the NFT is traded on the new chain, the new owner can choose to keep it or take it back to its original chain and see it as it is.\n\n“With the Aptos NFT bridge, Wormhole brings a new level of interoperability to the network. Aptos welcomes millions of assets, thousands of creators and artists from across the many chains accessible via this bridge.” – Mo Shaikh, Co-Founder & CEO @aptoslabs\n\nSource: Wormhole"}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiZ2h0dHBzOi8vd3d3Lmdpem1vdGltZXMuY29tL2Jsb2NrY2hhaW4vbmZ0L3dvcm1ob2xlLWFwdG9zLW5mdC1icmlkZ2UtaG93LXRvLXNlbmQtbmZ0LWZyb20tdG8tYXB0b3MvNDU1OTbSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 09 Mar 2023 08:00:00 GMT", "title": "Wormhole Launches Aptos NFT Bridge - How to send NFT from/to Aptos - Gizmo Times", "content": "Wormhole has now made it possible for anyone to bridge their NFTs to Aptos, and from Aptos to the other chains as well. The Wormhole bridge was already working for the tokens, i.e. you could always convert your tokens like SOL, ETH, and others to Aptos and get them sent onto the Aptos network in the form of either APT or the other tokens that are available on the Aptos network. But this new update brings the possibility of bridging NFTs and getting them sent from other networks to Aptos, and vice-versa.\n\nWhile the NFT can be transferred from one chain to the other, <PERSON><PERSON><PERSON> makes sure that the metadata is preserved and shows up on the new chain where it ends up. When an NFT is transferred out of any other chain to Aptos, here is what happens.\n\nThe NFT gets locked up in the NFT bridge smart contract.\n\nAn equivalent wrapped NFT is minted into a corresponding collection on the destination chain.\n\nThis wrapped NFT has the same name, looks the same as the original one, and behaves exactly like other NFTs on the chain. On EVM chains, the wrapped NFTs are ERC721 tokens, on Solana, they’re SPL tokens with Metaplex metadata, and on Aptos, they’re instances of the Aptos Token standard.\n\nIn addition to the name and look, what makes the wrapped NFTs unique is the ability to send them back to the original chain and unlock the original NFT. This means, for example, that an NFT that originates from Aptos can be bridged to Ethereum, subsequently sold on Opensea and then transferred back to Aptos by the new owner.\n\nWhich chains support the NFT bridging via Wormhole to Aptos?\n\nThe chains that support bridging of NFTs from/to Aptos include Arbitrum, Aurora, Avalanche, BSC, Celo, Ethereum, Fantom, Karura, Klaytn, Moonbeam, Oasis, Optimsm, Polygon, and Solana.\n\nHow much are the charges for bridging NFTs to Aptos?\n\nIt is the usual transaction charges that are spent for the bridging to happen on both chains. For example, if the NFT is on the Ethereum chain, the charges depend on the time of bridging and the network congestion, and it can take a few dollars as the normal transaction fee, and the second transaction is to be done on the Aptos network to redeem the NFT on Aptos, and that is the transaction fee charged to complete the process.\n\nHow to bridge an NFT from another chain to Aptos?\n\nOpen Wormhole NFT Bridge platform\n\nThe first step is to decide which NFT and from which chain are you going to bridge to Aptos. Choose the chain, and then connect the wallet and you will see the list of available NFTs to bridge from that wallet. Select the NFT, and it shows up on the page. As an example, we tried bridging Macks NFT from the Solana chain.\n\nGo to the next step where you choose the target, i.e. the recipient chain and address. Here, click on Aptos and connect the Aptos wallet where you want to send the NFT.\n\nIn the next step, tap on “Transfer” and it would open your Solana wallet transaction pop-up with the details mentioning that the NFT will be gone from your Solana wallet and there will be a transaction fee that will be charged. Confirm it, and you will see the confirmations on the screen.\n\nNow, the NFT is sent once all the needed confirmations are done. The final step is here. Redeem the NFT on the recipient chain wallet. In this case, you have to do it on the wallet that you used earlier as the Aptos recipient wallet. Click on Redeem and you will see the transaction pop-up on the Aptos wallet. Confirm it and you will see the NFT on your Aptos wallet.\n\nIt’s easy, but you might need that confidence and guidance for the first time if you are new to bridging. Wormhole has made the interface very easy for users with any level of knowledge and experience.\n\nCan I send NFT back from Aptos to the original chain?\n\nThe best part about wrapped NFTs is that they have the ability to get sent back to the original chain as they were, and the NFT can be unlocked in the original chain. Even if the NFT is traded on the new chain, the new owner can choose to keep it or take it back to its original chain and see it as it is.\n\n“With the Aptos NFT bridge, Wormhole brings a new level of interoperability to the network. Aptos welcomes millions of assets, thousands of creators and artists from across the many chains accessible via this bridge.” – Mo Shaikh, Co-Founder & CEO @aptoslabs\n\nSource: Wormhole"}]