[{"id": 4, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vZW4uY3J5cHRvbm9taXN0LmNoLzIwMjMvMDMvMDIvbGF0ZXN0LWNyeXB0by1uZXdzLWFib3V0LXNvbGFuYS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 02 Mar 2023 08:00:00 GMT", "title": "Latest crypto news about Solana (SOL): improvements to the blockchain and “tokenized” real estate - The Cryptonomist", "content": "Latest and important crypto news regarding Solana (SOL). Apparently, Solana plans to improve its blockchain after the recent outage that generated concern among users.\n\nFurthermore, it seems that the first “backed” real estate NFTs are being launched, allowing users to invest in real “tokenized” homes.\n\nSolana crypto news: enhancement to the blockchain\n\nAfter a network slowdown left users in a panic, Solana released plans to improve its latest network upgrade.\n\nFollowing the latest network update 1.14 on 25 February, the Solana network experienced a significant slowdown in blockchain production. Reacting immediately to the transaction disruptions, validators downgraded the software to increase performance.\n\nHowever, on 28 February, <PERSON><PERSON><PERSON>, founder and CEO of Solana Labs, issued another statement on how the ecosystem plans to improve the network’s recent upgrades. The main goal of the plan is stability as the network continues its transition.\n\nIndeed, <PERSON><PERSON><PERSON> stated:\n\n“The 2022 priorities were building new features and new tools. 2023 is stability. Tldr 1/3 of core eng will focus on stability and adversarial testing. Here are my thoughts.”\n\nThe statement outlines a six-step plan for engineers to help streamline the process and reveals the formation of an adversary team, which includes one-third of Solana’s engineering team.\n\nThis team was formed to implement additional hooks and instrumentation in the validator code and to detect exploits in the underlying protocols.\n\nIt has also defined ways to focus on finding the stability of the entire network. This includes a second validator client developed by Jump Crypto‘s firedancer team and Mango DAO developers creating new tools and implementing local fee markets, among other efforts.\n\nPrior to version 1.14, key engineers were working to fix problems that affected the speed and usability of the network, such as invalid gas calculation, lack of flow control for transactions, and lack of fee markets, among other more technical issues.\n\nThese issues were prioritized to improve the user experience. However, after the latest release, core engineers plan to engage other developers and external auditors to test and find exploits.\n\nDisruption of the Solana blockchain: the reasons why\n\nYakovenko’s recent statement mentions that the investigation into what happened during the initial outage is still ongoing, and the community will be informed when the information becomes available.\n\nIn any case, he clarified on 28 February that on-chain voting was not the cause of the slowdown.\n\nThe community’s reaction to the outage has been frantic, with some users calling the system a “transaction killer.”\n\nHowever, the response to Yakovenko’s improvement roadmap was mixed: some users said the news was “fantastic,” while others questioned Solana’s integrity:\n\n“Your testing process has failed with every major release. Do you at least have a real testing process? I’m not sure. A simple question: who is responsible for the overall coordination of tests and releases so far? You? raj? … it’s not serious.”\n\nThe Solana ecosystem call is scheduled for 2 March 2023, where the state of the ecosystem, among other issues, will be discussed.\n\nOn 27 March, Helium Network‘s communication protocol plans to migrate to Solana’s blockchain to distribute oracles.\n\nMore crypto news for Solana: “tokenized” homes\n\nThe company Homebase has sold the first NFTs “backed” by real estate on Solana, allowing users to invest in real “tokenized” homes.\n\nSpecifically, a share of a three-bedroom apartment in McAllen, Texas, was sold with 2,468 NFTs on offer, with a total value of $246,000.\n\nUsers can invest through the program in single-family properties, with each of these to be held by a limited liability company. Moreover, the ownership of each property would be associated with Homebase’s NFTs.\n\nAnd, when the home is rented out, the users (or rather the NFT holders) would receive the monthly income in the form of USDC, in proportion to the asset being owned. This is a way, according to the company, to enable more people to invest in the sector, which is very often exclusive given the liquidity needed to operate in it.\n\nFrom a regulatory standpoint, according to the releases, NFTs would be issued and registered under the SEC, and thus considered “securities” under the agency’s regulations.\n\nAlex Kim, co-founder of Homebase, stated the following:\n\n“We have decided to take one of the more prudent legal approaches. And we decided to register our assets as ‘securities’ from day one.”\n\nThis is not the first time such an operation has seen the light of day. Already last year, for example, Vesta Equity decided to sell similar products on Algorand, but the business did not seem to attract new investment.\n\nBy contrast, Roofstock onChain, a real estate NFT marketplace, has been more successful, recording the sale of a property in South Carolina last October, valued at $175,000."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMitQFodHRwczovL25ld3MuYWJwbGl2ZS5jb20vYnVzaW5lc3MvY3J5cHRvL2NyeXB0b2N1cnJlbmN5LXByaWNlLXRvZGF5LWluLWluZGlhLW1hcmNoLTItY2hlY2stZ2xvYmFsLW1hcmtldC1jYXAtYml0Y29pbi1idGMtZXRoZXJldW0tZG9nZS1zb2xhbmEtbGl0ZWNvaW4tbWFrZXItbWtyLWdhaW5lci1sb3Nlci0xNTg1NTcw0gG5AWh0dHBzOi8vbmV3cy5hYnBsaXZlLmNvbS9idXNpbmVzcy9jcnlwdG8vY3J5cHRvY3VycmVuY3ktcHJpY2UtdG9kYXktaW4taW5kaWEtbWFyY2gtMi1jaGVjay1nbG9iYWwtbWFya2V0LWNhcC1iaXRjb2luLWJ0Yy1ldGhlcmV1bS1kb2dlLXNvbGFuYS1saXRlY29pbi1tYWtlci1ta3ItZ2FpbmVyLWxvc2VyLTE1ODU1NzAvYW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 02 Mar 2023 08:00:00 GMT", "title": "Cryptocurrency Price Today: Top Coins See Minor Movements, Maker Becomes Top Gainer - ABP Live", "content": "Bitcoin (BTC), the oldest and most valued crypto coin in the world, remained stable within the $23,000 range early Thursday morning. Other popular altcoins — including the likes of Ethereum (ETH), Dogecoin (DOGE), Ripple (XRP), Litecoin (LTC), and Solana (SOL) — saw a mix of minor gains and losses across the board. The Maker (MKR) token emerged to be the biggest gainer with a 24-hour jump of over 11 percent. NEM (XEM), on the other hand, became the top loser, with a 24-hour dip of nearly 6 percent.\n\nThe global crypto market cap stood at $1.07 trillion at the time of writing, registering a 24-hour gain of 0.29 percent.\n\nBitcoin (BTC) price today\n\nBitcoin price stood at $23,500.70, registering a 24-hour gain of 0.16 percent, as per CoinMarketCap. According to Indian exchange WazirX, BTC price stood at Rs 20.30 lakhs.\n\nEthereum (ETH) price today\n\nETH price stood at $1,648.04, marking a 24-hour gain of 0.78 percent at the time of writing. As per WazirX, Ethereum price in India stood at Rs 1.42 lakhs.\n\nDogecoin (DOGE) price today\n\nDOGE registered a 24-hour dip of 1.044 percent, as per CoinMarketCap data, currently priced at $0.08108. As per WazirX, Dogecoin price in India stood at Rs 7.15.\n\nLitecoin (LTC) price today\n\nLitecoin saw a 24-hour gain of 0.97 percent. At the time of writing, it was trading at $97.03. LTC price in India stood at Rs 8,230.32.\n\nRipple (XRP) price today\n\nXRP price stood at $0.3783, seeing a 24-hour gain of 0.52 percent. As per WazirX, Ripple price stood at Rs 32.59.\n\nSolana (SOL) price today\n\nSolana price stood at $22, marking a 24-hour dip of 1.41 percent. As per WazirX, SOL price in India stood at Rs 2,001.\n\nTop crypto gainers today (March 2)\n\nAs per CoinMarketCap data, here are the top five crypto gainers over the past 24 hours:\n\nMaker (MKR)\n\nPrice: $915.73\n\n24-hour gain: 11.05 percent\n\nConflux (CFX)\n\nPrice: $0.2275\n\n24-hour gain: 9.54 percent\n\nFilecoin (FIL)\n\nPrice: $6.97\n\n24-hour gain: 5.18 percent\n\nAptos (APT)\n\nPrice: $13.18\n\n24-hour gain: 4.94 percent\n\nXDC Network (XDC)\n\nPrice: $0.02845\n\n24-hour gain: 4.60 percent\n\nTop crypto losers today (March 2)\n\nAs per CoinMarketCap data, here are the top five crypto losers over the past 24 hours:\n\nNEM (XEM)\n\nPrice: $0.05065\n\n24-hour loss: 5.48 percent\n\nKlaytn (KLAY)\n\nPrice: $0.2719\n\n24-hour loss: 5.31 percent\n\nImmutableX (IMX)\n\nPrice: $1.04\n\n24-hour loss: 5.28 percent\n\nLido DAO (LDO)\n\nPrice: $2.95\n\n24-hour loss: 4.63 percent\n\nssv.network (SSV)\n\nPrice: $42.85\n\n24-hour loss: 4.48 percent\n\nWhat crypto exchanges are saying about the current market scenario\n\nMudrex co-founder and CEO Edul Patel told ABP Live, “After dropping towards $24,000 on Wednesday following US Consumer Confidence data, Bitcoin rebounded to the $23,500 level and has since risen by more than 1 percent in the past 24 hours. However, BTC still appears somewhat weak and will require recovery to make a significant move in the upcoming days. In contrast, Ethereum saw a 2 percent increase over the previous day and traded between the $1,613 and $1,650 levels. The immediate support for ETH is at the $1,600 and $1,550 levels, while resistance is at the $1,670 level.”\n\nWazirX Vice President Rajagopal Menon said, “Bitcoin and Ethereum prices are not expected to undergo any major changes in the coming days. France continues its efforts to lead the crypto regulation efforts of the EU with tighter legislation for new crypto firms that will enter the market. Hong Kong’s evolving stance on crypto and consequent steps to introduce digital assets in the country is being actively supported by China. It is believed that Beijing is treating Hong Kong as a sandbox for crypto adoption amidst its own conflicted take on the asset.”\n\nKunji founder Anurag Dixit offered his take, “Silvergate Capital Corporation's stock price dropped severely after the bank disclosed that it may be subject to inquiries from the Department of Justice (DOJ), congressional committees, and bank regulators. This highlights the increased scrutiny that the crypto industry is currently facing from regulators and lawmakers. Previously the crackdown wasn't this steep on the traditional finance side of the ecosystem, but the current movements suggest the near-term focus is going to be the rails between traditional finance and digital assets.\"\n\nShivam Thakral, the CEO of BuyUCoin, said, “The crypto market remained flat due to unchanged macroeconomic factors in the last 24 hours. BTC and ETH showed a marginal jump in their value and are trading at $23,516.89 and $1,649.09 respectively. Australian central banks have experessed its willingness to explore the use of CBDC in the country and being a commonwealth realm, it may have a larger impact on the CBDC plans of other commonwealth members.”\n\nDisclaimer: Crypto products and NFTs are unregulated and can be highly risky. There may be no regulatory recourse for any loss from such transactions. Cryptocurrency is not a legal tender and is subject to market risks. Readers are advised to seek expert advice and read offer document(s) along with related important literature on the subject carefully before making any kind of investment whatsoever. Cryptocurrency market predictions are speculative and any investment made shall be at the sole cost and risk of the readers."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzI0NjY2L3NvbGFuYS10cHMv0gEyaHR0cDovL2FtcC5jb2luY29kZXguY29tL2FydGljbGUvMjQ2NjYvc29sYW5hLXRwcy8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 02 Mar 2023 08:00:00 GMT", "title": "Solana TPS - Will Solana Handle 600,000 Transactions per Second Soon? - CoinCodex", "content": "Solana is one of the fastest and most efficient blockchains on the market today. This has attracted a number of promising projects to build on Solana, and the platform has also attracted many users that have been priced out of Ethereum due to its high transaction fees.\n\nSo, how fast is Solana exactly? Solana is handling over 5,000 transactions per second in real-world scenarios, but has reached as high as 65,000 TPS during testing.\n\nIn this article, we will be taking at the Solana TPS figures and how Solana stacks up against other popular blockchains in terms of performance. We will also be looking at some future developments that will improve the scalability of Solana.\n\nWhat is Solana?\n\nSolana is a blockchain platform with support for smart contracts. The project was founded in 2017 by software engineer <PERSON><PERSON><PERSON>. Thanks to its high throughput and low costs, Solana is positioning itself as the underlying infrastructure for blockchain-based applications that are targeting a large userbase or need to handle a large number of transactions in a short period of time (for example, high-performance decentralized crypto exchanges).\n\nIn contrast to Ethereum, which is meant to scale through layer 2 solutions, Solana is designed to reach sufficient scalability at its base layer. Solana implements a Proof-of-Stake consensus mechanism and enhances it through a Proof-of-History timestamping function.\n\nSolana is already very scalable, especially when compared to other blockchains available today. You’ll be paying less than $0.001 per transaction, and you can expect it to be completed in about 5 seconds. In fact, Solana is one of the cheapest cryptocurrencies to transfer today.\n\nWhile Solana provides a very fast and low-cost alternative to Ethereum, the network has struggled with reliability. Solana has seen outages much more frequently than its competitors, and this is definitely an area where Solana will need to regain the crypto community’s trust.\n\nIn Solana, updates related to staking happen in “epochs”. In other words—staking, unstaking, and the distribution of staking rewards happens when the current Solana epoch ends and a new epoch starts. One epoch typically lasts between 2-3 days, but the exact length depends on how long the block times are in each epoch. At the time of writing, the Solana blockchain is in its 417th epoch and has a block height of about 163.5 million.\n\nSolana TPS—How many transactions per second can Solana handle?\n\nPer the Solana whitepaper, Solana is theoretically capable of handling up to 710,000 transactions per second. Moving away from theoretical targets, we can point out that Solana’s transactions per second peaked at around 65,000 during testing.\n\nWhile these figures are interesting, how many transactions per second is Solana actually handling in the real world? In practice, Solana is handling between 5,000 and 10,000 transactions per second, according to data from Solana Compass. The actual figure fluctuates quite a bit on a block-by-block basis depending on demand. If you wish to see the current Solana TPS, you can find the relevant information on most Solana explorers.\n\nRaw Solana TPS data over an 1 hour period. Image source: Solana Compass.\n\nHowever, there is a caveat to these figures. This is because the raw TPS metric doesn’t just count standard Solana transactions, but also includes vote transactions submitted by validators. Still, Solana is easily among the most performant blockchains on the market today when it comes to the number of transactions it can handle per second.\n\nSolana is expected to scale even further in the future\n\nWhile we can see that Solana is already able to process a large number of transactions per second, the platform’s scalability is expected to improve even further in future.\n\nOne project that could help Solana massively improve its scalability is Firedancer. Firedancer is a Solana validator client being developed by high-frequency trading firm Jump using the C++ programming language. According to Firedancer developers, the client is theoretically capable of processing up to 600,000 transactions per second.\n\nJump Trading claims that Solana’s throughput is currently not bound by hardware limitations, but software inefficiencies. The company aims to use its experience at the cutting edge of the high-frequency trading industry to create a Solana validator client that operates at the very limits of what the currently available hardware can handle.\n\nFiredancer will also contribute to the effective decentralization of Solana by providing an alternative validator client to the one created by Solana Labs. A greater variety of clients used by validator nodes means that bugs and vulnerabilities in one implementation have less impact on the network as a whole.\n\nSolana TPS versus other blockchains\n\nAs we’ve already mentioned, Solana’s TPS figures are very impressive when compared to other blockchains. Let’s check out exactly how Solana stacks up against other popular layer 1 blockchain platforms in terms of throughput.\n\nBlockchain Transactions per second (TPS) Solana 65,000 TPS* Polygon 7,000 TPS* Avalanche 4,500 TPS* XRP 1,500 TPS Cardano 250 TPS Litecoin 50 TPS Ethereum 30 TPS Bitcoin 7 TPS\n\n*Achieved in testing.\n\nThe bottom line—Solana is one of the fastest blockchains available today\n\nWhether you are trading NFTs on Solana or participating on the platform’s decentralized finance apps, you’ll be enjoying low fees and fast transactions. Solana’s transactions per second figure can easily go into the thousands.\n\nTo be frank, the maximum number of transactions Solana can process is not really relevant for everyday users, as it can handle the current level of demand without too many issues. However, it’s good to know that Solana will likely be able to tackle the load even if blockchain is adopted by a much greater number of people in the future.\n\nSolana’s scalability is one of the biggest reasons why Solana has a good chance of recovering from the crash it saw in 2022."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzI0NjY2L3NvbGFuYS10cHMv0gEyaHR0cDovL2FtcC5jb2luY29kZXguY29tL2FydGljbGUvMjQ2NjYvc29sYW5hLXRwcy8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 02 Mar 2023 08:00:00 GMT", "title": "Solana TPS - Will Solana Handle 600,000 Transactions per Second Soon? - CoinCodex", "content": "Solana is one of the fastest and most efficient blockchains on the market today. This has attracted a number of promising projects to build on Solana, and the platform has also attracted many users that have been priced out of Ethereum due to its high transaction fees.\n\nSo, how fast is Solana exactly? Solana is handling over 5,000 transactions per second in real-world scenarios, but has reached as high as 65,000 TPS during testing.\n\nIn this article, we will be taking at the Solana TPS figures and how Solana stacks up against other popular blockchains in terms of performance. We will also be looking at some future developments that will improve the scalability of Solana.\n\nWhat is Solana?\n\nSolana is a blockchain platform with support for smart contracts. The project was founded in 2017 by software engineer <PERSON><PERSON><PERSON>. Thanks to its high throughput and low costs, Solana is positioning itself as the underlying infrastructure for blockchain-based applications that are targeting a large userbase or need to handle a large number of transactions in a short period of time (for example, high-performance decentralized crypto exchanges).\n\nIn contrast to Ethereum, which is meant to scale through layer 2 solutions, Solana is designed to reach sufficient scalability at its base layer. Solana implements a Proof-of-Stake consensus mechanism and enhances it through a Proof-of-History timestamping function.\n\nSolana is already very scalable, especially when compared to other blockchains available today. You’ll be paying less than $0.001 per transaction, and you can expect it to be completed in about 5 seconds. In fact, Solana is one of the cheapest cryptocurrencies to transfer today.\n\nWhile Solana provides a very fast and low-cost alternative to Ethereum, the network has struggled with reliability. Solana has seen outages much more frequently than its competitors, and this is definitely an area where Solana will need to regain the crypto community’s trust.\n\nIn Solana, updates related to staking happen in “epochs”. In other words—staking, unstaking, and the distribution of staking rewards happens when the current Solana epoch ends and a new epoch starts. One epoch typically lasts between 2-3 days, but the exact length depends on how long the block times are in each epoch. At the time of writing, the Solana blockchain is in its 417th epoch and has a block height of about 163.5 million.\n\nSolana TPS—How many transactions per second can Solana handle?\n\nPer the Solana whitepaper, Solana is theoretically capable of handling up to 710,000 transactions per second. Moving away from theoretical targets, we can point out that Solana’s transactions per second peaked at around 65,000 during testing.\n\nWhile these figures are interesting, how many transactions per second is Solana actually handling in the real world? In practice, Solana is handling between 5,000 and 10,000 transactions per second, according to data from Solana Compass. The actual figure fluctuates quite a bit on a block-by-block basis depending on demand. If you wish to see the current Solana TPS, you can find the relevant information on most Solana explorers.\n\nRaw Solana TPS data over an 1 hour period. Image source: Solana Compass.\n\nHowever, there is a caveat to these figures. This is because the raw TPS metric doesn’t just count standard Solana transactions, but also includes vote transactions submitted by validators. Still, Solana is easily among the most performant blockchains on the market today when it comes to the number of transactions it can handle per second.\n\nSolana is expected to scale even further in the future\n\nWhile we can see that Solana is already able to process a large number of transactions per second, the platform’s scalability is expected to improve even further in future.\n\nOne project that could help Solana massively improve its scalability is Firedancer. Firedancer is a Solana validator client being developed by high-frequency trading firm Jump using the C++ programming language. According to Firedancer developers, the client is theoretically capable of processing up to 600,000 transactions per second.\n\nJump Trading claims that Solana’s throughput is currently not bound by hardware limitations, but software inefficiencies. The company aims to use its experience at the cutting edge of the high-frequency trading industry to create a Solana validator client that operates at the very limits of what the currently available hardware can handle.\n\nFiredancer will also contribute to the effective decentralization of Solana by providing an alternative validator client to the one created by Solana Labs. A greater variety of clients used by validator nodes means that bugs and vulnerabilities in one implementation have less impact on the network as a whole.\n\nSolana TPS versus other blockchains\n\nAs we’ve already mentioned, Solana’s TPS figures are very impressive when compared to other blockchains. Let’s check out exactly how Solana stacks up against other popular layer 1 blockchain platforms in terms of throughput.\n\nBlockchain Transactions per second (TPS) Solana 65,000 TPS* Polygon 7,000 TPS* Avalanche 4,500 TPS* XRP 1,500 TPS Cardano 250 TPS Litecoin 50 TPS Ethereum 30 TPS Bitcoin 7 TPS\n\n*Achieved in testing.\n\nThe bottom line—Solana is one of the fastest blockchains available today\n\nWhether you are trading NFTs on Solana or participating on the platform’s decentralized finance apps, you’ll be enjoying low fees and fast transactions. Solana’s transactions per second figure can easily go into the thousands.\n\nTo be frank, the maximum number of transactions Solana can process is not really relevant for everyday users, as it can handle the current level of demand without too many issues. However, it’s good to know that Solana will likely be able to tackle the load even if blockchain is adopted by a much greater number of people in the future.\n\nSolana’s scalability is one of the biggest reasons why Solana has a good chance of recovering from the crash it saw in 2022."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzI0NjY0L2FuYXRvbHkteWFrb3ZlbmtvLW5ldC13b3J0aC_SAUNodHRwOi8vYW1wLmNvaW5jb2RleC5jb20vYXJ0aWNsZS8yNDY2NC9hbmF0b2x5LXlha292ZW5rby1uZXQtd29ydGgv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 02 Mar 2023 08:00:00 GMT", "title": "<PERSON><PERSON><PERSON>: How <PERSON> Is <PERSON>’s Founder? - CoinCodex", "content": "Blockchain engineer and Solana founder <PERSON><PERSON><PERSON> has an estimated net worth of more than $200 million. It is worth noting that his actual net worth could be substantially higher or lower, as there is little public information available regarding the scope of his crypto and traditional investments.\n\nWho is <PERSON><PERSON><PERSON>?\n\n<PERSON><PERSON><PERSON> is a software engineer and founder of the Solana cryptocurrency. Image source: Solana Labs\n\n<PERSON><PERSON><PERSON> is a co-founder and CEO of Solana, a high-performance blockchain platform. Born in Ukraine in 1981, <PERSON><PERSON><PERSON> moved to the United States in the early 1990s. It was at this time that <PERSON><PERSON><PERSON> learned C, his first programming language.\n\nDuring his college years amidst the height of the dot-com boom, <PERSON><PERSON><PERSON> was enamored by breakthrough software innovations and dreamed of “becoming the next Steve <PERSON>s or <PERSON>,” according to a Fortune interview. <PERSON><PERSON><PERSON> has a strong background in computer science and engineering, having previously worked as a software engineer at companies such as Qualcomm and Dropbox.\n\n<PERSON><PERSON><PERSON> has been closely involved in the blockchain industry for several years and has worked on various projects related to blockchain scalability and performance. In 2017, <PERSON><PERSON><PERSON> founded Solana, a high-performance layer 1 blockchain that boasts smart contract and decentralized applications (dApps) support and is capable of facilitating up to 65,000 transactions per second (TPS).\n\n<PERSON><PERSON><PERSON> is also an avid poker player, having scored several notable finishes throughout his gambling career, including a win in the $25,000 No-Limit Hold’em 6-Handed Championship in 2010 for roughly $1.3 million and a first-place finish in the 2005 Festa Al Lago III No-Limit Hold’em Championship for $700,000.\n\nWhat is Anatoly Yakovenko’s net worth in 2023?\n\nAccording to numerous online publications, Anatoly Yakovenko has an estimated net worth of $200 million. However, some sources claim the Ukrainian-born software engineer could have a billion-dollar net worth, although the veracity of these claims is questionable.\n\nGiven his early involvement in Solana, it is safe to assume that Yakovenko received a significant share of SOL tokens during the Solana token sale. While it is not known how many tokens he received, we do know that the founders of the Solana project received a 25% share of 500M+ SOL tokens.\n\nSOL was initially sold to investors for 22 cents in 2020. The token quickly gained traction among crypto investors and reached an all-time high north of $259 in November 2021. While the token lost a significant chunk of its value since then, SOL is currently still trading more than 100x above its token sale price.\n\nAfter last year’s collapse of FTX, which was one of the main backers of Solana, some investors and many in the crypto community proclaimed the project “dead”. However, that couldn’t be further from the truth, as the development of the chain is still in full swing, making an investment in Solana a potentially lucrative play.\n\nAnatoly Yakovenko’s investment history\n\nYakovenko is among the most prominent figures in the crypto industry. He regularly publishes blog posts tackling various topics related to blockchain technology and is still heavily involved in the development side of things. He is a systems engineer “through and through,” noted a general partner at VC firm Andreessen Horowitz, Ali Yahya, when speaking to Fortune.\n\nIn addition to his deep involvement with Solana, Yakovenko has made several significant investments in other companies over the years. Here are the five most notable investments per his Crunchbase profile:\n\nSlingshot Finance\n\nSlingshot Finance is a financial services company that aims to disrupt the CeFi sector by allowing users easy access to Web3. It allows users to connect their crypto wallets and token price data aggregated from numerous crypto markets.\n\nYakovenko participated in the Series A funding round for Slingshot Finance that closed in December 2021 and raised $15 million.\n\nQonto\n\nQonto is a money management platform that allows users to handle business accounts and transfers, invoices, expense reports, and bookkeeping. The service can be used by anyone from self-employed individuals to micro-enterprises and associations.\n\nQonto raised €486 million during their Series D funding round in January 2022, at a pre-money valuation of €3.9 billion. Yakovenko participated in the round, although his investment share is unknown.\n\nWayflyer\n\nWayflyer is an e-commerce funding platform that looks to help e-commerce businesses with funding and insight. So far, the company has helped over 1,500 businesses accelerate their growth.\n\nYakovenko was one of the 12 investors that participated in Waylfyer’s Series B in February 2022, which saw the company raise $150 million at a $1.5 billion market cap.\n\nCikalia\n\nCikalia is a Spanish-based instant property buyer that aims to reform the real estate markets for the digital age. Cikalia uses big data to make the buying and selling process more efficient and accessible to a broader range of customers.\n\nIn February 2022, Cikalia raised €75 million from four investors, one of which was the Solana founder.\n\nHivemapper\n\nHivemapper is a blockchain project that to supports a community-led initiative to create the world’s first decentralized map. Each Hivemapper user can help build the map by providing data through a specialized dashcam, which in turn rewards them with HONEY tokens.\n\nYakovenko participated in the company’s Series A financing round in April 2022, which raised $18 million.\n\nThe bottom line: The exact scope of Yakovensko’s net worth is shrouded in mystery\n\nAnatoly Yakovenko’s net worth is shrouded in mystery as there is virtually no publicly available data to pinpoint the scope of his wealth. What is known, however, suggests that Yakovenko has a multi-million dollar net worth, and potentially even a multi-billion dollar one.\n\nYakovenko was part of the Solana founding team that received 25% of all SOL tokens at their launch, and has participated in numerous financing rounds in a personal capacity in the last couple of years. If SOL were to reach $1,000 as some hope, Yakovenko could become one of the richest individuals in crypto.\n\nIf you want to learn more about the net worth of some of the most prominent figures in the crypto industry, make sure to check our articles on Ethereum co-founder Vitalik Buterin, Bitcoin founder Satoshi Nakamoto, former CEO of FTX Sam Bankman-Fried, and Terra founder Do Kwon."}]