[{"id": 0, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA4LzAzL3NvbGFuYS10b2tlbnMtb3ItaG93LWktbGVhcm5lZC10by1zdG9wLXdvcnJ5aW5nLWFuZC1sb3ZlLXRoZS1wb2ludHMv0gF1aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDgvMDMvc29sYW5hLXRva2Vucy1vci1ob3ctaS1sZWFybmVkLXRvLXN0b3Atd29ycnlpbmctYW5kLWxvdmUtdGhlLXBvaW50cy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 03 Aug 2023 07:00:00 GMT", "title": "<PERSON><PERSON>s or: How I Learned to Stop Worrying and Love the Points - CoinDesk", "content": "<PERSON> has a pragmatic if cynically-tinged vision of the crypto markets that the author of this column most certainly shares. Degenerate traders only want one thing, and it's disgusting (err, I mean money). Free tokens are money. Therefore, the degens want free tokens. They’ll go where the tokens are and they’ll do what they must to get them. If that means trading, they’ll do so on leverage; if that means staking, they’ll loop for more."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMicWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA4LzAzL3NvbGFuYS10b2tlbnMtb3ItaG93LWktbGVhcm5lZC10by1zdG9wLXdvcnJ5aW5nLWFuZC1sb3ZlLXRoZS1wb2ludHMv0gF1aHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDgvMDMvc29sYW5hLXRva2Vucy1vci1ob3ctaS1sZWFybmVkLXRvLXN0b3Atd29ycnlpbmctYW5kLWxvdmUtdGhlLXBvaW50cy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 03 Aug 2023 07:00:00 GMT", "title": "<PERSON><PERSON>s or: How I Learned to Stop Worrying and Love the Points - CoinDesk", "content": "<PERSON> has a pragmatic if cynically-tinged vision of the crypto markets that the author of this column most certainly shares. Degenerate traders only want one thing, and it's disgusting (err, I mean money). Free tokens are money. Therefore, the degens want free tokens. They’ll go where the tokens are and they’ll do what they must to get them. If that means trading, they’ll do so on leverage; if that means staking, they’ll loop for more."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vd3d3LmludmVzdGluZ2N1YmUuY29tL3NvbGFuYS1wcmljZS1wcmVkaWN0aW9uLWhlcmVzLXdoeS1pbS1zdGlsbC1idWxsaXNoLW9uLXNvbC_SAVxodHRwczovL3d3dy5pbnZlc3RpbmdjdWJlLmNvbS9zb2xhbmEtcHJpY2UtcHJlZGljdGlvbi1oZXJlcy13aHktaW0tc3RpbGwtYnVsbGlzaC1vbi1zb2wvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 03 Aug 2023 15:01:43 GMT", "title": "Solana Price Prediction: Here’s Why I’m Still Bullish On SOL - InvestingCube", "content": "Solana (SOL) price has performed extremely well in 2023. Since the start of the year, the price has surged 129%. After a strong rebound from its June Lows, SOL price is currently having a major pullback. This pullback might turn out to be very healthy for the smart contract cryptocurrency. Let me explain how.\n\nOn Thursday, cryptocurrencies continued their downtrend as the BTC price once again plummeted below $29,000. This intensified the sell-off in altcoin markets, sending most of them below their June lows. SOL crypto also showed a negative price action and was down 1.05% till press time.\n\nSOL Crypto TVL Fails To Impress\n\nDespite a massive rally in SOL right from the start of this year, the total locked value (TVL) hasn’t seen a similar increase. The TVL on the smart contract platform has increased only 50% as compared to the 129% surge in Solana price.\n\nOffsetting the effect of the price surge, these stats show an outflow of other coins from the Solana blockchain. The holders could have moved these funds to a different blockchain. Nevertheless, extremely impressive price action this year suggests that Solana is finally ready to recover by putting behind its FTX ties.\n\nSolana Price Prediction Remains Bullish\n\nAlthough SOL crypto has slid more than 31% from its July highs, it is yet to make a lower low on the daily chart. This bullish market structure will remain intact until the price closes a day below $20.43. Till then, I’ll keep finding good setups to go long.\n\nSolana price prediction might flip extremely bullish if the coin gains strength above the $25 level. It broke above this level last month but couldn’t reclaim it. A lot depends on the BTC price action in the coming days. If BTC breaks above $31,000, altcoins like SOL might follow.\n\nIn the meantime, I’ll keep sharing updated Solana analysis and my personal trades on my Twitter, where you are welcome to follow me."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiSGh0dHBzOi8vYW1iY3J5cHRvLmNvbS9zb2xhbmEtbG9uZy10ZXJtLWludmVzdG9ycy1jYW4tbG9vay10by10aGlzLWxldmVsL9IBTGh0dHBzOi8vYW1iY3J5cHRvLmNvbS9zb2xhbmEtbG9uZy10ZXJtLWludmVzdG9ycy1jYW4tbG9vay10by10aGlzLWxldmVsL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 03 Aug 2023 07:00:00 GMT", "title": "Solana: Long-term investors can look to this level - AMBCrypto News", "content": "Disclaimer: The information presented does not constitute financial, investment, trading, or other types of advice and is solely the writer’s opinion.\n\nSolana has a strongly bullish outlook on the higher timeframe price charts.\n\nThe retracement into a weekly region of interest meant buyers can look for opportunities to enter the market.\n\nSolana [SOL] has registered strong gains since the fall to $15 in mid-June. Prices climbed as high as $32 on 10 July, although the bulls were unable to hold on to the $30 level thereafter.\n\nRead Solana’s [SOL] Price Prediction 2023-24\n\nThe presence of a demand zone just below $22 from the weekly timeframe meant it was a place where a bullish reversal could occur. Bitcoin [BTC] and SOL both have a bearish short-term outlook. Can we see some more losses followed by a rally for Solana?\n\nThe confluence of breaker and Fib retracement presented a zone of interest for the buyers\n\nOn the 3-day chart, the price action has a bullish structure. The move past the previous lower high at $22.3 reached $32.13, signaling firm bullish intent. A set of Fibonacci retracement levels (yellow) were plotted based on the move from $12.8 to $32.13.\n\nIt showed the 50% and 61.8% retracement levels sat at $22.47 and $20.18 respectively. Moreover, on the weekly timeframe, a bullish breaker block was seen that extended from $20.26 to $22.3. This had confluence with the Fib levels too.\n\nTherefore, buyers can look to bid SOL after the price enters this zone. A lower timeframe bullish market structure break within this zone can give buyers an opportunity. Meanwhile, a move below the $18.32 level would invalidate this idea.\n\nThe OBV saw a large uptick in late June and July but has pulled back a bit over the past three weeks. Overall, the buying volume was still dominant. The RSI also showed bullish momentum with a reading of 56.\n\nThe lower timeframe SOL charts continued to show bearish pressure\n\nIs your portfolio green? Check out the Solana Profit Calculator\n\nData from Coinalyze showed that Open Interest saw a sharp drop on 30 July as the price fell from $25 to $23.5. This suggested bearish sentiment, which was compounded on 31 July when the OI rose alongside falling prices. The spot CVD was also in a downtrend.\n\nThe rising OI signaled strong bearishness, and the lack of demand meant Solana prices were headed lower in the near term. This could see SOL drop to the $22 demand zone."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vd3d3LmxjeC5jb20vcHJvb2Ytb2YtaGlzdG9yeS1leHBsYWluZWQv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 03 Aug 2023 07:00:00 GMT", "title": "Proof-of-History Explained - LCX", "content": "Blockchain technology has revolutionized our conceptions of trust and security in the digital era, allowing decentralized networks to function without intermediaries. However, scalability is a significant obstacle for Blockchain networks, mainly when processing numerous transactions swiftly and securely. Proof of History (PoH) comes into play here. PoH is a novel consensus mechanism devised by Solana Labs that generates timestamps for each block in the Blockchain using a cryptographic function called the Verifiable Delay Function (VDF). By ensuring the immutability and authenticity of these timestamps, Proof-of-History can considerably enhance the efficiency and security of Blockchain networks, especially those that require high transaction throughput and quick finalization.\n\nConsidering Proof of History’s operational efficacy, it will likely emerge as the leading consensus mechanism for developing decentralized financial systems. It bolsters the significance of Proof of History for Blockchain engineers and architects working on Web3-based DApp ecosystems. This article will describe Proof of History, its operation, its benefits, and its potential to revolutionize the Blockchain industry.\n\nWhat Is Proof-of-History?\n\nProof of History (PoH) is a novel consensus mechanism devised by the founder of Solana Labs, <PERSON><PERSON><PERSON>. PoH is based on the premise that the order of events in a Blockchain network is just as significant as the events themselves, and the ability to establish the order of events is essential to maintaining the network’s integrity. To accomplish this, PoH generates a timestamp for each block in the Blockchain using a cryptographic Verifiable Delay Function (VDF).\n\nThe VDF is designed to be delay-hard and memory-hard, making it challenging for adversaries to manipulate timestamps. Incorporating the timestamp generated by the VDF into each block of the Blockchain provides a verifiable and immutable record of the order in which transactions occurred. PoH permits rapid finality, which means that once a block is added to the Blockchain, it is deemed final and cannot be undone.\n\nPoH is predominantly utilized in the Solana Blockchain network, which was designed to be highly scalable and capable of processing thousands of transactions per second. PoH can increase the efficiency and speed of the Solana network by reducing the amount of storage and bandwidth required to maintain the blockchain while also providing a secure and verifiable record of transactions.\n\nCore Principles of Proof-of-History\n\nVerifiability: PoH ensures that the historical record is transparent, immutable, and publicly accessible. Anyone can independently verify the timestamps generated by the PoH mechanism, thus establishing trust and reducing the need for heavy computational processes.\n\nDecentralization: PoH is designed to operate in a decentralized manner, allowing multiple nodes to generate and validate timestamps concurrently. This ensures that no single entity has control over the generation of timestamps, enhancing the security and reliability of the blockchain network.\n\nEfficiency: PoH significantly improves the efficiency of blockchain networks by eliminating the need for computationally expensive consensus algorithms, such as PoW. With a reliable temporal order of events, nodes can more efficiently process transactions, achieve consensus, and validate blocks, resulting in faster throughput and reduced transaction fees."}]