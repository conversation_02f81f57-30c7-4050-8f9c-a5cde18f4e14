[{"id": 3, "url": "https://news.google.com/rss/articles/CBMibmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzAzLzMwL3NvbGFuYS1iYXNlZC1jcnlwdG8tZXhjaGFuZ2UtcmF5ZGl1bS1wcm9wb3Nlcy0ybS1idWctYm91bnR5LWZ1bmQv0gFyaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDMvMzAvc29sYW5hLWJhc2VkLWNyeXB0by1leGNoYW5nZS1yYXlkaXVtLXByb3Bvc2VzLTJtLWJ1Zy1ib3VudHktZnVuZC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 30 Mar 2023 07:00:00 GMT", "title": "Solana-Based Crypto Exchange Raydium Proposes $2M Bug Bounty Fund - CoinDesk", "content": "At press time, Raydium’s liquidity pools held over $37 million in total value locked, which is roughly three-quarters of the TVL held by Orca, Solana’s top decentralized exchange, according to DefiLlama. Its native token RAY was worth 23 cents Thursday, according to CoinGecko. It has slid 2% in the past 24 hours."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMibmh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzAzLzMwL3NvbGFuYS1iYXNlZC1jcnlwdG8tZXhjaGFuZ2UtcmF5ZGl1bS1wcm9wb3Nlcy0ybS1idWctYm91bnR5LWZ1bmQv0gFyaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDMvMzAvc29sYW5hLWJhc2VkLWNyeXB0by1leGNoYW5nZS1yYXlkaXVtLXByb3Bvc2VzLTJtLWJ1Zy1ib3VudHktZnVuZC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 30 Mar 2023 07:00:00 GMT", "title": "Solana-Based Crypto Exchange Raydium Proposes $2M Bug Bounty Fund - CoinDesk", "content": "At press time, Raydium’s liquidity pools held over $37 million in total value locked, which is roughly three-quarters of the TVL held by Orca, Solana’s top decentralized exchange, according to DefiLlama. Its native token RAY was worth 23 cents Thursday, according to CoinGecko. It has slid 2% in the past 24 hours."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMioAFodHRwczovL3d3dy5idXNpbmVzc3dpcmUuY29tL25ld3MvaG9tZS8yMDIzMDMyOTAwNTgxMy9lbi9UaGUtTmV3LUVyYS1vZi1QYXljaGVja3MtU3BlbmQtQ3J5cHRvLUFueXdoZXJlLXdpdGgtWmViZWMtQ2FyZC1hbmQtR2V0LVBhaWQtaW4tUmVhbC1UaW1lLXdpdGgtWmViZWMtUGF50gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 30 Mar 2023 07:00:00 GMT", "title": "The New Era of Paychecks: Spend Crypto Anywhere with Zebec Card and Get Paid in Real Time with Zebec Pay - Business Wire", "content": "NEW YORK--(BUSINESS WIRE)--Zebec, a pioneer in streaming finance, announced today the launch of its new Zebec payment card in the participating countries.* Zebec Card users can now easily convert their cryptocurrency payroll and savings into everyday purchases in fiat currencies.\n\nThe card works in conjunction with the Zebec App and a Solana-based wallet, enabling users to transfer their cryptocurrency to the card and pay with it anywhere in the world Mastercard is accepted. Users of the Zebec Card and holders of Zebec’s native utility token can earn up to 7 percent in rewards.\n\nSpend-as-you-Earn\n\nZebec Card is the world’s first card connected to a real-time payroll engine, allowing those enrolled in Zebec Pay to spend their crypto pay, as it’s earned – by the second.\n\n“We are on the mission to bridge the gap between traditional financial systems and the world of cryptocurrency and allow businesses, employees and consumers to transform how they are paid, how they buy products or services and how they invest,” said <PERSON>, the founder of Zebec. “Our new payment card makes it easy and convenient for users to access and use their cryptocurrency pay and holdings in daily lives.”\n\nLowest fees\n\nZebec Card carries no sign up, no annual or extra exchange fees and offers the lowest - typically below 1 percent - transaction fees. Residents of the UK and qualified countries throughout Europe* can apply for the card today on Zebec.io The launch in the US is planned for later this year, US waitlist is open.\n\n* Available in Portugal, Croatia, Bulgaria, Slovenia, Germany, France, Spain, Poland, Belgium, Greece, Finland, Latvia, Italy, Sweden, Liechtenstein, Denmark, Malta, Lithuania, Slovakia, Luxembourg, Estonia, Norway, Hungary, Czech Republic (Czechia), Romania, Ireland. Austria, Republic of Cyprus, Iceland, and Netherlands not included.\n\nAbout Zebec:\n\nZebec enables real-time and continuous streams of payments and financial transactions for payroll, investments and more. Founded in 2021, with investments by Circle, Coinbase, Solana Ventures, Breyer Capital, Republic, and Lightspeed Venture Partners among others, Zebec already services hundreds of companies, running thousands of continuous payment streams and bringing the blockchain to everyday lives.\n\nTo learn more, visit Zebec.io."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMifGh0dHBzOi8vd3d3LmJ1c2luZXNzd2lyZS5jb20vbmV3cy9ob21lLzIwMjMwMzMwMDA1MDA2L2VuL0luamVjdGl2ZS1MYXVuY2hlcy1GaXJzdC1FdmVyLVNvbGFuYS1Sb2xsdXAtZm9yLUNvc21vcy13aXRoLUVjbGlwc2XSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 30 Mar 2023 07:00:00 GMT", "title": "Injective Launches First-Ever <PERSON>up for Cosmos with Eclipse - Business Wire", "content": "NEW YORK--(BUSINESS WIRE)--Injective, the blockchain built for decentralized finance applications, today announced the first-ever Solana rollup launch for Cosmos, powered by Eclipse. The integration will bring the Solana development environment to the broader Cosmos ecosystem, further increasing growth and user adoption within the two ecosystems.\n\nEclipse, a customizable rollup provider, worked alongside Injective to deploy the first-ever Solana Sealevel Virtual Machine (SVM) within the Cosmos ecosystem. Injective’s recent integration with Wormhole made it the first chain in the Cosmos ecosystem to support Solana assets. Now its new SVM rollup will enable Solana smart contracts to be seamlessly deployed within the Cosmos universe, paving the path towards Solana applications to interact directly with IBC.\n\nDevelopers who are familiar with Solana tooling and language can now easily write and deploy their applications to the Cosmos ecosystem through Eclipse without needing to learn new programming languages or tooling. Despite the current market environment, Solana development remains resilient with activity up over 1000% year to date according to Alchemy.\n\nThis new integration will not only attract a new audience to the Solana ecosystem, but will also enable Cosmos users to utilize popular Solana dApps, resulting in increased Web3 adoption overall across the two ecosystems.\n\n“Injective is continuing to build the most dynamic ecosystem for DeFi,” said <PERSON>, co-founder and CEO of Injective Labs. “This new SVM rollup for the Cosmos IBC world will not only empower developers from Solana to deploy their dApps on Injective, but it will also create more opportunities for users to experience the best Web3 dApps in one integrated network.”\n\nMoving forward, the new SVM rollup will continue to evolve to reach even higher levels of security and scalability. The current plan is over time to integrate Cosmos’s upcoming interchain shared security model to ensure greater levels of sustainability within Cosmos. In addition, the data availability layer for the rollup plans to migrate onto the Celestia mainnet which would expand scalability properties.\n\n“We continue to be impressed by the pioneering work Injective has done to date around scalability and interoperability,” said Neel Somani, Founder of Eclipse Labs. “The SVM environment will drive a tremendous amount of developer activity and we are excited to bring it into the Cosmos ecosystem for the first time alongside Injective.”\n\nToday, Injective and Eclipse launch their private testnet, offering a limited number of spots exclusively to select Solana developers.\n\nAbout Injective\n\nInjective is a lightning fast interoperable layer one blockchain optimized for building the premier Web3 finance applications. Injective provides developers with powerful plug-and-play modules for creating unmatched dApps. INJ is the native token that powers Injective and its rapidly growing ecosystem. Injective is incubated by Binance and is backed by prominent investors such as Jump Crypto, Pantera and Mark Cuban.\n\nAbout Eclipse\n\nEclipse is a customizable rollup provider based in San Francisco, CA. Featuring a novel architecture that allows for decentralized applications to act as their own sovereign chain, Eclipse allows for developers to deploy customizable chains without the hassle of managing infrastructure and security. Eclipse’s public testnets will go live in 2023 in ecosystems including Celestia, Polygon, and EigenLayer."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMifGh0dHBzOi8vd3d3LmJ1c2luZXNzd2lyZS5jb20vbmV3cy9ob21lLzIwMjMwMzMwMDA1MDA2L2VuL0luamVjdGl2ZS1MYXVuY2hlcy1GaXJzdC1FdmVyLVNvbGFuYS1Sb2xsdXAtZm9yLUNvc21vcy13aXRoLUVjbGlwc2XSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 30 Mar 2023 07:00:00 GMT", "title": "Injective Launches First-Ever <PERSON>up for Cosmos with Eclipse - Business Wire", "content": "NEW YORK--(BUSINESS WIRE)--Injective, the blockchain built for decentralized finance applications, today announced the first-ever Solana rollup launch for Cosmos, powered by Eclipse. The integration will bring the Solana development environment to the broader Cosmos ecosystem, further increasing growth and user adoption within the two ecosystems.\n\nEclipse, a customizable rollup provider, worked alongside Injective to deploy the first-ever Solana Sealevel Virtual Machine (SVM) within the Cosmos ecosystem. Injective’s recent integration with Wormhole made it the first chain in the Cosmos ecosystem to support Solana assets. Now its new SVM rollup will enable Solana smart contracts to be seamlessly deployed within the Cosmos universe, paving the path towards Solana applications to interact directly with IBC.\n\nDevelopers who are familiar with Solana tooling and language can now easily write and deploy their applications to the Cosmos ecosystem through Eclipse without needing to learn new programming languages or tooling. Despite the current market environment, Solana development remains resilient with activity up over 1000% year to date according to Alchemy.\n\nThis new integration will not only attract a new audience to the Solana ecosystem, but will also enable Cosmos users to utilize popular Solana dApps, resulting in increased Web3 adoption overall across the two ecosystems.\n\n“Injective is continuing to build the most dynamic ecosystem for DeFi,” said <PERSON>, co-founder and CEO of Injective Labs. “This new SVM rollup for the Cosmos IBC world will not only empower developers from Solana to deploy their dApps on Injective, but it will also create more opportunities for users to experience the best Web3 dApps in one integrated network.”\n\nMoving forward, the new SVM rollup will continue to evolve to reach even higher levels of security and scalability. The current plan is over time to integrate Cosmos’s upcoming interchain shared security model to ensure greater levels of sustainability within Cosmos. In addition, the data availability layer for the rollup plans to migrate onto the Celestia mainnet which would expand scalability properties.\n\n“We continue to be impressed by the pioneering work Injective has done to date around scalability and interoperability,” said Neel Somani, Founder of Eclipse Labs. “The SVM environment will drive a tremendous amount of developer activity and we are excited to bring it into the Cosmos ecosystem for the first time alongside Injective.”\n\nToday, Injective and Eclipse launch their private testnet, offering a limited number of spots exclusively to select Solana developers.\n\nAbout Injective\n\nInjective is a lightning fast interoperable layer one blockchain optimized for building the premier Web3 finance applications. Injective provides developers with powerful plug-and-play modules for creating unmatched dApps. INJ is the native token that powers Injective and its rapidly growing ecosystem. Injective is incubated by Binance and is backed by prominent investors such as Jump Crypto, Pantera and Mark Cuban.\n\nAbout Eclipse\n\nEclipse is a customizable rollup provider based in San Francisco, CA. Featuring a novel architecture that allows for decentralized applications to act as their own sovereign chain, Eclipse allows for developers to deploy customizable chains without the hassle of managing infrastructure and security. Eclipse’s public testnets will go live in 2023 in ecosystems including Celestia, Polygon, and EigenLayer."}]