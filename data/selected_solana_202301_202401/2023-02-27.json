[{"id": 2, "url": "https://news.google.com/rss/articles/CBMiWGh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL2hlcmVzLXdoeS10aGUtcmVjZW50LXNvbGFuYS1vdXRhZ2UtdG9vay1hbG1vc3QtYS1kYXktdG8tcmVzb2x2ZS_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Feb 2023 08:00:00 GMT", "title": "Here's why the recent Solana outage took almost a day to resolve - CryptoSlate", "content": "Twitter user @DBCrypt0 explained why Solana was down for almost 20 hours over the weekend.\n\nSolana’s uptime status showed the network suffered an outage that lasted 18 hours and 50 minutes on Feb. 25 — the first interruption in 2023.\n\nSolana has a history of network outages, having endured 11 major, and 3 minor, outages in 2022. The downtime outages ranged between 1 hour 15 minutes and 17 hours 7 minutes during this period. The most recent outage was the longest in over a year.\n\n@DBCrypt0 said outages occur due to “a massive design flaw” that bogs the system down. He also explained that validator communications shift to Discord during an outage, contributing to potentially long downtimes.\n\nSolana’s design flaw\n\nSolana adopts an on-chain consensus model, meaning network transactions consist of consensus communication between validators and the transactions themselves — such as token transfers and minting. This inflates the transaction volume, said @DBCrypt0.\n\nThe chart below shows a snapshot of the network’s transactions. The pink portion of the bar represents actual transactions, while the light blue refers to the validator verification communications. @DBCrypt0 commented that it was “crazy” validator messages make up 90%-95% of transactions.\n\n“So when #Solana mentions they are doing 4K TPS just know less than 10% are ACTUAL transactions on the network.”\n\nAs the majority of transaction volume is made up of validators’ messages, this “bog[s] down the system.” And when the network goes down, validators cannot speak to one another, said @DBCrypt0.\n\n\n\nIn such instances, validators turn to <PERSON>rd to decide what to do. The problem is that two-thirds of validators must consent to any proposed action before it can happen, and some may be offline and unaware of an outage.\n\n“They then need 66% of validators I believe to agree on a solution to get back up.”\n\nHedera called out\n\nSolana “creates insane amounts of data for full nodes” by operating an on-chain consensus model, @DBCrypt0 said.\n\nHe added that by bloating the history with unnecessary validator messages, “a data center” is required to operate a full Solana node.\n\nIn rounding off, @DBCrypt0 pointed out that Hedera also runs an on-chain consensus model and suffers from the same inherent bloat flaw as Solana.\n\n“Sorry to break it to all the #HBARbarians but the majority of tx are unnecessary Just as they are on #Solana $HBAR only does around 3-5 TPS on average.”\n\nOn Feb. 25, SOL suffered a 9% swing to the downside but recovered by closing the next daily candle above the previous day’s opening price — indicating the market accepting Solana outages as expected behavior.\n\nA Hedera representative contacted CryptoSlate post publication to clarify that the network does not operate an on-chain consensus mechanism. Instead, the Hedera Consensus Service \"creates decentralized, auditable logs of immutable and timestamped events\" to track and log data."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vZGVjcnlwdC5jby8xMjIyODQvc29sYW5hLXdlZWtlbmQtb3V0YWdl0gE1aHR0cHM6Ly9kZWNyeXB0LmNvLzEyMjI4NC9zb2xhbmEtd2Vla2VuZC1vdXRhZ2U_YW1wPTE?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Feb 2023 08:00:00 GMT", "title": "Solana Foundation Still Doesn’t Know What Caused Weekend Network Outage - Decrypt", "content": "Decrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nTwo days after a major outage, the Solana Foundation says it’s still investigating why the Solana network went down for almost 20 hours following a network upgrade.\n\n“The cause of this is still unknown and under active investigation,” the Foundation wrote in a blog post published on Sunday night. A Solana Foundation spokesperson told Decrypt in an email on Monday that there are still no updates to the information that’s already been shared in its blog.\n\nThat’s a big blow for a network that’s become a major player in the industry in just two years.\n\nAD\n\nAD\n\nSolana is the ninth-largest blockchain by the total value of assets on the network, $551 million at the time of writing, according to CoinMarketCap. It launched in 2020 as a faster, cheaper alternative to Ethereum. Since then, it’s become home to the second-largest NFT market in the industry, having seen $2.6 million in sales in the past day alone, according to CryptoSlam.\n\nIt’s also home to a small, yet growing DeFi ecosystem—the kinds of tools that enable non-custodial trading, lending, and borrowing, all done on-chain and without third-party intermediaries. The DeFi community on Solana recently took a big hit when crypto exchange FTX filed for bankruptcy and the team behind Serum, a decentralized exchange, had to scramble to shutdown the project founded by <PERSON>. As of this writing, DeFi protocols on Solana account for $108 million in total value locked, according to DeFi Llama.\n\nDuring the outage, which started just before 6 a.m. UTC on Saturday, the network was unable to process user transactions. This means all on-chain activity, including NFT and DeFi trading, came to a screeching halt. After engineers recommended restarting the network, validators had to downgrade to an earlier version of software used to run nodes. They restarted the network by 2 a.m. UTC on Sunday.\n\nOn Saturday, the price of Solana’s token, SOL, took a hit on the news that the network was experiencing an outage. It started the day trading at $23.03, but fell 6% to $21.71 before the outage ended on Sunday morning, according to CoinGecko. By Monday afternoon, SOL had almost made up the outage-induced losses and was trading at $22.46.\n\nBut backlash on Twitter was fiery and persistent through the weekend.\n\nAD\n\nAD\n\n“DeFi doesn’t work on a chain that goes down, no matter how low the fees are,” wrote a user who goes by 0xShitTrader on Twitter and says they’re an office manager for Ellipsis Labs.\n\nThe company has been building Phoenix, a decentralized limit order book, on Solana and has previously said it was attracted to building on the network because of its high throughput and low transaction fees.\n\nThe outage also brought out some heavy criticism from Paul Brody, who heads up blockchain initiatives at global accounting firm EY.\n\n“Solana is like a lifestyle-friendly blockchain because you can have nights and weekends off,” Brody wrote on Twitter Saturday. “When can we all just admit that Solana is a joke. We’re so far beyond farce here. How can you [build] mission critical infrastructure on this nonsense,” he continued in another tweet.\n\nSolana co-founder Anatoly Yakovenko said on an episode of Decrypt’s gm podcast last year that outages are “the biggest challenge for us, and the number one priority.” At the time, he said he saw a new validator client, Firedancer, as a “long-term fix.”\n\nThe client has a built-in fail-safe that switches validators to “vote-only” mode in the event of an outage. That means the network prioritizes voting transactions, which are needed to restart the network, over regular user transactions.\n\nSaturday’s Solana outage is the latest in a string of issues the Solana network has faced. From September 30 until October 1, the network saw degraded performance that turned into a 7-hour outage.\n\nThe Saturday outage came on the heels of unrelated news about Solana Spaces, two “IRL” stores in New York and Miami, that opened in July and are now shutting down.\n\nAD\n\nAD\n\nVibhu Norby, CEO and founder of Solana Spaces, announced the news last week on Twitter, saying that the initiative would “pivot our Solana onboarding efforts into digital products like DRiP, our free NFT product with more than 100k sign-ups.”\n\nOf course, free NFT drops only work as an onboarding strategy if the chain stays online."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtZm91bmRhdGlvbi1zdGlsbC1kb2Vzbi10LTIwMzU0OTY4Ni5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Feb 2023 08:00:00 GMT", "title": "Solana Foundation Still Doesn't Know What Caused Weekend Network Outage - Yahoo Finance", "content": "Two days after a major outage, the Solana Foundation says it’s still investigating why the Solana network went down for almost 20 hours following a network upgrade.\n\n“The cause of this is still unknown and under active investigation,” the Foundation wrote in a blog post published on Sunday night. A Solana Foundation spokesperson told Decrypt in an email on Monday that there are still no updates to the information that’s already been shared in its blog.\n\nThat’s a big blow for a network that’s become a major player in the industry in just two years.\n\nSolana is the ninth-largest blockchain by the total value of assets on the network, $551 million at the time of writing, according to CoinMarketCap. It launched in 2020 as a faster, cheaper alternative to Ethereum. Since then, it’s become home to the second-largest NFT market in the industry, having seen $2.6 million in sales in the past day alone, according to CryptoSlam.\n\nIt’s also home to a small, yet growing DeFi ecosystem—the kinds of tools that enable non-custodial trading, lending, and borrowing, all done on-chain and without third-party intermediaries. The DeFi community on Solana recently took a big hit when crypto exchange FTX filed for bankruptcy and the team behind Serum, a decentralized exchange, had to scramble to shutdown the project founded by <PERSON>. As of this writing, DeFi protocols on Solana account for $108 million in total value locked, according to DeFi Llama.\n\nDuring the outage, which started just before 6 a.m. UTC on Saturday, the network was unable to process user transactions. This means all on-chain activity, including NFT and DeFi trading, came to a screeching halt. After engineers recommended restarting the network, validators had to downgrade to an earlier version of software used to run nodes. They restarted the network by 2 a.m. UTC on Sunday.\n\nOn Saturday, the price of Solana’s token, SOL, took a hit on the news that the network was experiencing an outage. It started the day trading at $23.03, but fell 6% to $21.71 before the outage ended on Sunday morning, according to CoinGecko. By Monday afternoon, SOL had almost made up the outage-induced losses and was trading at $22.46.\n\nSolana Co-Founder Says 'Long-Term Fix' to Network Outages Is in the Works\n\nBut backlash on Twitter was fiery and persistent through the weekend.\n\n“DeFi doesn’t work on a chain that goes down, no matter how low the fees are,” wrote a user who goes by 0xShitTrader on Twitter and says they’re an office manager for Ellipsis Labs.\n\nThe company has been building Phoenix, a decentralized limit order book, on Solana and has previously said it was attracted to building on the network because of its high throughput and low transaction fees.\n\nStory continues\n\nThe outage also brought out some heavy criticism from Paul Brody, who heads up blockchain initiatives at global accounting firm EY.\n\n“Solana is like a lifestyle-friendly blockchain because you can have nights and weekends off,” Brody wrote on Twitter Saturday. “When can we all just admit that Solana is a joke. We’re so far beyond farce here. How can you [build] mission critical infrastructure on this nonsense,” he continued in another tweet.\n\nSolana co-founder Anatoly Yakovenko said on an episode of Decrypt’s gm podcast last year that outages are “the biggest challenge for us, and the number one priority.” At the time, he said he saw a new validator client, Firedancer, as a “long-term fix.”\n\nThe client has a built-in fail-safe that switches validators to “vote-only” mode in the event of an outage. That means the network prioritizes voting transactions, which are needed to restart the network, over regular user transactions.\n\nSaturday’s Solana outage is the latest in a string of issues the Solana network has faced. From September 30 until October 1, the network saw degraded performance that turned into a 7-hour outage.\n\nSolana Spaces Closing Stores in NYC and Miami\n\nThe Saturday outage came on the heels of unrelated news about Solana Spaces, two “IRL” stores in New York and Miami, that opened in July and are now shutting down.\n\nVibhu Norby, CEO and founder of Solana Spaces, announced the news last week on Twitter, saying that the initiative would “pivot our Solana onboarding efforts into digital products like DRiP, our free NFT product with more than 100k sign-ups.”\n\nOf course, free NFT drops only work as an onboarding strategy if the chain stays online."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xhbmEtZm91bmRhdGlvbi1zdGlsbC1kb2Vzbi10LTIwMzU0OTY4Ni5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Feb 2023 08:00:00 GMT", "title": "Solana Foundation Still Doesn't Know What Caused Weekend Network Outage - Yahoo Finance", "content": "Two days after a major outage, the Solana Foundation says it’s still investigating why the Solana network went down for almost 20 hours following a network upgrade.\n\n“The cause of this is still unknown and under active investigation,” the Foundation wrote in a blog post published on Sunday night. A Solana Foundation spokesperson told Decrypt in an email on Monday that there are still no updates to the information that’s already been shared in its blog.\n\nThat’s a big blow for a network that’s become a major player in the industry in just two years.\n\nSolana is the ninth-largest blockchain by the total value of assets on the network, $551 million at the time of writing, according to CoinMarketCap. It launched in 2020 as a faster, cheaper alternative to Ethereum. Since then, it’s become home to the second-largest NFT market in the industry, having seen $2.6 million in sales in the past day alone, according to CryptoSlam.\n\nIt’s also home to a small, yet growing DeFi ecosystem—the kinds of tools that enable non-custodial trading, lending, and borrowing, all done on-chain and without third-party intermediaries. The DeFi community on Solana recently took a big hit when crypto exchange FTX filed for bankruptcy and the team behind Serum, a decentralized exchange, had to scramble to shutdown the project founded by <PERSON>. As of this writing, DeFi protocols on Solana account for $108 million in total value locked, according to DeFi Llama.\n\nDuring the outage, which started just before 6 a.m. UTC on Saturday, the network was unable to process user transactions. This means all on-chain activity, including NFT and DeFi trading, came to a screeching halt. After engineers recommended restarting the network, validators had to downgrade to an earlier version of software used to run nodes. They restarted the network by 2 a.m. UTC on Sunday.\n\nOn Saturday, the price of Solana’s token, SOL, took a hit on the news that the network was experiencing an outage. It started the day trading at $23.03, but fell 6% to $21.71 before the outage ended on Sunday morning, according to CoinGecko. By Monday afternoon, SOL had almost made up the outage-induced losses and was trading at $22.46.\n\nSolana Co-Founder Says 'Long-Term Fix' to Network Outages Is in the Works\n\nBut backlash on Twitter was fiery and persistent through the weekend.\n\n“DeFi doesn’t work on a chain that goes down, no matter how low the fees are,” wrote a user who goes by 0xShitTrader on Twitter and says they’re an office manager for Ellipsis Labs.\n\nThe company has been building Phoenix, a decentralized limit order book, on Solana and has previously said it was attracted to building on the network because of its high throughput and low transaction fees.\n\nStory continues\n\nThe outage also brought out some heavy criticism from Paul Brody, who heads up blockchain initiatives at global accounting firm EY.\n\n“Solana is like a lifestyle-friendly blockchain because you can have nights and weekends off,” Brody wrote on Twitter Saturday. “When can we all just admit that Solana is a joke. We’re so far beyond farce here. How can you [build] mission critical infrastructure on this nonsense,” he continued in another tweet.\n\nSolana co-founder Anatoly Yakovenko said on an episode of Decrypt’s gm podcast last year that outages are “the biggest challenge for us, and the number one priority.” At the time, he said he saw a new validator client, Firedancer, as a “long-term fix.”\n\nThe client has a built-in fail-safe that switches validators to “vote-only” mode in the event of an outage. That means the network prioritizes voting transactions, which are needed to restart the network, over regular user transactions.\n\nSaturday’s Solana outage is the latest in a string of issues the Solana network has faced. From September 30 until October 1, the network saw degraded performance that turned into a 7-hour outage.\n\nSolana Spaces Closing Stores in NYC and Miami\n\nThe Saturday outage came on the heels of unrelated news about Solana Spaces, two “IRL” stores in New York and Miami, that opened in July and are now shutting down.\n\nVibhu Norby, CEO and founder of Solana Spaces, announced the news last week on Twitter, saying that the initiative would “pivot our Solana onboarding efforts into digital products like DRiP, our free NFT product with more than 100k sign-ups.”\n\nOf course, free NFT drops only work as an onboarding strategy if the chain stays online."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMifGh0dHBzOi8vd3d3LmJ1c2luZXNzd2lyZS5jb20vbmV3cy9ob21lLzIwMjMwMjI3MDA1NjM1L2VuL0VjbGlwc2UtVG8tTGF1bmNoLUZpcnN0LVNvbGFuYS1WaXJ0dWFsLU1hY2hpbmUtTGF5ZXItMi1XaXRoLVBvbHlnb27SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 27 Feb 2023 08:00:00 GMT", "title": "Eclipse To Launch First Solana Virtual Machine Layer 2 With Polygon - Business Wire", "content": "SAN FRANCISCO--(BUSINESS WIRE)--Eclipse is launching with Polygon, the leading Ethereum scaling protocol, to launch Polygon SVM, the first Solana-equivalent scaling solution. Polygon SVM is a Layer 2 blockchain capable of running all smart contracts and tooling compatible with Solana.\n\nEclipse, a Polygon Ventures portfolio company and recipient of a Solana Foundation grant, will deploy the Sealevel virtual machine (SVM) Layer 2 blockchain for Polygon SVM. With the launch, dApps built for the Solana blockchain can now easily migrate or go multichain to Polygon SVM, providing users with a faster and more efficient experience.\n\nNew Use Cases and Higher Volume for Polygon Transactions\n\nThe Solana-equivalent Polygon SVM will offer superior speed for developers building games or high throughput DeFi applications such as central limit order books, enabling new use cases and traffic for the Polygon ecosystem.\n\n\"We are thrilled to see Eclipse launch Polygon SVM on the Polygon Network, which will provide developers with a powerful new blockchain infrastructure,\" <PERSON><PERSON><PERSON><PERSON>, VP of Games and Platform at Polygon Labs. \"Our vision has always been to see development of novel modular blockchain architectures, and Polygon SVM is the latest addition to the ecosystem of cutting-edge scaling solutions.\"\n\nBenefits of the Polygon SVM\n\nDevelopers can now choose the SVM on Polygon\n\nLeverages the security of the existing Polygon network while running the new blockchain\n\nFaster and more efficient experience\n\n<PERSON> is a customizable Layer 2 (rollup) provider that allows blockchain infrastructure developers to choose between different virtual machines, including the Sealevel virtual machine. Its rollup technology enables developers to run a new blockchain while leveraging the security of existing networks such as Polygon.\n\n\"We're excited to build on Polygon and develop superior scaling solutions,\" said Neel Somani, CEO of Eclipse. \"Polygon SVM is a first-of-its-kind collaboration between the Polygon and Solana communities.\"\n\nPolygon Labs previously acquired the Hermez Network (now Polygon Hermez) to develop the first EVM-equivalent zero-knowledge rollup. Polygon Labs later acquired the Ethereum scaling startup Mir. Polygon Avail, a blockchain that is focused on data availability (ordering and recording blockchain transactions), will provide security for Polygon's Eclipse chain.\n\nLast year, Eclipse announced its $15 million fundraise led by Polychain and Tribe Capital. Since then, the Eclipse team has developed a zero-knowledge Sealevel virtual machine proof-of-concept. The Eclipse developer documentation describes the customizations that it supports. Over 100 projects have expressed interest in deploying to Eclipse chains.\n\nThe testnet for Polygon SVM is slated for release in late Q1 2023, with the mainnet launch to follow in the summer.\n\nNotes to the Editor\n\nLayer 2 refers to blockchains built on top of a base blockchain layer which is known as Layer 1. Virtual machines such as the Ethereum virtual machine (EVM) or Sealevel virtual machine (SVM) execute smart contracts. Modular blockchains share infrastructure with other blockchains to perform one of their critical functions for operation. Zero-knowledge proofs or zk-rollups use advanced cryptography, and this cryptography validates that computation is done in the correct way following certain rules.\n\nAbout Polygon Labs\n\nPolygon Labs develops Ethereum scaling solutions for Polygon protocols. Polygon Labs engages with other ecosystem developers to help make available scalable, affordable, secure and sustainable blockchain infrastructure for Web3. Polygon Labs has initially developed a growing suite of protocols for developers to gain easy access to major scaling solutions, including layer 2s (zero-knowledge rollups and optimistic rollups), sidechains, hybrid chains, app-specific chains, enterprise chains, and data availability protocols. Scaling solutions that Polygon Labs initially developed have seen widespread adoption with tens of thousands of decentralized apps, unique addresses exceeding 218 million, over 1.15 million smart contracts created and 2.42 billion total transactions processed since inception. The existing Polygon network is home for some of the biggest Web3 projects, such as Aave, Uniswap, and OpenSea, and well-known enterprises, including Robinhood, Stripe and Adobe. Polygon Labs is carbon neutral with the goal of leading Web3 in becoming carbon negative.\n\nIf you're an Ethereum Developer, you're already a Polygon developer! Leverage Polygon’s fast and secure txns for dApps you develop, get started here.\n\nAbout Eclipse\n\nEclipse, a customizable rollup provider based in San Francisco, CA was founded in 2022 by Neel Somani. Designed for developers building some of blockchain's most unique use cases, Eclipse challenges the current constraints and limitations presented by existing blockchain technologies. Featuring a novel architecture that allows for decentralized applications to act as their own sovereign chain, Eclipse enables developers to deploy customizable chains without the hassle of managing infrastructure and security. Developers who are interested in Eclipse's technology can sign up for the mailing list on Eclipse's website and follow Eclipse's Twitter."}]