[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvc29sYW5hLXZtLWRldmVsb3BlcnMtbWFraW5nLXByb2dyZXNzLW9uLW1vdmUtbGFuZ3VhZ2UtaW50ZWdyYXRpb27SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 28 Jan 2023 08:00:00 GMT", "title": "Solana VM Developers Making Progress on Move Language Integration - BSC NEWS", "content": "SOL\n\nSolana devs are working on integrating the Move programming language into its library of compatible codes. Could it help Solana capture some of Aptos' mojo?\n\nMove Language Powers Aptos, Sui, (formerly) Diem\n\nThe hottest coin in crypto right now is arguably Aptos, whose claim to fame is being the first Layer-1 blockchain based on Facebook’s Move programming language and scrapped stablecoin project Diem. Meanwhile, developers on Solana recently reported success in integrating Move into the relatively old-school blockchain.\n\nWould the addition of Move into Solana’s library of compatible codes provide a boost to the beleaguered but still highly active ecosystem?\n\n😍 @solanalabs' VM team is building a Move compiler for Solana.\n\nYesterday, devs were able to compile and link Move code into a Solana VM binary. pic.twitter.com/AwupcshIR6 — <PERSON> (@fd_ripatel) January 27, 2023\n\nOn Jan. 27, software developer <PERSON> tweeted that devs from Solana Labs Virtual Machine team were able to compile and link Move code into a Solana VM binary file.\n\nThe progress is evidence that Solana devs have been building throughout rocky economic conditions, at least since early September 2022.\n\nThe goal is to listen to what the devs want and give it to them. Currently Solana smart contracts can be written in Rust, C, Python and Move once it’s done. Claiming a single language is better is silly. Languages aren’t a moat, enable all of them if you can — <PERSON> (@therealchaseeb) September 3, 2022\n\nWhat is Solana:\n\nSolana is a public, open-source blockchain that allows for smart contracts, non-fungible tokens (NFTs), and various decentralized applications (dApps). The SOL token, which is native to Solana's blockchain, provides network security through staking as well as a means of transferring value.\n\nLearn more about Solana:\n\nWebsite | Twitter | Docs | Github | Discord |\n\nWhat is Aptos:\n\nAptos is a new, independent project focused on delivering the safest and most production-ready Layer 1 blockchain in the world. The team includes the original creators, researchers, designers, and builders of Diem, the blockchain that was first built to serve this purpose.\n\nWhere to find Aptos Labs:\n\nWebsite | Twitter | Telegram | Medium | Discord |"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiW2h0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3Qvc29sYW5hLXZtLWRldmVsb3BlcnMtbWFraW5nLXByb2dyZXNzLW9uLW1vdmUtbGFuZ3VhZ2UtaW50ZWdyYXRpb27SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 28 Jan 2023 08:00:00 GMT", "title": "Solana VM Developers Making Progress on Move Language Integration - BSC NEWS", "content": "SOL\n\nSolana devs are working on integrating the Move programming language into its library of compatible codes. Could it help Solana capture some of Aptos' mojo?\n\nMove Language Powers Aptos, Sui, (formerly) Diem\n\nThe hottest coin in crypto right now is arguably Aptos, whose claim to fame is being the first Layer-1 blockchain based on Facebook’s Move programming language and scrapped stablecoin project Diem. Meanwhile, developers on Solana recently reported success in integrating Move into the relatively old-school blockchain.\n\nWould the addition of Move into Solana’s library of compatible codes provide a boost to the beleaguered but still highly active ecosystem?\n\n😍 @solanalabs' VM team is building a Move compiler for Solana.\n\nYesterday, devs were able to compile and link Move code into a Solana VM binary. pic.twitter.com/AwupcshIR6 — <PERSON> (@fd_ripatel) January 27, 2023\n\nOn Jan. 27, software developer <PERSON> tweeted that devs from Solana Labs Virtual Machine team were able to compile and link Move code into a Solana VM binary file.\n\nThe progress is evidence that Solana devs have been building throughout rocky economic conditions, at least since early September 2022.\n\nThe goal is to listen to what the devs want and give it to them. Currently Solana smart contracts can be written in Rust, C, Python and Move once it’s done. Claiming a single language is better is silly. Languages aren’t a moat, enable all of them if you can — <PERSON> (@therealchaseeb) September 3, 2022\n\nWhat is Solana:\n\nSolana is a public, open-source blockchain that allows for smart contracts, non-fungible tokens (NFTs), and various decentralized applications (dApps). The SOL token, which is native to Solana's blockchain, provides network security through staking as well as a means of transferring value.\n\nLearn more about Solana:\n\nWebsite | Twitter | Docs | Github | Discord |\n\nWhat is Aptos:\n\nAptos is a new, independent project focused on delivering the safest and most production-ready Layer 1 blockchain in the world. The team includes the original creators, researchers, designers, and builders of Diem, the blockchain that was first built to serve this purpose.\n\nWhere to find Aptos Labs:\n\nWebsite | Twitter | Telegram | Medium | Discord |"}]