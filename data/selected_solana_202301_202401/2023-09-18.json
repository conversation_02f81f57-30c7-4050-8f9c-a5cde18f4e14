[{"id": 13, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vd3d3Lm9reC5jb20vbGVhcm4vb2t4LXBhcnRuZXJzLXdpdGgtc29sYW5hLW1vYmlsZS10by1iZS1hbW9uZy10aGUtZmlyc3QtZXhjaGFuZ2UtYXBwLW9uLXNhZ2HSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 18 Sep 2023 07:00:00 GMT", "title": "OKX Partners with Solana Mobile to be Among the First Exchange App on Saga Web3 Smartphone - OKX", "content": "Disclaimer\n\nThis article may cover content on products that are not available in your region. It is provided for general informational purposes only, no responsibility or liability is accepted for any errors of fact or omission expressed herein. It represents the personal views of the author(s) and it does not represent the views of OKX. It is not intended to provide advice of any kind, including but not limited to: (i) investment advice or an investment recommendation; (ii) an offer or solicitation to buy, sell, or hold digital assets, or (iii) financial, accounting, legal, or tax advice. Digital asset holdings, including stablecoins and NFTs, involve a high degree of risk, can fluctuate greatly, and can even become worthless. You should carefully consider whether trading or holding digital assets is suitable for you in light of your financial condition. Please consult your legal/tax/investment professional for questions about your specific circumstances. OKX Web3 features, including OKX Web3 Wallet and OKX NFT Marketplace, are subject to separate terms of service at www.okx.com.\n\n© 2023 OKX. This article may be reproduced or distributed in its entirety, or excerpts of 100 words or less of this article may be used, provided such use is non-commercial. Any reproduction or distribution of the entire article must also prominently state:\"This article is © 2023 OKX and is used with permission.\" Permitted excerpts must cite to the name of the article and include attribution, for example \"Article Name, [author name if applicable], © 2023 OKX.\" No derivative works or other uses of this article are permitted."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMijAFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wOS8xOC9leC1nb2xkbWFuLXNhY2hzLWV4ZWN1dGl2ZS1sYXlzLW91dC1idWxsLWNhc2UtZm9yLWV0aGVyZXVtLXJpdmFsLXNvbGFuYS1zYXlzLXNvbC1iZXN0LXN0b3J5LW91dHNpZGUtb2YtZXRoL9IBkAFodHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wOS8xOC9leC1nb2xkbWFuLXNhY2hzLWV4ZWN1dGl2ZS1sYXlzLW91dC1idWxsLWNhc2UtZm9yLWV0aGVyZXVtLXJpdmFsLXNvbGFuYS1zYXlzLXNvbC1iZXN0LXN0b3J5LW91dHNpZGUtb2YtZXRoL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 18 Sep 2023 07:00:00 GMT", "title": "Ex-Goldman Sachs Executive Lays Out Bull Case for Ethereum Rival Solana, Says SOL Best Story Outside of ETH - The Daily Hodl", "content": "Former Goldman Sachs executive <PERSON> says that Sol<PERSON> (SOL) is one of his top altcoin picks and is potentially the most promising project outside of Ethereum (ETH).\n\nIn a new interview with Altcoin Daily, the Real Vision founder says Solana co-founder <PERSON><PERSON><PERSON>, commonly referred to as <PERSON><PERSON>, is becoming an important thought leader in the crypto space and represents the Solana ecosystem nicely.\n\n“Too many people get too involved with what they like because they like it and they’re a part of that thing. But I just observe <PERSON><PERSON>… I see <PERSON><PERSON> as a thought leader. He’s accepting of others around him, he builds bridges and doesn’t destroy them.\n\nI see a very engaged developer community. And people are really, really passionate about building their ecosystem and building their technology, and there’s a lot of people who use it, and there’s a lot of public awareness of the chain.”\n\nRecently, payments giant Visa announced it is building new stablecoin settlement capabilities on Solana. According to <PERSON><PERSON>, the chances are slim that Visa isn’t at least somewhat on the right track.\n\nIf digital assets are destined for another market expansion in the next couple of years, which <PERSON><PERSON> believes, he says that Solana could be one of the safest bets.\n\n“So, why is Visa using Solana? Because it’s fast. It’s fast, and it does a good job.\n\nIt’s not Ethereum. It’s different from Ethereum and even the way <PERSON><PERSON> has been talking about, ‘Could SOL be a layer-2 on ETH?,’ or ‘How do these worlds integrate?’ It just shows that for Visa to make a wrong decision is not that easy because things can be bridged, things can be interoperable…\n\nI’ve just been observing that and the Solana space has not put a foot wrong for a long time now, just when everybody had maximum fear and hate over the space…\n\nFor me, it’s a great story. It’s still probably the best major story in the space outside of Ethereum because Ethereum is the big daddy. But we’re looking at the next stage down, where’s the next altcoin that has relative security, and you’re not taking a wild guess…”\n\nI\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on X Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any losses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/Rattanamanee Patpong"}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiZGh0dHBzOi8vd3d3LmNvaW50cmlidW5lLmNvbS9lbi9wb3VycXVvaS12aXNhLXBhcmllLXQtaWwtc3VyLXNvbGFuYS1wb3VyLWNvbnF1ZXJpci1sZS1tb25kZS1jcnlwdG8tMi_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 18 Sep 2023 07:00:00 GMT", "title": "Crypto: Solana, Visa's choice for sustainable growth - Cointribune EN", "content": "Mon 18 Sep 2023 ▪ 5 min of reading ▪ by Luc Jose <PERSON>\n\nVisa, the payment services giant, is making cryptocurrencies and the underlying blockchain technology a cornerstone of its growth strategy. In recent months, the company has been actively involved in this direction, with numerous initiatives related to cryptocurrencies. While Visa’s previous crypto-related activities showcased its enthusiasm for blockchain, they didn’t reveal much about the company’s long-term plans. In a recent media statement, Visa clarified its stance on this matter, asserting that Solana’s blockchain is the most relevant in the market to ensure its expansion in the crypto industry. In this article, we will delve into why Visa is banking on Solana’s blockchain.\n\nIn brief:\n\nVisa is betting on Solana’s blockchain for its growth, capitalizing on its high transaction capacity and speed of execution;\n\nSolana’s affordable and predictable transaction fees are a key factor driving Visa’s interest in the platform;\n\nWith 1,893 active validators and a robust node infrastructure, Solana offers the reliability and scalability that aligns with Visa’s requirements.\n\nVisa’s Attraction to Solana’s Operational Efficiency\n\nSolana’s blockchain stands out as one of the top performers in the crypto industry. Unlike many other blockchains, Solana distinguishes itself with its scalability and speed, attributes that have propelled the platform’s significance in the crypto and decentralized applications (dApps) ecosystem.\n\nThese advantages form the primary reason Visa would rely on Solana over similar infrastructures. Specifically, Solana boasts an average transaction execution capacity of 400 cryptocurrency transactions per second (TPS), which can surge to over 2,000 TPS during peak demand periods. This comes with an execution speed averaging between 500 to 600 milliseconds, significantly enhancing user experiences.\n\nThis transaction throughput is far from negligible. In fact, Solana demonstrates substantial transactional capacity compared to Bitcoin and Ethereum, which have average TPS of 7 and 12, respectively, relatively low volumes.\n\nFor an international platform like Visa, which can process more than 65,000 TPS, Solana evidently emerges as a viable blockchain, at least enough to “test and pilot payment use cases.” Furthermore, Solana’s smart contracts, also known as programs, can execute in parallel through a multithreaded approach.\n\nVisa is Drawn to Solana’s Accessible Transaction Fees\n\nThis aspect is likely the most compelling reason that motivated Visa to choose Solana’s blockchain. Indeed, Solana outshines its competitors when it comes to transaction fees. These fees typically amount to less than $0.001, making them affordable for stakeholders.\n\nHowever, Solana’s transaction fees are not just affordable; they are also predictable. Compared to the transaction costs imposed by Bitcoin and Ethereum, Solana’s fees are unlikely to fluctuate unpredictably.\n\nThis enhances the blockchain’s attractiveness, making it “an intriguing option for exploring efficiency gains and cost savings for existing payment operations” on Visa’s platform.\n\nIf Solana’s transaction fees are both affordable and predictable, it’s not by chance. This is achieved through localized fee markets introduced by the blockchain’s developers. This innovation, typical of blockchain technology, stems from its transactional capabilities. Its major advantage is alleviating network congestion, which is often the root cause of rising transaction costs that can hinder efficiency.\n\nLastly, the Scale of Solana’s Validators and Nodes\n\nAs of July 31, 2023, Solana’s blockchain had 1,893 active validators, which are entities responsible for producing and voting on blocks. The platform also had more than 925 Remote Procedure Call (RPC) nodes, which, while unable to create blocks, maintain a local record of transactions.\n\nThe abundant availability of active validators and nodes is a key advantage of Solana. It ensures the platform’s real-time operational capability, greatly satisfying users. In this regard, only Ethereum can rival Solana, as Ethereum also boasts a substantial number of independent validators.\n\nConclusion\n\nSolana’s blockchain possesses exclusive technological advantages, combining high throughput, affordability, and robustness, thanks to its numerous nodes and validators. This harmonious synergy makes it a scalable blockchain platform offering compelling payment solutions. This is why Visa has chosen to expand its stablecoin settlement pilot project to encompass transactions conducted on the Solana network. In doing so, the payment services behemoth aims to evaluate Solana’s capacity to meet the operational demands of modern enterprises. According to Visa, with its strengths, Solana is a promising candidate to address the evolving needs of modern finance.\n\nMaximize your Cointribune experience with our 'Read to Earn' program! Earn points for each article you read and gain access to exclusive rewards. Sign up now and start accruing benefits.\n\nClick here to join 'Read to Earn' and turn your passion for crypto into rewards!\n\nA A Lien copié"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMif2h0dHBzOi8vZm9ydHVuZS5jb20vMjAyMy8wOS8xOC9zb2xhbmEtbmV4dC1ncmVhdC1hbWVyaWNhbi1mb3VuZGVyLWFtZXJpY2EtY29uZ3Jlc3MtcmVndWxhdGUtY3J5cHRvLWxhd21ha2Vycy1hbmF0b2x5LXlha292ZW5rby_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 18 Sep 2023 07:00:00 GMT", "title": "<PERSON><PERSON> co-founder: 'To keep the next great American founder in America, Congress must regulate crypto. But first ... - Fortune", "content": "I was born under Soviet rule in modern-day Ukraine. We moved to America when I was 11. Even at that age, I was very aware of the differences between the place I grew up–where access to assets and information was controlled with an iron fist–and my new home–where opportunity was unbound.\n\nI wanted to become an engineer and build big, ambitious projects. So, I studied computer science and spent over a decade building global networks at Qualcomm. A few years ago, I took the plunge as an entrepreneur myself and helped launch a project called Solana, a blockchain built for global accessibility. I want everyone in the world to have equal access to an open, interoperable global network, one that no single person or entity can shut down.\n\nToday, a new generation of thousands of blockchain developers are diving into their own entrepreneurial journeys. Many of them are taking on ambitious projects, competing against today’s corporate giants. They’re building user-owned wireless networks, ridesharing networks, food delivery services, and social platforms to someday compete with Comcast, Google, Uber, and Facebook. Many are in the United States. Increasingly, many are not.\n\nI meet promising entrepreneurs every day who want to build the next great technological innovation in America but don’t know how to build a blockchain company in a compliant way. For typical startups, the first step is incorporating your company for less than $500 on LegalZoom. For blockchain companies, it means pouring precious amounts of time, energy, and often tens of thousands of dollars into legal fees trying to structure their businesses to operate in a compliant manner. It’s well-documented that there’s no viable path to reasonable regulatory certainty in the space. For young entrepreneurs, the absence of clear rules is terrifying. They see public, multi-billion dollar companies struggle to navigate the legal landscape, and wonder how their tiny project will survive.\n\nFaced with the choice of staying in America or building their dream, more founders are choosing to leave. In 2018, the U.S. was home to 42% of the world’s open-source blockchain developers, according to Electric Capital. By 2022, that figure dropped to 29%.\n\nAs with any new technology, there have been scams in the digital asset space, and we should do everything possible to eliminate them. But a well-functioning economy shouldn’t punish an entire industry for the actions of its worst elements. Many of us are here because we want to create real value–and we want American values at the foundation of the world’s most impactful companies. Imagine if Google had been founded in Russia, or Reddit had been founded in China. How different would the internet look today? For the U.S. to attract and retain the very best talent in the new digital landscape, we need a cogent regulatory framework that protects consumers and encourages entrepreneurship.\n\nIn addition to clarity being provided in the courts, two Congressional committees advanced key pieces of legislation in July that would create regulatory frameworks for digital assets and stablecoins on a bipartisan basis. This fall, the full House will have the opportunity to vote on these two bills.\n\nThe bills aren’t perfect. No legislation is. As a country and as an industry, we cannot let perfect be the enemy of the good. Congress must continue stewarding these efforts to protect American technological leadership, provide important market protections, and promote a free and open internet. I applaud the efforts of members from both parties to move these bills forward, and I hope legislators across both chambers will take these proposals seriously, work to improve them, and turn them into law.\n\nBeyond legislation, our government should be at the forefront of investing in blockchain research and development. Some of the most meaningful technologies on earth–GPS, rockets, and even the internet–were initially incubated by the U.S. government. European and Asian governments are already investing in blockchain. The European Commission even runs a digital ledger sandbox to identify potential private-public partnerships. We should do the same.\n\nPolicymakers need to experiment with the technology themselves. Ethics rules prohibit most government officials who regulate digital assets from using them.This makes it tough to craft good policy: Imagine trying to regulate social media without having ever opened Facebook!\n\nThere are creative solutions that give policymakers access to the technology. For example, the government could take advantage of crypto’s speed and cost-effectiveness to send humanitarian relief funds and launch decentralized communications networks in low-connectivity areas.\n\nThere are hundreds of ways that the U.S. government can encourage this new wave of the internet and support brilliant blockchain entrepreneurs. I welcome an open conversation with policymakers about web3, its potential, and yes, its pitfalls. Let’s keep builders building in America.\n\nAnatoly Yakovenko is a cofounder of Solana and the CEO of Solana Labs. The opinions expressed in Fortune.com commentary pieces are solely the views of their authors and do not necessarily reflect the opinions and beliefs of Fortune."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiXGh0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzIxNDk3L3NvbC1mYXVjZXQtaGVyZXMtaG93LXlvdS1jYW4tZ2V0LWZyZWUtc29sYW5hLW9uLXRlc3RuZXQv0gFfaHR0cDovL2FtcC5jb2luY29kZXguY29tL2FydGljbGUvMjE0OTcvc29sLWZhdWNldC1oZXJlcy1ob3cteW91LWNhbi1nZXQtZnJlZS1zb2xhbmEtb24tdGVzdG5ldC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 18 Sep 2023 07:00:00 GMT", "title": "SOL Faucet – Here’s How You Can Get Free Solana on Testnet - CoinCodex", "content": "A SOL faucet is a website that enables users to receive a small amount of Solana (SOL) tokens for free. Unlike mainnet SOL coins, Solana testnet faucet-issued SOL tokens do not possess real economic value and primarily serve as a tool used by developers to try out decentralized applications (dApps) and smart contracts before deploying them to the Solana mainnet.\n\nThis doesn't render the mainnet SOL tokens useless when making developments on the network, as they also play a major role when developing on Solana. Developers need the mainnet SOL tokens to deploy smart contracts, or programs on Solana. Meanwhile, the need for testnet SOL tokens comes into play earlier in the development process, when it's time for developers to test the programs out on testnet and devnet.\n\nWhat is a SOL faucet?\n\nA SOL faucet is a website that sends users testnet SOL coins for free. Sol Faucet is the go-to website for Solana developers who want to get test SOL tokens for the purpose of deploying smart contracts to the Solana Devnet and Testnet. However, several alternatives, like Thirdweb’s and Stakely’s Solana coin faucet services, also attract plenty of users.\n\nTo reiterate our previous point, users who intend to use Solana free faucets should note that these tools do not give out real SOL tokens but rather testnet versions. Hence, they can be leveraged on both the Solana Testnet and Devnet for public use. Moreso, test SOL tokens generated from the SOL faucet cannot be transferred to or used on Solana's mainnet. In addition to this, the tokens cannot be cashed out as they are meant for development purposes only. The tokens are solely used by blockchain developers and testers to test Solana programs and dApps.\n\nWhat is Solana Devnet and Testnet?\n\nSolana Devnet is a test network deployed by Solana developers for the trial phase of a new dApp. Solana Devnet functions as a sandbox for developers to test out their applications before deploying them to the mainnet. Meanwhile, only the test SOL tokens are required to execute transactions on the Devnet. This way, developers do not need to spend money to test their applications.\n\nSolana Testnet is very similar to Devnet. While the Devnet is recommended for developers to test their new applications, the Solana Testnet is primarily used by Solana Labs to test network upgrades. Basically, it is where the Solana core contributors stress test recent feature releases and bug fixes on a live cluster, particularly focused on network performance, stability and validator behavior. It also allows for the development of blockchain applications without the risk of losing funds. The testnet tokens can be obtained freely from various token-issuing services on the Solana faucet list.\n\nHow to get free Solana testnet coins?\n\nAs said earlier, the SOL Faucet does not give regular Solana tokens but testnet tokens. To get free Solana testnet coins from a SOL faucet, users must first complete a process that can be categorized into a couple of basic steps.\n\nStep 1 - Install the Solana Command Line Interface on your device\n\nTo start with, users are required to install the Solana Command Line Interface (CLI), one of the fundamental Solana developer tools on their device. This step must be completed before you can have Solana installed on your device.\n\nTo get the Solana CLI installer into your transient directory, you'll need to open your command prompt as an admin, then run the code displayed below:\n\ncurl\n\nhttps://release.solana.com/v1.10.32/solana-install-init-x86_64-pc-windows-msvc.exe\n\n--output\n\nC:\\solana-install-tmp\\solana-install-init.exe\n\n--create-dirs\n\nThe previous step will see you have the Solana installer on your device. Hence, you can proceed to install Solana itself on our Windows. You can achieve this by running this command on your device:\n\nC:\\solana-install-tmp\\solana-install-init.exe\n\nThis concludes the Solana installation process on your device. The successful installation of the Solana CLI will be displayed on your screen like the image below:\n\nMeanwhile, to confirm that the Solana CLI has been properly installed, you should open a new command line interface on your device, not as an admin, and run the code below:\n\nsolana -version\n\nStep 2 - Create a Solana wallet\n\nCreating a Solana wallet is essential as it is the tool that helps you to receive the free Solana faucet tokens. Note, there are multiple types of wallets featured on the network for people to use. This includes browser-based wallets, hardware wallets, paper wallets, or file system wallets. Let's learn how to use a paper wallet to claim free SOL faucet tokens.\n\nTo create a paper wallet from the command line and get your seed phrase, run this script:\n\nsolana-keygen new --no-outfile\n\nThis will launch you to a stage where you'll be asked for a passphrase that you will have to set, after which your wallet address and seed phrase will be displayed.\n\nYou can proceed to get your free SOL faucet tokens from any of the two types of Solana faucets, which include the command line Solana faucet website options.\n\nStep 3 - Request free SOL faucet tokens\n\nTo receive test SOL tokens using the command line, use the script below:\n\nsolana airdrop 2 <RECIPIENT_ACCOUNT_ADDRESS>\n\n--url https://api.devnet.solana.com\n\nAfter this, you should replace the recipient account address with the wallet address displayed for you earlier. This process will see you receive 2 SOL in your paper wallet. The image below will be displayed on your screen.\n\nTo confirm if the tokens are in your wallet, close that interface and open another one. Then run this command.\n\nsolana balance <ACCOUNT_ADDRESS>\n\n--url https://api.devnet.solana.com\n\nThe display above is what you'll see after completing the step.\n\nAlternative ways to get testnet and devnet SOL\n\nAnother way to get devnet SOL – which is more suitable for users that prefer graphical user interfaces over command lines – is to request SOL devnet or testnet tokens using a Solana faucet website.\n\nSol Faucet\n\nSol Faucet allows users to request the number of SOL tokens to receive in a devnet/testnet-supporting wallet. Keep in mind that the max amount of tokens you can on testnet is 1 SOL, while up to 10 SOL can be airdropped into a devnet Solana blockchain address.\n\nThirdWeb SOL faucet for Solana devnet\n\nThirdWeb provides a faucet where you can get devnet SOL tokens for free. However, please keep in mind that the faucet only distributes 1 devnet SOL token at a time.\n\nAll you need to do in order to use the ThirdWeb SOL faucet is to input the address you're using on the Solana devnet and click \"Request Funds\". After that, the 1 SOL should arrive in your Solana devnet wallet almost immediately.\n\nQuickNode Solana faucet\n\nBlockchain infrastructure company QuickNode provides a Solana devnet and testnet faucet where you can get devnet SOL coins for free every 12 hours. You can connect to the QuickNode faucet using your Phantom wallet. If you want to increase the amount of SOL coins you get from this faucet, you can share a tweet mentioning the faucet and QuickNode will provide you with a 2x bonus.\n\nThe bottom line\n\nSOL faucets allow developers who desire to build decentralized applications on the Solana network to test their apps in a secure and risk-free environment. As you can see from our guide, getting free test SOL coins is a very straightforward process..\n\nAside from the SOL faucet, developers who intend to build on Binance can also get testnet BNB for free on the Binance Smart Chain (BSC) testnet."}]