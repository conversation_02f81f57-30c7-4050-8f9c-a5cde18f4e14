[{"id": 1, "url": "https://news.google.com/rss/articles/CBMijQFodHRwczovL3d3dy5kZWxtYXJ0aW1lcy5uZXQvbGlmZXN0eWxlL3N0b3J5LzIwMjMtMDQtMDYvbGVwb3J0LW1vbnRlc3Nvcmktc29sYW5hLWJlYWNoLXN0dWRlbnRzLWRvbmF0ZS1tb3JlLXRoYW4tMTIwLWJvb2tzLXRvLWNoaWxkcmVuLWluLW5lZWTSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 06 Apr 2023 07:00:00 GMT", "title": "LePort Montessori Solana Beach students donate more than 120 books to children in need - Del Mar Times", "content": "Students at LePort Montessori School Solana Beach in Del Mar recently donated more than 120 new books to the Pajama Program, a nonprofit organization that promotes and supports a comforting bedtime routine and healthy sleep for all children to help them thrive. Students held a book drive as part of a National Reading Month initiative to provide children in their community with access to books that they deserve.\n\nAfter the collection, students organized the donations for delivery to the organization, which then distributed the books to community partners that work directly with local children facing adversity. Through this initiative, students learned the importance of reading and helping those in need in their community."}, {"id": 9, "url": "https://news.google.com/rss/articles/CBMiVWh0dHBzOi8vdS50b2RheS92aXRhbGlrLWJ1dGVyaW5zLWRlYmF0ZS13aXRoLXNvbGFuYS1mb3VuZGVyLXJlc3VtZXMtYWZ0ZXItYWxtb3N0LXllYXLSAVlodHRwczovL3UudG9kYXkvdml0YWxpay1idXRlcmlucy1kZWJhdGUtd2l0aC1zb2xhbmEtZm91bmRlci1yZXN1bWVzLWFmdGVyLWFsbW9zdC15ZWFyP2FtcA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 06 Apr 2023 07:00:00 GMT", "title": "<PERSON><PERSON>'s Debate With <PERSON>ana Founder Resumes After Almost Year - U.Today", "content": "Solana Labs' co-founder <PERSON><PERSON><PERSON> has unexpectedly resumed a dialogue with Ethereum creator <PERSON><PERSON> on bridge security after almost a year. Then, at the end of May 2022, blockchain activists were discussing how to possibly detect and prevent attempts to cheat a system that allows funds to move between different networks by illegally using the same money multiple times.\n\nAdvertisement\n\nThe dialogue between <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> stalled then, but the two sides agreed that a social layer of bridge management could, in theory, solve any problems. However, both figures also acknowledged uncertainty about how secure such a system would be, especially during a 51% attack.\n\nBecause what if 51% attack — vitalik.eth (@VitalikButerin) May 31, 2022\n\n<PERSON><PERSON><PERSON> bridge\n\nSolana's founder puzzled over the solution for a year, he says, and may have finally found it. Thus, <PERSON><PERSON><PERSON> proposed a trust-minimized bridge between independent Layer 1 blockchains, a design that ensures that local users will be able to pull their bridged assets back into the local chain, even if the remote chain has an unfair majority, withholds data or conducts a 51% attack.\n\nA full description of <PERSON><PERSON><PERSON>'s bridge can be found in a tweet by <PERSON><PERSON>'s co-founder. The key points of the solution are to hold auctions when problems arise to decide who can fix them. Apart from that, people using the system will have to wait for confirmation before they can use their money. If something goes wrong, people will be able to submit a challenge to get access to their data.\n\n<PERSON><PERSON> has not yet responded to his colleague's proposal."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vZGFpbHlob2RsLmNvbS8yMDIzLzA0LzA2L2hlcmUtYXJlLXRocmVlLWJ1bGxpc2gtbWV0cmljcy1mb3ItZXRoZXJldW0tcml2YWwtc29sYW5hLXNvbC1hY2NvcmRpbmctdG8taW52ZXN0YW5zd2Vycy_SAX1odHRwczovL2RhaWx5aG9kbC5jb20vMjAyMy8wNC8wNi9oZXJlLWFyZS10aHJlZS1idWxsaXNoLW1ldHJpY3MtZm9yLWV0aGVyZXVtLXJpdmFsLXNvbGFuYS1zb2wtYWNjb3JkaW5nLXRvLWludmVzdGFuc3dlcnMvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 06 Apr 2023 07:00:00 GMT", "title": "Here Are Three Bullish Metrics for Ethereum Rival Solana (SOL), According to InvestAnswers - The Daily Hodl", "content": "A popular crypto analyst says there are three metrics that are looking bullish for Ethereum (ETH) competitor Solana (SOL) right now.\n\nIn a new YouTube video, the anonymous host of InvestAnswers tells his 443,000 YouTube subscribers that Solana registered 446,000 daily active users in early April, a spike in activity that placed the project above all other smart contract platforms.\n\nThe analyst also notes that Solana has seen its seven-day moving average of non-vote transactions spike to nearly 30 million, a level it hasn’t neared since last August.\n\nAdditionally, the InvestAnswers host says Solana has the second-highest number of commits since the inception of any smart contract platform, after only Ethereum, which has the most by far.\n\nExplains the analyst,\n\n“What are commits? … When a developer does a commit in software development, it is the change made to code repository by a developer typically tracked to a version control system like GitHub or Git, and [the number of commits] helps analyze work volume, team performance, identify trends, it also helps understand where the project is going, how much resource allocation there is to the project in development, and it is an essential metric for tracking progress and quality of a chain and of course any software development.”\n\nSOL is trading at $20.79 at time of writing. The 11th-ranked crypto asset by market cap is down more than 2% in the past 24 hours.\n\nI\n\nDon't Miss a Beat – Subscribe to get email alerts delivered directly to your inbox\n\nFollow us on X Facebook and Telegram\n\nDisclaimer: Opinions expressed at The Daily Hodl are not investment advice. Investors should do their due diligence before making any high-risk investments in Bitcoin, cryptocurrency or digital assets. Please be advised that your transfers and trades are at your own risk, and any losses you may incur are your responsibility. The Daily Hodl does not recommend the buying or selling of any cryptocurrencies or digital assets, nor is The Daily Hodl an investment advisor. Please note that The Daily Hodl participates in affiliate marketing.\n\nFeatured Image: Shutterstock/Rattanamanee Patpong/WhiteBarbie"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiOWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3NvbGFuYS1mb3VuZGF0aW9uLW5mdHMtc3RvcmFnZdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 06 Apr 2023 07:00:00 GMT", "title": "Storing Solana NFTs On Chain Just Got More Affordable - Blockworks", "content": "The Solana Foundation has teamed up with a handful of ecosystem partners to make it cheaper to store NFTs on the blockchain.\n\nIt costs roughly 1,200 SOL ( about $24,000) to store 1 million NFTs on Solana today, but state compression — a novel technology that uses Merkle trees — is set to drastically reduce the associated transaction costs to roughly four SOL (about $103).\n\nState compression started after developers at Solana Labs and Metaplex wanted to facilitate gasless experiences for users on the Solana blockchain, <PERSON>, a technical lead on the ecosystem engineering team at the Solana Foundation, told Blockworks in an exclusive interview.\n\nCompanies that could benefit from this type of technology include airdrop facilitators, as well as Web3 gaming developers who find it too expensive to build digital goods on a chain.\n\n<PERSON> in a separate statement described the technology as a “compression-friendly data structure” that “ allows developers to store a small bit of data on-chain and updates directly in the Solana ledger, cutting the data storage cost down dramatically while still using the security and decentralization of Solana’s base layer.”\n\nOn- vs. off-chain NFT storage\n\nNFTs that are hosted on-chain are given a unique identifier.\n\nOn Solana, when you purchase an NFT, you have to pay for every byte stored on-chain, <PERSON> said.\n\n“How compression works is that it takes a large set of data and summarizes it into what is called a fingerprint using a data structure called Merkle tree,” he said.\n\nRather than having a bunch of NFT data on the blockchain, only a condensed version of the data is hosted on-chain in that arrangement. The remainder can be accessed off-chain through Solana remote procedure call (RPC) providers. ​​\n\nIf any of the individual values that are stored off-chain change, the value stored on the blockchain will also change.\n\nIt’s like a receipt to prove that you purchased an item, Wong said. Your purchase may not physically be in your house or apartment. It may sit in a storage center. But your receipt verifies you as the product’s honor. And you will still be able to buy and sell it in a marketplace.\n\n“From a consumer’s perspective, nothing changes,” Wong said. “[It] shows up in your wallet, you can send it, transfer it, operate in smart contracts, put it on marketplaces. So, nothing changes there. We’re just storing it in a different way.”\n\nAlthough the technology has been available on Solana mainnet for a few months, there had not yet been any RPC providers which were able to facilitate these queries until now.\n\n“Now is the moment that the circle has been completed,” Wong said. “People can create and transfer these NFTs…so from a consumer standpoint, they can now see and touch and feel these things in a way they weren’t able to before.”\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiWmh0dHBzOi8vdG9rZW5pc3QuY29tL21pbnRpbmctY29zdHMtZm9yLXNvbGFuYS1uZnRzLXN1YnN0YW50aWFsbHktbG93ZXJlZC13aXRoLW5ldy1mZWF0dXJlL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 06 Apr 2023 07:00:00 GMT", "title": "Minting Costs for Solana NFTs Substantially Lowered with New Feature - The Tokenist", "content": "Neither the author, <PERSON>, nor this website, The Tokenist, provide financial advice. Please consult our website policy prior to making financial decisions.\n\nSolana and a group of its ecosystem partners rolled out new a new feature that makes storing non-fungible tokens (NFTs) on-chain much cheaper. The feature, known as state compression, reduces the cost of minting 1 million NFTs on Solana to just around $110.\n\nSolana Teams Up With Ecosystem Partners to Reduce Costs of Storing NFTs On-Chain\n\nSolana announced on Thursday the launch of ‘state compression,’ a new feature that makes storing NFTs on the blockchain significantly cheaper. According to the official post, with state compression, minting 1 million compressed NFTs on a blockchain costs roughly $110. Solana said the function could be used to store any data on-chain, though the first use of this technology is compressed NFTs.\n\n“Compressed NFTs are just like regular NFTs, only drastically cheaper — minting 100 million compressed NFTs costs about ◎50 to store on-chain, compared to ◎1.2mm for their uncompressed counterpart.” – Solana wrote in the announcement.\n\nState compression represents a cross-ecosystem effort built by Solana and Metaplex developers in collaboration with Remote Procedure Call (RPC) providers and indexers such as Triton, Helius, and SimpleHash. By taking advantage of the so-called Me<PERSON>le trees, this sophisticated technology is designed to reduce transaction costs dramatically. Without state compression, it costs around 1,200 SOL (roughly $24,000) to store 100,000 million NFTs on the Solana blockchain.\n\n<PERSON>, a technical lead on the ecosystem engineering team at the Solana Foundation, said state compression is a “compression-friendly data structure” that enables developers to keep “a small bit of data on-chain and updates directly in the Solana ledger, cutting the data storage cost down dramatically while still using the security and decentralization of Solana’s base layer.”\n\nJoin our Telegram group and never miss a breaking digital asset story.\n\nHow Does it Work?\n\nUnlike typical compression methods, which host a bunch of NFT data on-chain, the state compression feature allows only a smaller, concentrated version of data to be hosted on the blockchain using Merkle trees. The remaining portion of the data can be accessed off-chain via Solana RPC providers.\n\nIf the individual values kept off-chain change, the value stored on-chain would also change. To clarify how it works, Wong compared it to a receipt a buyer receives when they purchase an item. Although the product might not be near them, they own the receipt, which verifies them as the product’s honor.\n\nThe move came amid a slight resurgence in demand for NFTs after a severe crypto winter obliterated the digital collectibles market last year.\n\n\n\nFinance is changing. Learn how, with Five Minute Finance. A weekly newsletter that covers the big trends in FinTech and Decentralized Finance. Try it out (for free) Awesome You’ve subscribed. You’re well on your way to being in the know.\n\nDo you think other major blockchains will also implement state compression in the future? Let us know in the comments below."}]