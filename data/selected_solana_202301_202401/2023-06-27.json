[{"id": 1, "url": "https://news.google.com/rss/articles/CBMihAFodHRwczovL3d3dy5jb2luZGVzay5jb20vbWFya2V0cy8yMDIzLzA2LzI3L2FkYS1zb2wtdW5kZXJwZXJmb3JtLWFzLXRva2Vucy1hcmUtc2V0LXRvLWJlLWRlbGlzdGVkLWZyb20tcm9iaW5ob29kLWFtaWQtc2VjLWNyYWNrZG93bi_SAYgBaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL21hcmtldHMvMjAyMy8wNi8yNy9hZGEtc29sLXVuZGVycGVyZm9ybS1hcy10b2tlbnMtYXJlLXNldC10by1iZS1kZWxpc3RlZC1mcm9tLXJvYmluaG9vZC1hbWlkLXNlYy1jcmFja2Rvd24vYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 27 Jun 2023 07:00:00 GMT", "title": "ADA, SOL Underperform as Robinhood Gets Set to Delist Them Amid SEC Crackdown - CoinDesk", "content": "ADA and SOL were down about 2.3% and 1.8%, respectively, over the past 24 hours, according to CoinDesk data, while the broader CoinDesk Market Index (CMI) was up 0.6% over the same time period. Polygon (MATIC), the native cryptocurrency that powers the Polygon Network, was flat. Since Robinhood announced it was ending support for the three tokens on June 9, the prices for the three crypto assets have dropped more than 5% while the CMI is up about 9% over the same period."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMibmh0dHBzOi8vY3J5cHRvc2xhdGUuY29tL3JvYmluaG9vZC1sYXlzLW9mZi03LW9mLXdvcmtmb3JjZS1vbi1zYW1lLWRheS1hcy1jYXJkYW5vLXBvbHlnb24tYW5kLXNvbGFuYS1kZWxpc3Rpbmcv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 27 Jun 2023 07:00:00 GMT", "title": "Robinhood lays off 7% of workforce on same day as Cardano, Polygon, and Solana delisting - CryptoSlate", "content": "Robinhood is slashing about 7% of its full-time employees, marking the company’s third wave of layoffs in just over a year. The decision comes as the company faces dwindling customer trading activity and follows the controversial delisting of three major cryptocurrencies, according to the original report from The Wall Street Journal.\n\nRoughly 150 employees are impacted by the cut, the online brokerage firm said in an internal message. The move is part of an effort to “adjust to volumes and to better align team structures,” CFO <PERSON> stated in the memo.\n\nLast year, the company cut more than 1,000 jobs, leaving it with about 2,300 full-time employees by the end of 2022, according to its annual report. Robinhood’s spokesperson stated the firm is committed to “operational excellence in how we work together on an ongoing basis,” which may involve changes based on volume, workload, and organizational design.\n\nThe layoffs primarily impact roles in customer experience, platform shared services, customer trust and safety, and safety and productivity. The firm has faced increased employee departures and reported declines in job satisfaction after previous layoffs.\n\nThis latest move follows less than a week after Robinhood announced its acquisition of credit-card startup X1 in a $95 million cash deal, part of its ongoing strategy to expand beyond trading.\n\nUsers dropping off\n\nSimultaneously, the company is grappling with a significant drop in monthly active users. As of May, Robinhood had fewer than 11 million users, a substantial fall from the more than 21 million active users during the peak of the Covid-19 pandemic. The first quarter’s transaction-based revenue dropped 5% year over year and was more than halved from the first quarter of 2021.\n\nThe financial services company is also facing headwinds from recent actions in the cryptocurrency market. Amidst a crackdown by the U.S. Securities and Exchange Commission (SEC), Robinhood announced it would delist Cardano (ADA), Polygon (MATIC), and Solana (SOL) on June 27. The decision followed SEC lawsuits against Coinbase and Binance, which labeled these tokens as unregistered securities.\n\nThe delisting could exert further pressure on the firm’s crypto trading volumes, which reported a year-on-year decline of 30% in May. However, these issues are not unique to Robinhood, with a relatively calm crypto market leading to decreased trading volumes across the board.\n\nMentioned in this article"}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMijAFodHRwczovL3d3dy5kZWxtYXJ0aW1lcy5uZXQvbmV3cy9zdG9yeS8yMDIzLTA2LTI3L3NvbGFuYS1iZWFjaC1yZXNpZGVudC1iZWNvbWVzLWNoaWVmLXNjaWVuY2UtYWR2aXNvci1vZi1jYW5jZXItcmVzZWFyY2gtbm9ucHJvZml0LWN1cmVib3VuZNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 27 Jun 2023 07:00:00 GMT", "title": "Solana Beach resident becomes chief science advisor of cancer research nonprofit Curebound - Del Mar Times", "content": "Solana Beach resident <PERSON>, M.D., recently joined Curebound as its first chief science advisor.\n\n<PERSON> grew up in Toronto, went to medical school at the University of Toronto, and worked as a family physician before pursuing his interest in oncology. After 15 years on the faculty at the University of Chicago, he moved to UC San Diego about 10 years ago.\n\nIn a Q&A, <PERSON> discussed his new position at Curebound, a nonprofit that focuses on cancer research. Answers were lightly edited for clarity and conciseness.\n\nQ: How did your career lead you to Curebound?\n\n<PERSON>: At UC San Diego I was doing multiple things, including roles in the cancer center, in the division of hematology-oncology. And then I got involved with a couple of cancer-focused fundraising organizations. One of them was called the Immunotherapy Foundation and the other was called Pedal the Cause. Those two organizations merged to become Curebound. I was involved in that merger and peripherally advising the founders in terms of how they should structure their organization. I eventually became a member of the scientific advisory board. And then their CEO approached me because we had regular contact. In one of our conversations, she said that they’d soon be looking for a scientific director, and that thought stuck with me. Several months later I had another conversation with her and I said I’d be interested in doing that role.\n\nQ: What will your priorities be in your role with Curebound?\n\n<PERSON>: The key is the idea of funding translational research focused on science that is being generated here in San Diego. I really think those are the two key elements in what made Curebound slightly different than most other, if not all other, cancer-funding organizations. What I mean by translational — and I really feel strongly about this, it’s been a focus of my career and my efforts for 20 years — is getting discoveries to patients. So some sort of human and patient application. I want to make a difference in patients’ lives. And that is in no way to diminish the importance of basic science discoveries — finding out how a protein works, how a gene affects other genes. Those are important and eventually those are discoveries that can lead to changes in people.\n\nWe have incredible research going on within just a few square miles in San Diego. I felt like if we had the ability to fund collaborations to get those discoveries to people, that would make a tremendous difference in the long-term for cancer patients, not only here in San Diego but obviously all over the world.\n\nQ: How do you think Curebound can best engage the community to raise awareness and interest in the work it does?\n\nCohen: Curebound is relatively young, it’s even less than two years old if I’m counting properly. That’s really a critical part of the effort now, is to let the community know that Curebound exists, what it’s doing and what its mission is. In terms of how to do that, obviously there are events. One of the other major events that Curebound has (in addition to the Padres Pedal the Cause bicycling event) is a concert in the fall. Last year it was Alicia Keys. We’re about to announce this year’s in the next couple weeks. It will also be a big name. And other fundraising events or publicity events that go on throughout the year. Smaller salons or roundtables, we’re planning to sponsor and host scientific meetings really showcasing the science that we’re funding as those projects mature. And so there will be a lot of opportunities for Curebound to interact with the community and vice versa.\n\nQ: What are your short and long-term goals with Curebound?\n\nCohen: I think short-term, we’re working on solidifying the process of grant funding and making sure that we get the best science to apply for grants to make sure that we have a validated and transparent process to fund the best science. Without that, we lose legitimacy, and that would be terrible. In the long term, it’s really about getting Curebound to a point where we’re able to fund projects that will make a major impact on how we treat cancer, how we diagnose it, prevent it. That, of course, will require larger sums of funding, larger grants. But I believe, first of all, that San Diego has the capacity to do that. And secondly, I think we’ve got the science here that is ripe for that type of funding and take advantage of the biotech hub we have here to translate those findings to patients.\n\nVisit www.curebound.org for more information about Curebound."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMijAFodHRwczovL3d3dy5kZWxtYXJ0aW1lcy5uZXQvbmV3cy9zdG9yeS8yMDIzLTA2LTI3L3NvbGFuYS1iZWFjaC1yZXNpZGVudC1iZWNvbWVzLWNoaWVmLXNjaWVuY2UtYWR2aXNvci1vZi1jYW5jZXItcmVzZWFyY2gtbm9ucHJvZml0LWN1cmVib3VuZNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 27 Jun 2023 07:00:00 GMT", "title": "Solana Beach resident becomes chief science advisor of cancer research nonprofit Curebound - Del Mar Times", "content": "Solana Beach resident <PERSON>, M.D., recently joined Curebound as its first chief science advisor.\n\n<PERSON> grew up in Toronto, went to medical school at the University of Toronto, and worked as a family physician before pursuing his interest in oncology. After 15 years on the faculty at the University of Chicago, he moved to UC San Diego about 10 years ago.\n\nIn a Q&A, <PERSON> discussed his new position at Curebound, a nonprofit that focuses on cancer research. Answers were lightly edited for clarity and conciseness.\n\nQ: How did your career lead you to Curebound?\n\n<PERSON>: At UC San Diego I was doing multiple things, including roles in the cancer center, in the division of hematology-oncology. And then I got involved with a couple of cancer-focused fundraising organizations. One of them was called the Immunotherapy Foundation and the other was called Pedal the Cause. Those two organizations merged to become Curebound. I was involved in that merger and peripherally advising the founders in terms of how they should structure their organization. I eventually became a member of the scientific advisory board. And then their CEO approached me because we had regular contact. In one of our conversations, she said that they’d soon be looking for a scientific director, and that thought stuck with me. Several months later I had another conversation with her and I said I’d be interested in doing that role.\n\nQ: What will your priorities be in your role with Curebound?\n\n<PERSON>: The key is the idea of funding translational research focused on science that is being generated here in San Diego. I really think those are the two key elements in what made Curebound slightly different than most other, if not all other, cancer-funding organizations. What I mean by translational — and I really feel strongly about this, it’s been a focus of my career and my efforts for 20 years — is getting discoveries to patients. So some sort of human and patient application. I want to make a difference in patients’ lives. And that is in no way to diminish the importance of basic science discoveries — finding out how a protein works, how a gene affects other genes. Those are important and eventually those are discoveries that can lead to changes in people.\n\nWe have incredible research going on within just a few square miles in San Diego. I felt like if we had the ability to fund collaborations to get those discoveries to people, that would make a tremendous difference in the long-term for cancer patients, not only here in San Diego but obviously all over the world.\n\nQ: How do you think Curebound can best engage the community to raise awareness and interest in the work it does?\n\nCohen: Curebound is relatively young, it’s even less than two years old if I’m counting properly. That’s really a critical part of the effort now, is to let the community know that Curebound exists, what it’s doing and what its mission is. In terms of how to do that, obviously there are events. One of the other major events that Curebound has (in addition to the Padres Pedal the Cause bicycling event) is a concert in the fall. Last year it was Alicia Keys. We’re about to announce this year’s in the next couple weeks. It will also be a big name. And other fundraising events or publicity events that go on throughout the year. Smaller salons or roundtables, we’re planning to sponsor and host scientific meetings really showcasing the science that we’re funding as those projects mature. And so there will be a lot of opportunities for Curebound to interact with the community and vice versa.\n\nQ: What are your short and long-term goals with Curebound?\n\nCohen: I think short-term, we’re working on solidifying the process of grant funding and making sure that we get the best science to apply for grants to make sure that we have a validated and transparent process to fund the best science. Without that, we lose legitimacy, and that would be terrible. In the long term, it’s really about getting Curebound to a point where we’re able to fund projects that will make a major impact on how we treat cancer, how we diagnose it, prevent it. That, of course, will require larger sums of funding, larger grants. But I believe, first of all, that San Diego has the capacity to do that. And secondly, I think we’ve got the science here that is ripe for that type of funding and take advantage of the biotech hub we have here to translate those findings to patients.\n\nVisit www.curebound.org for more information about Curebound."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiTmh0dHBzOi8vd3d3LmNvaW5nZWNrby5jb20vbGVhcm4vc29sYW5hLXZzLWV0aGVyZXVtLXRoZS1iYXR0bGUtZm9yLWwxLWRvbWluYW5jZdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 27 Jun 2023 07:00:00 GMT", "title": "Solana vs. Ethereum: The Battle for L1 Dominance - CoinGecko Buzz", "content": "The battle of Layer 1 chains has been raging on since 2020, in the middle of DeFi Summer. Before the 2021 bull run, with L1 chains, Ethereum was undoubtedly the behemoth of decentralized finance (DeFi). It made sense, since it has first-mover advantage.\n\nBut the times, they are a-changin'.\n\nAnd being the first isn't always good. See, Ethereum's architecture has been creaking under the thousands of dApps built on top of it. In the Ethereum white paper, <PERSON>lik laid out what’s now commonly known as the blockchain trilemma. In essence: You can have speed, scalability, and security, but not all three at once. (Ah the Three S's.) This is the problem that we've been trying to resolve with all the different L1 blockchains.\n\nAnd one of the most hyped contenders of them all is probably Solana. Sure, Sol<PERSON> is the new kid on the block(chain). But despite being just a baby, it's attracted a horde of pitchfork-holding haters (and smart-money-following VC lovin' investors too). Let's ignore price movement for a second, though. Instead, we should take a closer look and attempt to determine which one is the better blockchain.\n\nShall we?\n\nSolana vs. Ethereum: A Breakdown\n\n© olieman.eth | Unsplash\n\nNewer doesn't always mean better. In fact, Ethereum's maturity means that it's had time to go live with thousands of decentralized applications (dApps). Many of these dApps have inspired similar projects on other chains as well.\n\n1. Consensus mechanism\n\n© Shubham Dhage | Unsplash\n\nThe consensus mechanisms for Solana and Ethereum couldn't be any more different. Bitcoin and Ethereum both use Proof-of-Work (PoW). That's all fine and good for a transactional network like Bitcoin, but that isn't what Ethereum is.\n\nEthereum's Proof-of-Work consensus is based on an auction model, so users compete by bidding to have their transaction included in the next block. During times of network congestion, you can imagine the nightmarish bidding scenarios this model can create. And insane gas fees on Ethereum can harm transaction volume. Combine this with its PoW consensus mechanism. This involves mining, and mining is energy-intensive. That's why, with Ethereum's Merge (formerly 'Eth2.0'), the network is transitioning to Proof-of-Stake (PoS), purportedly in a couple of months’ time.\n\nWhy is PoS believed to be better than PoW? Well, for one, it's less energy-intensive. See, instead of running specialized hardware equipment, validators for PoS consensus need to have a certain minimum amount of native tokens staked in the chain. The amount of ETH you've got staked in the network ensures that you won't act against your own interest. The reasoning is that you wouldn't act as a malicious actor to compromise the same network you're invested in. In exchange, you're rewarded for helping secure the network.\n\nSolana already uses PoS, by the way. And it combines it with this innovative little mechanism called Proof-of-History (PoH). We'll get more into PoH below, when we discuss scalability. For now, know that both SOL and ETH can be staked on their respective networks. But for Solana, combining two consensus mechanisms makes its network highly performant. So what does this mean when a user transacts on the blockchain? Well, it feels like the future. It's fast. (Like, bullet-train, this-doesn’t-feel-like-a-blockchain fast.) And don't even get me started on transaction costs. A transaction fee on Solana is peanuts. 🥜\n\n2. Downtimes\n\n© Raffaele Vitale | Unsplash\n\nAs far as downtimes go, Ethereum benefits from being an OG blockchain, having had lots of technical kinks ironed out in its early days. While the network can get congested at times, it never goes down, thanks to being significantly more decentralized than most chains. In fact, it's part of the reason Ethereum has had such a hard time scaling. The project still wants a typical crypto user to be able to run an Ethereum node on normal hardware. Vitalik clearly addressed the limitations of its L1 network in the Ethereum white paper.\n\nSolana, on the other hand, is clearly still experiencing growing pains. To date, the chain has suffered several downtimes since its launch. It's worth looking a bit closer at these incidents.\n\nAll the shutdowns appear to have been caused by a Distributed Denial-of-Service (DDOS) Attack. A DDOS attack occurs when a malicious actor attacks a network with the aim of overwhelming it. In two of the cases, the attacker appears to have intentionally targeted the network. The result? Solana was shut down for several hours, undermining confidence in our investors (They could not help but furrow their brows in quiet despair).\n\nAnother shutdown was not the result of a direct attack. Instead, during an Initial DEX Offering, swarms of bots appear to have overloaded the network. The network reportedly peaked at 400,000 transactions per second (TPS) (!) by the time of the crash. I didn't know what to be more impressed by when I first read this. Sure, the crash raised a few alarm bells (I've got SOL), and all the headlines focused on the Solana crash. Still. I feel like the papers should have underscored the fact that Solana was able to handle up to 400,000 TPS.\n\nTalk about burying the lede.\n\n3. Transaction Speed\n\n© Chuttersnap | Unsplash\n\nWhen it comes to transaction speed, Solana takes the cake. In fact, when it comes to the processing of transactions too, Solana is one of the fastest blockchains in the game. This obviously has to do with the network architectures. See, Ethereum prioritized decentralization, whereas Solana focused on throughput. Let's take a look at the numbers.\n\nAt present, Ethereum can manage up to 30 TPS. 😢 Compare this with Solana, which can process over 50,000 TPS, without risk of crashing. Want to see how this stacks up against Visa? 65,000 TPS, so the numbers are becoming comparable. Not impressed? Think timescale. Visa has been operational for decades. Blockchains, in contrast, are a nascent technology.\n\nAnd things are only heating up.\n\nFinally, think these numbers are impressive? Then imagine my face when Ethereum announced that it would be able to process 100,000 TPS post-merge. No freaking way, I thought. Ethereum's only been around a handful of years. It's actually decentralized. And it's claiming it'll be able to process tens of thousands more transactions per second than Visa? (aka the largest credit card institution in the world!)\n\n4. Programming Language\n\n© Tezos | Unsplash\n\nThe main programming languages used to develop on Solana are Rust C and C++. In contrast, Solidity is the main programming language for the Ethereum blockchain. Here's a breakdown of Solidity versus Rust, in a nutshell.\n\nFirst off, Ethereum is the most popular blockchain platform for DeFi and NFTs. Many DeFi dApps have been built on Ethereum first (i.e., with Solidity). Due to Ethereum's popularity, many other L1 blockchains have wanted to emulate this success by allowing developers to simply port over their Solidity-based smart contracts. Specifically, there are a number of Ethereum Virtual Machine (EVM)-compatible blockchains, outside of Ethereum itself, which work with Solidity code. These include popular chains like Fantom Opera, Avalanche Contract Chain (C-Chain), and Binance Smart Chain (BSC). So if you want to try your hand at learning a smart contract language, Solidity is a solid bet. What also helped my consideration was that I was already familiar with web languages like Javascript. If you are too, you'll hit the ground running with Solidity.\n\nLet's compare this to Rust real quick, which currently has fewer blockchain-specific applications. However, a couple of key points work for Rust:\n\nIt's not a language limited to smart contracts. Rust is used in many off-chain applications, including finance. There are more Rust developers out there in the world than Solidity developers. Rust is gaining popularity. It's currently used by new L1 chains that have gained tons of rapid momentum recently. NEAR is an example.\n\nKeep in mind, though, that it will take you longer to learn Rust. This is because you'll be required to familiarize yourself with the Rust syntax first. (Solidity syntax is super basic.)\n\n5. Scalability\n\n© Alex Wong | Unsplash\n\nWhen it comes to scalability, the main issue with Ethereum is that PoW takes time and energy to verify. Specifically, all nodes are required to form a consensus to verify the present state of the blockchain. Compare this to Solana, which is a stateless chain.\n\nSolana can afford to be stateless thanks to its PoH consensus. PoH involves timestamping the transactions, which ensures that the transactions are verified in sequential order. And what happens if they're in sequence? You can do away with consensus verification of the present state. Moreover, you can also do away with the mempool. That's the name for the temporary space for blocks that are awaiting validation (It's sort of like a purgatory for blocks).\n\nDoing away with these functions allows Solana to scale much more lightly and easily. Oh, and Solana doesn't need any Layer 2 solutions either. It just works. It does have one major vulnerability though.\n\nThe fewer number of nodes do mean that Solana is way more centralized compared to Ethereum. (It's best to think of decentralization on a scale. Not all decentralization is good, and not all centralization is bad. They have their specific use cases, as well as tradeoffs. Another challenge with Ethereum scalability comes in the form of TVL. Sure, when it comes to market capitalization, Ethereum is the clear winner.\n\nBut will this remain true in the future?\n\nOther L1 chains have been chewing away at Ethereum's market share. That's why The Merge will make things interesting. For one, I'd like to see whether it will actually launch on time this time. Second, what whole new Pandora's box of problems will it open? This remains to be seen. Another, more serious concern at the moment is whether L2 solutions like Polygon will begin eating away at the TVL of the Ethereum base layer.\n\nInteresting developments. Interesting times.\n\n6. Ecosystem Growth\n\nDeFi Ecosystem\n\nEthereum and Solana present viable platforms for decentralized applications and smart contract projects. Each with its own perks, developers are fast building utilities on the two blockchains and targeting mainstream relevance. Ethereum is the originator of the DeFi idea and conceived the earliest DeFi projects with the first fully recognized DeFi platform launching on Ethereum in 2015. Solana’s first stablecoin trading platform - Mercurial Finance, launched 6 years later. Both blockchains have seen immense growth since then.\n\n© DefiLlama Ethereum’s TVL chart [on August 8, 2022]\n\n© DefiLlama Solana’s TVL chart [on August 8, 2022]\n\nDecentralized Finance (DeFi) projects on Ethereum blockchain boast a cumulative TVL (Total value locked) of over $30 billion from over 400 tracked DeFi projects at the time of this writing while projects on Solana have amassed a TVL of about $1 billion from 70 tracked projects. At previous peaks, these were well over $100 billion and $12 billion respectively.\n\nMaker DAO – Ethereum’s biggest DeFi project (in terms of value locked), develops and furnishes a decentralized algorithmic stablecoin (DAI) and offers decentralized lending and borrowing services controlled by smart contract protocols. A closely related platform on Solana – Solend, controls a TVL of over $250 million and is the blockchain’s second biggest DeFi project’ only behind staking solution project; Marinade Finance with a TVL of over $300 million.\n\nEven more plausible DeFi projects are leveraging Ethereum and Solana blockchains’ facilities. You’d say Ethereum enjoys a lot of that ‘first to market’ privilege, but despite being slower and charging more fees for transactions, its decentralization and stability appeal to developers and users. In contrast, Solana has an extensive record of timeouts and blockchain-level mishaps. A popular reason why some developers still prefer Ethereum blockchain.\n\nOther factors like maximalist views and the need to benefit from an existing reputation add to Ethereum’s growth speed. Regardless, Solana developers are hard at work and making plausible breakthroughs.\n\nNFT Ecosystem\n\nNFT projects are thriving on both blockchains. Both blockchains have moved billions of dollars in NFT trading and have seen incredible adoption of NFT protocols. . Ethereum’s Bored Ape Yacht Club (BAYC) NFT is arguably one of the most successful NFT project to date, selling for over $300,000 at peak value. Solana’s Monkey Business sold at over 248 SOL floor price at peak.\n\n© CoinGecko Q2 2022 Cryptocurrency Report\n\nThere are over 80,000 NFT collections on Ethereum blockchain and well over 10,000 on Solana blockchain. Judging from the statistics above, Ethereum is still the dominant player in the NFT scene, controlling about 83% of the NFT market in June 2022. Mainstream adoption has unarguably influenced this growth, but the viability of both blockchains is the major reason for the exponential climb in digital arts minted on both blockchains.\n\n7. Roadmap\n\nEven in their brilliance, both Ethereum and Solana blockchain still suffer tangible issues. Developers on both blockchains are consistently working on improvements and further optimizations. Ethereum has a stacked map of improvement events for the future, while Solana developers have also announced a couple of plans for the near future and beyond. Both blockchains’ roadmaps are developing and prone to changes. Deviation from set timelines is usual.\n\nEthereum developers are working on numerous fixes as part of the blockchain’s upgrade. Also known as Serenity, Ethereum’s upgrade is tipped to fix Ethereum’s biggest issues and set the blockchain up for further adoption. Serenity upgrade is dubbed the biggest development on Ethereum blockchain since its inception in 2015. As part of the upgrade, Ethereum’s consensus mechanism will be changed from proof of work to proof of stake. This is expected to improve the blockchain’s scalability, reduce the block size and optimize the overall output. Ethereum’s Ropsten testnet first implemented the switch to proof-of-stake in June 2022 after merging the Beacon chain and mainnet. Ethereum’s mainnet is expected to make this switch in September 2022.\n\nWhen the Merge happens, Ethereum will be just one step away from finally completing one of the most important deliverables of the Serenity upgrade – Sharding. Sharding technology will also be implemented as part of the Ethereum upgrade. The Sharding idea is to make a blockchain more efficient by partitioning it into lighter units. The sharding technology is expected to be implemented on the Ethereum blockchain in 2023. However, these dates are not specific and there are bound to be variations. Learn more about Ethereum’s upgrade.\n\nDespite the rapid growth and adoption, Solana is still in its earliest development stage and developers are working on delivering smooth functionality as part of the final release. The published roadmap hints at plans to deliver the full mainnet version in the last quarter of 2022.\n\nSolana has also announced plans to build a web3 mobile phone. Solana will partner with privacy-focused tech company – Osom, to produce Solana Saga; a blockchain-based smartphone built on the Solana blockchain. Solana Saga is an improvement from Osom’s OV1 which was expected to be released later in 2022.\n\nBoth blockchains working on important improvements and fixes, the projected improvements could see the technological margin between the two chains lessen. According to Ethereum developers, Ethereum will be able to execute 100,000 transactions per second after the Serenity upgrade. Compared to the current speed of 30 transactions per second, this will further speed up developments on the chain. Solana developers also expect to fix the downtime errors and deliver a stable network when the mainnet is fully launched.\n\nShowdown Results\n\n© Leyre | Unsplash\n\nSolana and Ethereum are the two main chains to pay attention to at the moment. While Solana is already blazing fast, it’s still sometimes plagued by network downtime issues. Ethereum, on the other hand, has been chugging along for a while, but is starting to show its age. All eyes are on The Merge, though, and whether it can successfully modernize and scale.\n\nStill, the battle of L1 chains rages on.\n\nIf you'd like to see more battles, check out our comparative piece on Cardano vs Ethereum (Tons of maxis in both camps who want to rip each other's heads off.)"}]