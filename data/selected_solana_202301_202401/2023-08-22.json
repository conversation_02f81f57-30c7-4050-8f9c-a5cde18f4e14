[{"id": 16, "url": "https://news.google.com/rss/articles/CBMidWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA4LzIyL21hcGxlLWZpbmFuY2UtZXllcy1hc2lhbi1leHBhbnNpb24td2l0aC01bS1pbnZlc3RtZW50LXJldHVybnMtdG8tc29sYW5hL9IBeWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA4LzIyL21hcGxlLWZpbmFuY2UtZXllcy1hc2lhbi1leHBhbnNpb24td2l0aC01bS1pbnZlc3RtZW50LXJldHVybnMtdG8tc29sYW5hL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 22 Aug 2023 07:00:00 GMT", "title": "Maple Finance Eyes Asian Expansion With $5M Investment, Returns to Solana - CoinDesk", "content": "Maple’s focus on Asia showcases the region’s increasing importance for the digital asset industry. Asian countries have taken charge in setting up clear rules for crypto firms to service consumers, which is in stark contrast to the regulatory uncertainty in the U.S. Hong Kong recently handed out the first licenses to trading platforms under the new crypto regime, while last week Singapore’s central bank released a regulatory framework for stablecoins. U.S-based exchange Gemini expanded in the region with a new hub in Singapore earlier this summer."}, {"id": 15, "url": "https://news.google.com/rss/articles/CBMiNGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL21hcGxlLXNvbGFuYS11cy10cmVhc3VyeXPSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 22 Aug 2023 16:37:35 GMT", "title": "Maple seeks 'first mover' status on Solana with US Treasury yield access - Blockworks", "content": "Maple Finance returned to Solana on Monday as it seeks to be a “first mover” by offering on-chain access to US Treasury bill yields on the network.\n\nThe institutional capital marketplace’s cash management solution is designed for DAOs and other accredited investors to better manage treasury funds, according to Maple CEO <PERSON>.\n\nDeposits can be made by such clients that hold the USDC-SPL token. The pool currently accepts deposits of USDC-SPL and passes yield sourced from one-month US Treasury bills — minus fees — to lenders.\n\nMaple’s 15 lending pools have facilitated more than $2 billion in corporate loans since the company launched in May 2021. It launched on Solana last year, facilitating the issuance of roughly $125 million in loans on the blockchain before the development pause in December.\n\nRead more: Maple Finance scoops up lending protocol Avari to hasten launch on Solana\n\n“Maple’s return to Solana signifies a strategic choice that goes beyond mere business considerations,” <PERSON> told Blockworks. “Builders continue to launch new Solana protocols and investors continue to back them.”\n\nThe company sees a number of positive catalysts for Solana’s growth, <PERSON> added, such as Firedancer, an independent validator client for the blockchain that is built by Jump.\n\n“As Solana continues to expand and attract new protocols, there is a growing need for products that can generate low-risk yield opportunities for stablecoins,” the Maple CEO said.\n\nOthers have sought to offer on-chain investors access to US Treasury yields. Adapt3r Digital launched a tokenized Treasury-focused fund on decentralized marketplace Archblock last week, while Ondo Finance introduced tokenized US Treasurys and bond offerings in January.\n\nThe Solana re-launch comes after Maple introduced its cash management pool in April. It has since attracted about $27 million in deposits and pays roughly 4.7% to participants including UXD Protocol, Relm Insurance and Altitude Fi.\n\nThe company’s cash management solution opened to US accredited investors and other entities earlier this month.\n\nMaple on Tuesday revealed it had closed a $5 million funding round led by BlockTower Capital and Tioga Capital. The capital is set to help it expand to the Latin America and Asia Pacific regions, the company said in a statement.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 19, "url": "https://news.google.com/rss/articles/CBMiOGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3BoYW50b20tc2lnbi1pbi1zb2xhbmEtd2FsbGV00gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 22 Aug 2023 07:00:00 GMT", "title": "Phantom materializes 'sign in with <PERSON><PERSON>' wallet authentication - Blockworks", "content": "Wallet service provider Phantom said Monday it has introduced a new feature allowing applications to authenticate users using a Solana address.\n\nThe introduction of Sign In With Solana (SIWS) is intended to improve the user experience and security by streamlining the traditional “connect” and “signMessage” flow into a single-click “signIn” method.\n\nTraditionally, the connect function enables a user’s digital wallet to interact with an application, while “signMessage” allows the user to cryptographically verify their identity.\n\nThe message lacks human-readable information, which can sometimes lead to new and seasoned users agreeing to malicious prompts.\n\n\n\nEven in cases when connecting a wallet to trusted applications, unexpected pop-up signatures can often confuse or frighten new users, <PERSON> said in a statement. This, in turn, can often be a major hurdle for new entrants, it said.\n\nThe one-click sign-in method, available as of Phantom version 23.11, allows Solana developers to prompt users to connect and sign a standardized authentication message and prove ownership of their addresses.\n\nDesigned as a drop-in replacement for the previous two-step authentication flow, the sign-in method also provides developers with a robust API for creating standardized authentication messages, <PERSON> said.\n\nThe responsibility for message construction shifts from applications to the wallet, allowing Phantom to scrutinize elements such as the site’s domain or the time of message issuance to ensure legitimacy.\n\nIt is unclear whether there could be fresh privacy concerns associated with the shift of responsibility in message construction. Blockworks has reached out to Phantom for comment.\n\nIn some instances, that construction may help slow or hinder the progress of malicious actors. That could help prevent instances like last year’s breach, when a coding error allowed hackers to easily find and use client passwords, leading to a breach in both Slope and Phantom wallets.\n\nPrivate keys associated with the impacted wallets were either leaked or compromised, and these keys were then utilized to authorize fraudulent transactions, according to reports at the time.\n\nThe sign-in feature released on Monday makes authentication more consistent and allows Phantom to check for suspicious activity, its developers said.\n\nEthereum dapps similarly can facilitate a “sign in with Ethereum” feature using Ethereum Name Service (ENS), as part of a push for decentralized identity services.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 17, "url": "https://news.google.com/rss/articles/CBMiNGh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2VsdXNpdi1wcml2YWN5LXRva2VuLXN3YXDSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 22 Aug 2023 07:00:00 GMT", "title": "Elusiv debuts private token swap functionality for Solana - Blockworks", "content": "Privacy protocol Elusiv is launching a private token swap tool for Solana. The platform intends to aid users who wish to trade their assets without revealing personal information about themselves.\n\nThe core infrastructure for the tool has been under development for over a year, and is now available through a web app and supports transfers to other crypto wallets in solana (SOL), USD Coin (USDC), tether (USDT), bonk (BONK) and samoyedcoin (SAMO).\n\nExpansion to other ecosystems outside of Solana is being considered, according to Elusiv co-founder <PERSON>.\n\nTo use it, people have to connect their wallet and top up their “private balance” to their desired amount. Then, when it’s time to make a transaction, the private swap functionality encrypts the user’s public key while also generating a temporary key. That temporary key sends the transaction, thereby obscuring the user’s trading habits.\n\n“This enables users to swap assets with complete privacy, as token pairs and amounts can never link to a user’s wallet address. This increases users’ security, hides trading strategies and protects those making frequent swaps or exchanging large quantities,” Elusiv shared in a statement.\n\nElusiv also pointed out that making trades on decentralized exchanges (DEXs) are not the end all be all for privacy, as data is scraped from them and published on block explorers.\n\n<PERSON><PERSON><PERSON> said this tool is crucial to make DeFi fair for everyone, including regular users and seasoned traders.\n\n“Private token swaps secure one of the most fundamental actions in all of DeFi and push the entire Web3 space forward to deliver on core values of self-sovereignty and data ownership,” <PERSON><PERSON><PERSON> said in a statement.\n\nThe idea of obscuring crypto transactions isn’t at all new. Crypto mixers such as Tornado Cash have been around for years. CoinJoin was released in 2013, and it performed a similar function to mixers but was specific to Bitcoin.\n\nHowever, ​​Deschler told Blockworks that Elusiv doesn’t mix user funds, adding that Elusiv is more akin to privacy protocols Aztec and Railgun.\n\n“We utilize a combination of cryptographic schemes, most notably zk-SNARKs to bring privacy guarantees to users,” ​​Deschler said. “Mixers, on the other hand, present notable differences in their privacy assurances, programmability, and foundational structure.”\n\nMixers have also run into legal trouble. Tornado Cash developer Alexey Pertsev was arrested last summer after authorities suspected him of facilitating money laundering. Before Pertsev’s arrest, the US Treasury sanctioned Tornado Cash, making it impossible for Americans to interact with it.\n\n​​Deschler said Elusiv has “not been met with any legal hurdles,” adding that his team is engaged with the European Crypto Initiative’s efforts to develop fair regulation.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vd3d3LmRlbG1hcnRpbWVzLm5ldC9hcnQvc3RvcnkvMjAyMy0wOC0yMi9hcnRpc3QtZG9uYXRlcy1yaW8tc2N1bHB0dXJlLXRvLWNpdHktb2Ytc29sYW5hLWJlYWNo0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 22 Aug 2023 07:00:00 GMT", "title": "Artist donates 'Rio' sculpture to city of Solana Beach - Del Mar Times", "content": "The city of Solana Beach’s Public Art Collection has added its 13th permanent piece with <PERSON><PERSON>, a beautiful bronze woman in a yellow bikini, her feet sinking into the sand atop the bluffs of Tide Beach Park.\n\nSculpted by local artist <PERSON>, Rio welcomes beachgoers at the access point at the corner of Pacific Avenue and Solana Vista Drive. The statue was on loan to the city for a year as part of the 2022 Temporary Public Art Program before <PERSON><PERSON><PERSON><PERSON> donated her to the city for keeps earlier this year.\n\nThe bronze sculptor has also been a cosmetic dentist for 50 years with a practice in La Jolla, Harmetz & Kim.\n\n“I never really thought of myself as an artist, even when I did that piece,” he said. “ I always think of myself as a dentist.”\n\n<PERSON><PERSON><PERSON><PERSON> did the artwork on Rio nearly 20 years ago. He spent a total of 16 months sculpting the clay while working his day job as a dentist and the sculpture spent another seven months at the foundry to be cast in bronze.\n\n“I’m glad she has a permanent home where she is appreciated,” said <PERSON><PERSON><PERSON><PERSON>. Rio’s new home on Pacific Avenue is actually right down the street from where <PERSON><PERSON><PERSON><PERSON> bought his first house on Pacific Avenue in 1976. He used to go to the Tide Beach Park all the time when his son was little, back when the beach access was a rickety wooden staircase.\n\nFrom Solana Beach the family moved to the edge of Rancho Santa Fe where they lived for many years—one of his son <PERSON>’s early claims to fame was being one of the kids to co-name Solana Santa Fe School when it opened in 1993. <PERSON><PERSON><PERSON><PERSON> and his wife <PERSON>, a special education teacher for over 35 years, now reside in Carmel Valley.\n\n<PERSON>rmetz didn’t start sculpting until he was around 40 years old. As a dentist, he had done a lot of meticulous lab work, shaping crowns and perfecting smiles. He started dabbling in making jewelry and gold figurines before deciding he wanted to try his hand at sculpting 35 years ago when he was driving by a <PERSON>. Armed with a glue gun and some Styrofoam, he gave it a go.\n\nUp until three years ago, he used to sculpt in his office in his free hours—that’s where Rio was formed—but now he has his own art studio in the Del Mar village.\n\nSculpture can be a long, long process, often starting with just some PVC pipes and clay.\n\n“You look at it and you think ‘God, where do I start?’ It’s very hard, it’s very physical,” he said “The clay is hard as rock, you get blisters and keep working at it and working at it and finally you have something.”\n\nBefore Rio,Harmetz had done a large sculpture called “Healing Love” depicting a man’s love for a woman who had battled breast cancer, showing a mastectomy scar. The sculpture has been on display for many years at Sharp Memorial Hospital—“I was thrilled to death when Sharp asked for it, I was just beside myself,” he said. He has also done a 9-foot polished bronze dolphin named “Hope” which was donated to the city of Orange Beach, Alabama following the BP oil spill caused by the explosion of the Deepwater Horizon oil rig.\n\n“It still doesn’t come easy to me. I don’t have a particular style, I like doing figurative work,” he said. “I have done small landscapes and oceanscapes, I try to do things that haven’t been done.”\n\nRio is just about life-size—the model was a woman from La Jolla named Paula who was 6 feet tall but the bronze is just 5’10. After he did the sculpture Paula ended up moving away and she never did see the completed project.\n\nWhen the city of Solana Beach put out the call for artists a couple years ago, he offered up Rio and was happy when she was selected and chosen for placement at the popular beach site. The sculpture was one of four temporary art installations that include spots such as the Seascape Sur Beach public beach access, Lomas Santa Fe Drive and Solana Beach Town Centre.\n\nRichard Harmetz’s “Rio” in Solana Beach. (Richard Harmetz)\n\nHarmetz said he wasn’t sure what the reaction would be and even called every few days to check if people were saying anything bad. After a year of Rio on the beach, he offered to donate the sculpture, which cost him $32,000 to make, not including his time sculpting it.\n\n“I just like the idea of having the piece out in public,” he said.“It’s a fun thing doing this.”\n\nHamertz said there are many things about Rio that people may not even notice at first glance, like how the base is slanted like the beach or the pareo that is clasped in her hand and blowing in the wind behind her—he reveals that to make that mold of the flowing fabric, he floated mylar balloons and poured super glue over it.\n\nIn the studio right now, Hamertz is working a life-size female MMA fighter, working to portray a woman as a warrior and the courage and discipline it takes to get into the octagon. So far he’s only done two feet and two legs—he estimates it will take a year to sculpt it and he won’t have it bronzed unless he has a buyer.\n\nOver the years, however, Hamertz has never made art for money or just to be sold—he has gifted all of his pieces for display or for charity and he doesn’t really market himself as an artist.\n\n“It’s really for me that I do the art,” he said."}]