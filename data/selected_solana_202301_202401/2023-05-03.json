[{"id": 15, "url": "https://news.google.com/rss/articles/CBMiXGh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA1LzAzL25ldy1jcnlwdG8tdmMtZmlybS1jcmVkaWJseS1uZXV0cmFsLXJhaXNlcy01NW0v0gFgaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDUvMDMvbmV3LWNyeXB0by12Yy1maXJtLWNyZWRpYmx5LW5ldXRyYWwtcmFpc2VzLTU1bS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 May 2023 07:00:00 GMT", "title": "New Crypto VC Firm Credibly Neutral Raises $5.5M - CoinDesk", "content": "Credibly Neutral, a new crypto venture capital firm, has raised $5.5 million from a collection of investors that includes the co-founders of the Solana and Polygon blockchains and the investment firm of <PERSON>, co-founder of Ethereum and founder of ConsenSys. The firm was founded by <PERSON>, who leads the protocol operations team at Coinbase Cloud, and <PERSON>, chief operating officer at Ethereum privacy startup Aztec Protocol."}, {"id": 16, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvZG9nZWNvaW4tY3JlYXRvci1jcml0aWNpemVzLXNvbGFuYS1hcy1jZW50cmFsaXplZC1kYXRhYmFzZS13aXRoLW5vLXJlYWwtc29sdXRpb25z0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 May 2023 07:00:00 GMT", "title": "Dogecoin Creator Criticizes Solana as Centralized Database with No Real Solutions - Watcher Guru", "content": "Dogecoin co-founder <PERSON> has recently commented on Solana. <PERSON>, aka <PERSON><PERSON><PERSON>, is one of the co-founders of Dogecoin. <PERSON> has recently taken to his Twitter account to talk about cryptocurrencies in general.\n\nAlso read: How to Bridge From Solana to Ethereum\n\nCryptocurrencies are rising in terms of adoption and numbers. There are thousands of cryptos out there. However, not all of them are worth considering as gems. <PERSON> stated in his tweet that cryptocurrencies that have high gas fees or high transaction fees are slowly turning into broken garbage. He also said, “Scale the tech, or ngmi.”\n\nreal talk, high gas fees / high transaction fees in popular cryptos essentially make them broken garbage\n\n\n\nscale the tech or it’s ngmi May 3, 2023\n\nSolana is centralized, says <PERSON>\n\nEven though <PERSON> doesn’t associate himself with <PERSON>ecoi<PERSON>, DOGE has very low transaction fees. There are numerous cryptocurrencies and blockchains out there touting low gas fees.\n\nA Twitter user commented below <PERSON>’ tweet, stating that Solana solves the problem of high gas fees. To this, <PERSON> replied that Solana is a centralized database. He also added that it doesn’t really solve anything.\n\nsolana is basically a centralized database though, it doesn’t really solve anything — <PERSON><PERSON><PERSON> (@BillyM2k) May 3, 2023\n\nSolana was indeed famous for its recurring network outages in 2022. However, the team had taken quick actions to fix them every time. Even though cryptocurrencies in general act in a united way, there is always a secret competition over which crypto or blockchain is better."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vYml0Y29pbmlzdC5jb20vZG9nZWNvaW4tZm91bmRlci1zbGFtcy1zb2xhbmEtY2VudHJhbGl6ZWQtZGF0YWJhc2Uv0gFOaHR0cHM6Ly9iaXRjb2luaXN0LmNvbS9kb2dlY29pbi1mb3VuZGVyLXNsYW1zLXNvbGFuYS1jZW50cmFsaXplZC1kYXRhYmFzZS9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 May 2023 07:00:00 GMT", "title": "Dogecoin Founder <PERSON><PERSON> As 'Centralized Database' - Bitcoinist", "content": "<PERSON>, one of the inventors of Dogecoin is known on Twitter for not making light of things. In one of his latest tweets, the software developer attacked the Solana (SOL) blockchain as a decentralized project without utility.\n\nThe Dogecoin creator known on Twitter as <PERSON><PERSON><PERSON> first wrote that he considers blockchains, which cannot scale and suffer from high transaction fees, useless. At first, <PERSON> did not name any names, but only wrote in a general statement:\n\nReal talk, high gas fees / high transaction fees in popular cryptos essentially make them broken garbage scale the tech or it’s ngmi [not gonna make it].\n\nUsers in the community responded with various replies to the Dogecoin founder’s claim. One user shared a meme of NANO, a cryptocurrency from 2017’s bull run that was massively hyped for its high scalability but never caught on due to a lack of use cases.\n\nHowever, <PERSON> dismissed NANO as not a valid answer, saying it was only about popular blockchains. He wrote: “I said popular crypto (jkjk but it was too easy).”\n\ni said popular crypto (jkjk but it was too easy) — <PERSON><PERSON><PERSON> (@BillyM2k) May 3, 2023\n\nAnother user countered the Dogecoin inventor that Bitcoin’s block space size of 1 MB and block time of 10 minutes is a feature, not a bug. In the long run, this will benefit Bitcoin, and turn BTC into a reserve asset.\n\nRegarding layer-2s (L2s) and L3s becoming “normal” everyday uses of cryptocurrencies, the user agreed with <PERSON>. The latter replied, “I guess if it’s not meant to scale then sure.”\n\nIs Solana Centralized?\n\nWhen someone in the comment thread remarked that “Solana fixes this,” by which he meant the gas fees and the limited speed, Markus slammed the blockchain as a “centralized database.” He wrote:\n\nSolana is basically a centralized database though, it doesn’t really solve anything.\n\nTo recall, the Solana blockchain has gone offline several times in recent years. The Solana Foundation had to reboot the ledger each time. But what is really true about the claim from the Dogecoin founder that Solana is useless and centralized?\n\nIn terms of transaction fees and processing speed, the SOL blockchain is known to be one of the best and most popular blockchains. As Nansen reported just today, Solana ranks second among blockchains with the most active addresses. Therefore, the claim that Solana is useless doesn’t really seem to be true.\n\nWhich chains had the most on-chain active addresses in April & how did they compare to March? BNB Chain: 10.9M (-11%)\n\nSolana: 5.1M (-12%)\n\nEthereum: 4.9M (-7.9%)\n\nPolygon: 4.2M (+6%)\n\nArbitrum: 2.4M (+7.8%) And what were these addresses doing? Let's take a look on-chain… pic.twitter.com/mjrBPgbmif — Nansen 🧭 (@nansen_ai) May 3, 2023\n\nHowever, the centralization question has been a point of criticism against Solana for quite some time. As Ryan Berckmans recently explained, Solana is much closer to Amazon Web Services (AWS) than to Ethereum and is much more centralized.\n\nIt’s not because of validators or stake statistics. It’s because Solana’s change management is “very centralized”. There are no specifications, no research community and no customer diversity, as Berckmans argues. According to the Web3 investor, Solana “evolves much like enterprise software” that is “primarily managed and executed by a single entity (Solana Labs).”\n\nOn the decentralization spectrum from eg. AWS to Ethereum, Solana is actually much closer to AWS than it is to Ethereum and significantly more centralized than many understand or represent. Not because of validator or stake stats. It's because Solana's change management is very… — Ryan Berckmans ryanb.eth (@ryanberckmans) May 1, 2023\n\nAt press time, the SOL price stood at $21.48. Given the current market situation, SOL is still holding strong and trying to defend a multi-month ascending trend line (black). However, a daily close above $22 is crucial today.\n\nFeatured image from Tech Times, chart from TradingView.com"}, {"id": 14, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vd3d3LmNuYmN0djE4LmNvbS9jcnlwdG9jdXJyZW5jeS9hLWd1aWRlLW9uLWhvdy10by1icmlkZ2UtZnJvbS1zb2xhbmEtdG8tZXRoZXJldW0tMTY1NTU5OTEuaHRt0gFpaHR0cHM6Ly93d3cuY25iY3R2MTguY29tL2NyeXB0b2N1cnJlbmN5L2EtZ3VpZGUtb24taG93LXRvLWJyaWRnZS1mcm9tLXNvbGFuYS10by1ldGhlcmV1bS0xNjU1NTk5MS5odG0vYW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 May 2023 07:00:00 GMT", "title": "A guide on how to bridge from Solana to Ethereum - CNBCTV18", "content": "Due to the complex nature of blockchain technology, linking assets across blockchains can get tedious. There is where blockchain bridges come in. They enable the transfer of assets from one blockchain to another, providing users access to a wider range of decentralised applications.Typically, a cross-chain bridge entails using a smart contract to lock or burn tokens on the source chain and another smart contract to unlock or mint tokens on the destination chain.For instance, some cross-chain bridges allow one to transfer assets between Ethereum and Solana. Bridges enable interoperability between two blockchain networks while leveraging their respective benefits, such as exploring unique dApps, trading with lower transaction fees, and increasing investor’s exposure to different assets that otherwise would not have been possible. In this article, we will examine how to bridge assets from Solana to Ethereum. But first, let’s explore how cross-chain bridges operate.As mentioned earlier, blockchain assets often cannot be directly transferred across different blockchains. To solve this issue, cross-chain bridges create \"wrapped\" versions of the asset that can be transferred to another blockchain.Suppose you want to send one Solana coin to an Ethereum wallet using a cross-chain bridge. The wallet will receive a \"wrapped\" token that represents the Solana coin in the form of an ERC-20 token, which is supported on the Ethereum blockchain.A quick note here is that there are two main types of bridges: Trusted bridges, which are controlled by a centralised entity, and trustless bridges, which operate on algorithms and smart contracts.Let us now understand how one can bridge from Solana to Ethereum.One of the many bridging options available to users is Allbridge. It assists in connecting EVM networks like Polygon and Ethereum with non-EVM chains such as Solana. Since its inception, Allbridge has facilitated over $6.4 million worth of asset transfers across blockchains. The transactions on Allbridge normally settle within 1 to 5 minutes, while the average fee charged for transactions is 0.3 percent.To utilise Allbridge, simply navigate to their website, connect the Solana-supported wallet, and select the \"From Solana to Ethereum\" option. Then, input the destination address and enter the amount to bridge. The transaction then needs to be completed by clicking \"send\".Portal Bridge is one of the most popular options for bridging assets between Solana and Ethereum.This is how the transfer can be done: First, go to Portal bridge, choose \"Source Solana to Target Ethereum\", and then select an asset you want to transfer. Connect two wallets on Solana and Ethereum. Finally, insert the address and amount, then proceed to approve the transaction by paying the network fees.Once the transfer is complete, the bridge will lock your asset on the Solana network and mint equivalent tokens on the Ethereum network. You will get a confirmation once the process is successful. The procedure normally takes 8 to 10 minutes.Centralised exchanges have been a popular way to move assets between blockchains once cross-chain bridge thefts, like the Wormhole exploit, emerged as a security risk for users. Each has pros and cons, but both options can be utilized per individual preference. For instance, decentralized bridges charge more fees than centralized solutions, while CEXs require submitting a KYC and personal details on the platform before transferring funds.To get started, open your CEX account, go to your wallet, and transfer ETH from your wallet. After receiving the asset, you then need to convert your funds to SOL. After the conversion, withdraw your SOL and send it back to your Solana wallet.The two most well-known decentralised finance (DeFi) platforms available to users are Solana and Ethereum, and a number of options have been developed to allow users to transfer assets between these blockchains.It is also important to note that cross-chain-related thefts have emerged as a major security risk in recent years, and one must weigh the options before choosing any bridge."}, {"id": 11, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8venljcnlwdG8uY29tL2ZpbmRpbmctY3J5cHRvLWdlbXMtbWFkZS1lYXN5LXdpdGgtYXZvcmFrLWFpLWFuZC1zb2xhbmEv0gFRaHR0cHM6Ly96eWNyeXB0by5jb20vZmluZGluZy1jcnlwdG8tZ2Vtcy1tYWRlLWVhc3ktd2l0aC1hdm9yYWstYWktYW5kLXNvbGFuYS8_YW1w?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 03 May 2023 07:00:00 GMT", "title": "Finding crypto gems made easy with Avorak AI and Solana - ZyCrypto", "content": "Advertisement\n\nMost avid crypto adherents agree that finding the next big thing can be challenging. However, Avorak AI (AVRK) and Solana (SOL) have attracted many analysts and experienced holders and could be the end of your search for 2023 crypto gems.\n\nCrypto gems\n\nCrypto gems are coins that have the potential to bring huge returns. However, these coins can be difficult to spot since no precise attributes define a gem other than the potential to increase in value over time. Nonetheless, many crypto gems are usually low-cap and have scarcity mechanisms, such as a limited supply or a deflationary model. This indicates that their value will increase with demand over time. But this isn’t set in stone since some gems are inflationary. Therefore, analyzing the project’s fundamentals is crucial in finding a crypto gem. Conducting thorough research and analysis is essential to avoid some of the risks associated with low-cap coins. Is the project a game-changer? Does it have a competitive advantage? And how proficient is the team? These are some of the questions you should be asking.\n\nAvorak AI (AVRK): An AI crypto gem\n\nMany analysts suggest that AI crypto is the next trend in crypto. Avorak AI (AVRK) is rapidly gaining recognition as an AI crypto gem due to several unique features that set it apart from other projects and tokens in the market. Avorak AI is first-to-market, which means it offers services and solutions that are not yet available from competitors, thus giving it a competitive edge. The platform is also user-friendly, including step-by-step guidance in all areas and AI chatbots and digital assistants that ensure customers receive answers quickly. This makes it easy for users to navigate the platform and use the latest AI technology. Moreover, Avorak’s AI services are affordable and use a token payment system, making them globally accessible to users at any level.\n\nAdvertisement\n\nAvorak AI has a comprehensive and reliable set of AI solutions for anyone seeking to leverage AI technology for business or personal use. These AI services are unique, easy to use, and can streamline many fields, such as trading, data analysis, security, content creation, and more. For example, Avorak offers a fully customizable algorithmic trading bot to help traders profit in any market condition. The Avorak Trade bot is not limited to one particular asset class or exchange and uses a simple command line input programmed with a standard script. The bot can execute trades on its user’s behalf and offers a range of indicators with notification systems to alert users of trend or pattern changes.\n\nThe AVRK token will be used to pay for Avorak’s AI services. AVRK is a deflationary token with a max supply of 40 million tokens. Thus, its price is expected to increase with demand. AVRK is selling at $0.210 with a 6% bonus in phase 5 of Avorak’s initial coin offering (ICO) event. With the influx of users in its ICO, Avorak AI pushed its launch price from $0.27 to $1. Analysts believe Avorak’s competitive advantages and performance in ICO indicate strong growth potential.\n\nIs Solana a good investment?\n\nDespite Solana (SOL) ranking among the top coins by market cap, it is significantly below its ATH (all-time high). This thus provides a great opportunity. Solana is a layer-1 blockchain, meaning it competes with blockchains like Ethereum. Solana’s unique PoH (Proof-of-History) consensus mechanism ensures impressive transaction speed and scalability, making it a promising project with the potential to revolutionize the blockchain industry. Solana’s transactions are also cheaper, giving it a competitive edge against blockchains like Bitcoin. More developers are building on top of Solana, with thousands of projects ranging from DeFi, NFTs, Web 3.0, and more. Many analysts are bullish on the future of Solana (SOL), with some predicting new highs this year.\n\nThe bottom line\n\nAvorak AI (AVRK) and Solana (SOL) have unique features and strong growth potential, which makes them intriguing opportunities. However, the crypto market is highly volatile, and carefully considering the risks and potential rewards is fundamental.\n\nTo get more information on Avorak AI:\n\nWebsite: https://avorak.ai\n\nBuy AVRK: https://invest.avorak.ai/register\n\nDisclaimer: This is a sponsored article, and views in it do not represent those of, nor should they be attributed to, ZyCrypto. Readers should conduct independent research before taking any actions related to the company, product, or crypto projects mentioned in this piece; nor can this article be regarded as investment advice."}]