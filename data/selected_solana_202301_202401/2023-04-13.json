[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiZWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzA0LzEzL3NvbGFuYXMtY3J5cHRvLXJlYWR5LXNhZ2Etc21hcnRwaG9uZS1nb2VzLW9uLXNhbGUtbWF5LTgv0gFpaHR0cHM6Ly93d3cuY29pbmRlc2suY29tL2J1c2luZXNzLzIwMjMvMDQvMTMvc29sYW5hcy1jcnlwdG8tcmVhZHktc2FnYS1zbWFydHBob25lLWdvZXMtb24tc2FsZS1tYXktOC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Apr 2023 07:00:00 GMT", "title": "Solana’s Crypto-Ready ‘Saga’ Smartphone Goes on Sale May 8 - CoinDesk", "content": "The Android smartphone is a gamble on mobile being imperative to the future of crypto, employees at Solana-focused companies told CoinDesk. It was nearly 10 months ago that Solana first teased the radical potential of a cellphone that doubled as a dedicated crypto hardware wallet, and the possibilities such a product could hold for its entire ecosystem."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiNWh0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3NvbGFuYS1sYXVuY2hlcy1zYWdhLXBob25l0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Apr 2023 07:00:00 GMT", "title": "Solana Saga, an Android Phone With a Crypto Twist - Blockworks", "content": "Solana Mobile has introduced its first Android device — the Saga. Customers from the US, EU, UK, Canada, Switzerland, Australia and New Zealand will be able to order the phone beginning May 8.\n\nThe Solana Saga is designed to integrate the Web3 experience onto your mobile phone seamlessly.\n\nIn an interview with Blockworks, <PERSON>, the lead software engineer at Solana Mobile, notes that existing ways to engage with Web3 are still primarily through a big screen, despite mobile phones being the device we interact with most on a daily basis.\n\n“I can’t remember the last time I went to anything but a phone…everything I do, I start with my phone, except for Web3,” <PERSON><PERSON> said.\n\nGoogle and Apple app stores enforce strict rules around their payment systems, and many Web3 apps have to make compromises just to get their app in their app stores. Uniswap’s widely anticipated app was stuck in Apple app store limbo for a month before finally reaching the public Thursday.\n\nThis is where the Solana Saga steps in. A separate Solana app store sits alongside native Android apps. The mobile device is designed with two key technological elements: being useful for a Web3 audience and keeping them safe.\n\nSecurity focus\n\nUsers who purchase a Solana Saga will be prompted to connect their Web3 wallets or create a new seed vault that is integrated into the phone — this is similar to having mobile pay activated on the current crop of phones.\n\nThe seed vault is connected to all dapps which are available for purchase on the Solana app store, enabling transactions to be quickly and securely signed.\n\n“The key custody is built below the layer of the Android — so they have all Android components — and the Android OS that runs on the phone is how we surface all of this information to users, through wallets they’re already familiar with,” he said.\n\nSaga uses the Qualcomm secure execution environment so that the custody of the wallet’s private keys can be removed from the main Android OS.\n\n“Users should have self-custody of their assets, and they need to feel comfortable bringing those assets with them on the go, so we want to make sure you have something that is more secure than a traditional pure software-based self-custody system,” Laver said.\n\nThe is also a secure element processor built into the phone, Laver notes. This means that the seed vault is encrypted in a way that even the phone itself does not have the capability to decrypt private keys without user engagement.\n\n“Whether that be through entering a password or through secure biometric touching the fingerprint sensor, all of that processing happens away from the Android, so if the Android was compromised, all your keys are still held securely away from the operating system,” Laver said.\n\nLaunching for Web3 enthusiasts\n\nAt launch, 16 dapps will be available for download on the Solana app store. These apps include Audius, Dialect, Jupiter Aggregator, Ledger, Magic Eden, Mango Markets, Marinade, and Squads, just to name a few.\n\nThe phone is expected to begin shipping soon after its May 8 launch.\n\nUsers will be able to claim a Saga NFT token as soon as they set up their device, and the first few users will receive a welcome pack to Solana mobile, which will include cryptocurrencies and sticker packs.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiV2h0dHBzOi8vdGVjaGNydW5jaC5jb20vMjAyMy8wNC8xMy9yaW5nLXJpbmctc29sYW5hcy13ZWIzLWZvY3VzZWQtc2FnYS1waG9uZS1pcy1jYWxsaW5nL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Apr 2023 07:00:00 GMT", "title": "Ring ring, Solana’s web3-focused Saga phone is calling - TechCrunch", "content": "About 10 months ago, Solana’s co-founder and CEO, <PERSON><PERSON><PERSON>, announced the launch of Saga, a web3-focused Android smartphone. The phone is now being rolled out, and we got to test one ahead of its launch.\n\nSolana says Saga was launched to make crypto products and services more accessible for users by offering them through a phone instead of the traditional way of accessing crypto platforms and applications, which is via computers.\n\nI had an interesting few days using the Saga as my daily driver. Here’s what I thought about it.\n\nSaga phone at a glance\n\nLet’s get the basics out of the way: The $1,000 device comes packaged in a black box that includes the phone itself, a USB-C charging cable and a physical seed phrase paper card so you can write down your recovery phrase, which is a sequence of random words that you need to access your crypto wallet.\n\nThe phone’s operating system is based on Android 13, and it comes with 512 GB of storage with a 6.67-inch OLED display.\n\nThe setup process is your standard Android experience, involving the usual prompts for setting up your account and internet connection. You can skip most of that and set it up later, but I’d recommend setting up the fingerprint unlock, as it also serves as a verification method for signing crypto transactions.\n\nI used Saga as my work phone for a few days. The phone’s 4011 mAh battery lasted me about 1.5 days starting at 82% on Monday night when I unboxed it to about 4% Wednesday morning. So I’d say it works well as far as battery life is concerned.\n\nThe phone also has a built-in mobile wallet adapter, which is part of the Solana Mobile Stack (SMS) that is invisible to users but is infrastructure that connects dApps to wallets, Steven Laver, the engineering lead at Solana Mobile, told TechCrunch. Similar to how MetaMask has a connect button on its desktop client, this adapter is a “comprehensive technology” that will bridge web dApps to mobile devices, he added.\n\nThe device also includes a web3-focused feature, the Seed Vault, which is embedded into the phone. Users can import a seed phrase from another crypto wallet or create a new one (Solana recommends creating a new one).\n\nAnd that brings us to the core purpose of this phone’s existence: dApps.\n\nA unique experience marred by bugs\n\nUnlike typical Android devices, the Saga has web3 features and a “Solana dApp Store,” where you can find apps for web3 platforms.\n\nThe store has a slim handful of applications:\n\nFour wallets: Phantom, Solflare, Ledger and Squads\n\nThree DeFi apps: Marinade.finance, Jupiter and Mango\n\nThree NFT apps: Nokiamon, Minty Fresh and TIEXO\n\nThree social apps: Dialect, Audius Music and urFeed\n\nAnd the dApp “workspace”\n\nInterestingly, Magic Eden isn’t on the list of dApp stores, even though the marketplace shared plans to partner with the Solana Mobile Stack (SMS) last year when Saga was announced. After publication, Chris Akhavan, chief gaming officer at Magic Eden, told TechCrunch in a tweet its app would be live soon and is currently in a review state.\n\nWhen the phone is released to the public “in the next two weeks” the Solana team is hoping more applications will be added to the dApp store, but Laver did not disclose to TechCrunch how many the store would eventually house.\n\n“We’ve been reaching out and engaging with big players in the Solana ecosystem and those who recognize that mobile has won for everything else,” Laver said. “A lot of developers are realizing mobile will win eventually and they want to be there when it happens.”\n\nSaga owners get $20 worth of USDC and 0.01 SOL, or 0.0026 of a dollar, as part of the dApp store welcome pack. The 0.01 SOL was not enough to play around with some of the applications, so I had a friend send my new crypto wallet a few dollars worth of SOL to test out the dApps.\n\nThe dApp Store currently gives Saga owners rewards, which includes an airdropped Saga genesis token, which is an NFT that provides access to rewards offered by dApps. The token is non-transferable and non-burnable, so users should make sure it’s not connected to a temporary wallet.\n\nWe tested out the Minty Fresh dApp and minted a picture of ourselves for 0.01197 SOL within seconds. It now lives forever on the Solana blockchain. The process itself was easy — and admittedly, fun — but using the phone was not the smoothest experience. The phone was sometimes slow, and I had to close and reopen dApps multiple times because the crypto wallet and NFT minting platform kept freezing.\n\nIt’s worth noting that I faced these issues the whole time I used the device, not just with dApps. I found myself having to close applications, tap the screen a number of times to unlock it or use dApps, or even order myself an Uber.\n\nThat’s not a problem you want to have with a brand new phone.\n\nRollout and future plans\n\nLaver said that while the phone is still in its early stages, there will be software upgrades over time to keep things fresh.\n\nLast June, Yakovenko said the phone would be delivered in Q1 2023, but deliveries have since been pushed to Q2. The phone is available for preorder with a $100 deposit.\n\nA number of major web3 companies like crypto wallet Phantom, NFT marketplace Magic Eden and the now-collapsed crypto exchange FTX shared in June that they were partnering with Solana to help launch the phone, alongside a $10 million developer fund for people who build apps on it.\n\nThis phone is an attempt for web3 to compete with Big Tech providers like Apple, Microsoft and, of course, Android — but many are skeptical of its potential to do so given its niche market.\n\nLast year, Yakovenko said the phone is “targeting the hardcore people who know what self-custody means. You gotta start with a group that loves this product and grow from there.”\n\nOn the other hand, Laver sees this as a device for a bigger crypto audience. He said Saga’s team is planning a marketing push with crypto-native users as well as people who are “crypto curious.”\n\n“We want this to be someone’s phone,” Laver said. “This is the only phone I carry, I don’t have a second phone; it does everything a phone should do plus web3 things.”\n\nLaver didn’t disclose the exact number of phones that were preordered, but said the number was in the “high single digits of thousands.” The phone is certified for sale in 33 countries and the company plans to expand to other countries in the long term, he added.\n\n“We’re in this interesting place where we’re building interesting technologies, but also interested to see where people take it next,” Laver said. “We don’t have a roadmap of A, B or C. We’ve done ‘A’ of building this phone, but we’re excited to see where people take it next for ‘B’ and ‘C.’”"}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiL2h0dHBzOi8vdW5jaGFpbmVkY3J5cHRvLmNvbS9zb2xhbmEtc2FnYS1yZXZpZXcv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Apr 2023 07:00:00 GMT", "title": "Solana Saga Review - Unchained - Unchained", "content": "The Saga, the new phone from Solana Mobile, is only as good as its apps — and there aren’t many to recommend yet.\n\nWhen you think of sagas in the context of crypto, you might imagine soap operas involving SBF or Terra instead of fun-filled adventures and epics. The Saga, the long-awaited phone by Solana Labs subsidiary Solana Mobile, hopes its own story is riveting and has a happy ending. The smartphone, which is now available for pre-order and will retail at $1,000, is meant to be a one-stop shop for users who want to mint NFTs, trade crypto, and navigate web3 on the go.\n\nI spent the last week testing it out. Here’s what I found:\n\nThe Basics\n\nBefore we get to the web3 aspects of the Saga, let’s take a step back and acknowledge that in 2023, we expect phones to do many things. How does the Saga fare as, well, a phone?\n\nPretty well, all told. It makes phone calls! It texts! It takes photos! (Well enough to capture variation within the black coat of my flat-coated retriever.) When I swiped right on this Android phone, I got a semi-personalized rundown of popular news articles and sports scores. Meanwhile, my Google Docs and Drive were always a click away on the home screen.\n\nThe Setup\n\nIt was simple to set up the phone, like any other Android or an iPhone. The notable difference is the Seed Vault, which <PERSON> describes as a “secure custody protocol.” It’s not quite a wallet, but rather works with your Solana wallet (either <PERSON> or <PERSON><PERSON><PERSON><PERSON> at the moment) and the Android OS without giving either “direct access to your secrets.” The main secret is a 24-word seed phrase—not to be confused with the Seed Password, which can be used to sign transactions.\n\nSecurity\n\nThe Saga features a circular, sunken fingerprint sensor two-thirds of the way up the back of the phone. Get to know that area of the phone well—you’ll need it a lot. Though the sensor fit naturally with my grip, my fingerprint must not have been solid; I was continually told to try again.\n\nBecause it syncs to user wallets and seed storage, Saga takes security seriously. However, I found myself quickly getting security fatigue. Since connecting apps or sending transactions often required multiple steps, I was rarely sure what exactly I was saying yes to with any single action. So, I just did what it told me without thinking.\n\nWhile connecting one new app to my wallet, for example, I had to use my fingerprint three times, press connect, confirm that I could use my fingerprint, and then enter my seed passcode for good measure. Just when I thought I was done, I was asked for another authentication, meaning more fingerprinting and button-pushing.\n\nThis experience varies by app and transaction, but it’s unlikely that most users of Saga will distinguish where Saga UX and security ends and where an individual app’s security begins.\n\nWallet\n\nPhantom is the most popular Solana wallet, so I signed up with that. But the app kept ghosting me, freezing up as I tried to press buttons and forcing me to close it out and reopen it.\n\nNonetheless, Phantom integrated reasonably well with the other apps. After minting an NFT of my dog on Minty Fresh, the asset popped into my wallet just as you would expect. I could even easily list it on Magic Eden. (But since the Magic Eden app wasn’t yet available for review, I couldn’t gauge what I should price it at. My dog is lovely, so probably $10,000 in SOL.)\n\nThe Apps\n\nThough Google Play appears on the home screen, the Saga is understandably pushing web3 apps via its Solana dApp Store, which also comes preloaded on the home screen.\n\nAlso preloaded: 20 USDC and 0.01 SOL to get started. (Though the SOL is meant to cover transactions, I had to convert some of my USDC to mint an NFT.)\n\nWhile the cash is nice, I suspect it’s superfluous. Solana Mobile states in its materials: “The first cohort of Saga users will likely have some experience with asset self-custody, buying and selling NFTs, signing transactions, and other aspects of interacting with the Solana blockchain.” In other words, the initial target user will be able to figure out how to get SOL on their phone in a snap.\n\nAs of this review, 14 dApps are available, including the Phantom and Solflare wallets. They include DeFi asset swap Jupiter; Minty Fresh, an app for turning your photos into NFTs; NFT marketplace TIEXO; hardware wallet Ledger Live; and web3 music streaming app Audius.\n\nWhile I found the selection underwhelming, I recognized that the Saga has to start somewhere. It’s built the marketplace. Now developers need to come build on it.\n\nAudius\n\nI started by downloading Audius and creating an account (with my email because there was no wallet option). To get started, I needed to select three artists so the app could understand my preferences. Audius isn’t Spotify; there’s no Taylor Swift or Fleetwood Mac. I listed the only artists I had heard of, ODESZA and web3 mainstay RAC, and picked a random third.\n\nIt didn’t matter. As it was ostensibly creating my account, the app showed a spinner. It never went further; four days later, I still couldn’t sign in to Audius.\n\nSo, that was a bummer.\n\nWorkspace\n\nThe next app didn’t go any better. I tried to get onto Synesis One’s Workspace app despite little information about what it was—while the other apps fall under the category Social, NFTs, DeFi, or Wallets, Workspace is listed only under DApps, and it provides no description. I may never know what it does. After trying to connect via my wallet, I was told I needed to make changes in the developer settings. After I did, the app crashed.\n\nDialect\n\nDialect, which took a long time to connect due to security measures, actually let me in. Once I was there, it granted me a sticker pack exclusive to Saga owners. Unfortunately, while the app said the pack was on the way, I was still waiting hours later. I thought Solana was speedier than Ethereum, so this was likely a Dapp issue as well.\n\nTIEXO\n\nUnlike some other apps, I found it easy to get on TIEXO immediately: just a single fingerprint authentication and a push of a button to connect my wallet. That said, I wish the app itself were more dynamic. While there’s a filter button, there’s no way to look for NFTs by the price until you’ve decided on a collection. But, hey, it worked!\n\nConclusion\n\nThe Saga is a workable blockchain phone with the built-in security many crypto users will check for. But there are a couple of questions potential buyers must ask. First, are they committed solely to the Solana ecosystem for the foreseeable future?\n\nSecond, can they be patient with app development? Springing for a Saga is like buying a house with lots of guest rooms before you have any friends. There’s space for everybody—but no one you want to have over yet.\n\nNonetheless, Saga represents a step forward for the network after several steps backward: It dropped out of the top 10 in market cap, received unwanted attention for network outages, and watched the arrest of its biggest supporter, Sam Bankman-Fried. This phone gives developers a solid starting point for building the network back into prominence.\n\nEditor’s Note: An earlier review incorrectly stated that Google Play wasn’t available on the home screen."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiRGh0dHBzOi8vZGVjcnlwdC5jby8xMjYyMTgvc29sYW5hLXNhZ2EtcmV2aWV3LXdlYjMtc21hcnRwaG9uZS1hcnJpdmVk0gFKaHR0cHM6Ly9kZWNyeXB0LmNvLzEyNjIxOC9zb2xhbmEtc2FnYS1yZXZpZXctd2ViMy1zbWFydHBob25lLWFycml2ZWQ_YW1wPTE?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Thu, 13 Apr 2023 07:00:00 GMT", "title": "Solana Saga Review: The Web3 Smartphone Has Arrived - Decrypt", "content": "Decrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nIt’s here: Solana really did make its own Web3 smartphone, the Saga—and it’s rolling out now to people who pre-ordered, with new orders set to begin May 8.\n\nDecrypt has had its hands on the $1,000 Android flagship device for the past week—and right now, Solana Labs’ “moonshot” bet feels mostly like a Web2 device with a splash (albeit a meaningful one) of Web3 integration. It’s a capable, powerful phone with native, secure wallet functionality that feels tight and intuitive, but with few available apps plus some hitches along the way.\n\nEven more so than usual in Web3, using the Saga today feels like beta testing something with promise and potential. That may be an intriguing premise for early adopters and die-hards, and a potential catalyst for developers, but potentially a tough sell for the average consumer—and Solana Labs knows it. But there’s a lot to like here, even if it feels a bit early.\n\nBuild and specs: polished and premium\n\nAD\n\nAD\n\nAs a longtime tech reviewer and someone who always wields a top-end handset, one of my key considerations when evaluating a phone is whether I could live with it on a day-to-day basis. In other words, would I be happy carrying this thing in my pocket as I stay connected, snap photos, work remotely, and remain perpetually online?\n\nLuckily, the Solana Saga meets that requirement. This is a premium Android flagship with an attractive (but not overly flashy) design, built with sturdy materials and packed to the gills with just about everything you’d need to communicate, stay connected, and be entertained. Even without the Web3 element, this would be a very good Android phone.\n\nThe Solana Saga in the wild. Image: Decrypt\n\nAt a glance, the Saga is less distinctive than the Samsung Galaxy S23 or recent iPhones, but little accents help set it apart—the metallic green side buttons and rounded triangular titanium camera module on the back, especially. The stainless steel frame and ceramic backing are nice touches, too, even if the matte black rear finish is a fingerprint and smudge magnet.\n\nIt’s a big boy, too—slightly taller and heavier than my iPhone 14 Pro Max, with a bright 6.67” 1080p AMOLED screen with a smooth 120Hz display. Battery life is pretty robust and reliable here with a beefy 4,110mAh cell, with wireless charging available for convenience alongside a faster wired USB-C connection.\n\nThe Saga's ceramic backing is a smudge magnet. Image: Decrypt\n\nThe Saga is plenty fast and powerful too, thanks to a flagship-level Qualcomm Snapdragon 8+ Gen1 processor paired with 12GB RAM, making it capable of running games and demanding apps with ease. That said, it’s not the top-of-the-line chip: the Galaxy S23 and other new phones run a Gen2 chip that delivers solid performance gains in benchmark testing.\n\nAD\n\nAD\n\nYou’re unlikely to notice the difference now, but the Saga could be less resilient than some 2023 rivals in the future as more demanding apps and games launch. On the upside, however, the Saga is packed with a robust 512GB of internal storage as standard—most phones require an upcharge to get you that kind of onboard storage.\n\nSnapping with the Saga. Image: Decrypt\n\nThe dual-camera setup, with a 50-megapixel main shooter and 12MP ultrawide camera alongside, met my needs for casual everyday snaps, delivering crisp details and punchy colors. Put side by side with the iPhone 14 Pro Max, however, Apple’s shots were routinely sharper, with more natural color balance.\n\nAside from the Web3 elements, the Saga ships with a thankfully clean Android 13 build—no clunky skin, no bloatware junk—and the full suite of Google services, including the Play Store. And this 5G phone works with all major U.S. carriers, so you can take it where you please.\n\nWeb3 on the go\n\nThat’s the standard, “Web2” side of the Solana Saga experience—and if you plan to use it as an everyday phone, that’s really the most important piece of the puzzle. But it’s the Web3 integration that sets the Saga apart from your typical smartphone, and the key reason why anyone would consider the Saga over a standard flagship.\n\nThe Saga’s Web3 offering is built around the Seed Vault, a native custody solution that secures your wallet’s seed phrase within a secure environment on the phone. Even the Android operating system doesn’t have access, and it’s tied to your biometric signature via the rear fingerprint sensor.\n\nThat’s paired with a native store for decentralized apps (dapps), which has just over a dozen dapps right now but provides a smoother, easier-to-use flow than typical web apps. Using a wallet app like Phantom or Solflare, you can sign Solana network transactions with a touch of the sensor, along with a tap of the screen and occasional PIN entry for good measure.\n\nSetting up a wallet and getting started is relatively straightforward here thanks to a simple onboarding process, and within minutes, I was swapping USDC for Solana (SOL) via the Jupiter swap aggregator dapp and buying NFTs through the Tiexo marketplace. I took a photo amid the mayhem of Times Square and then immediately minted it as an NFT via the Minty Fresh dapp.\n\n(Note: As with early consumer units, our Solana Saga review loaner came with $20 worth of USDC and 0.01 SOL—about $0.25 worth—for network fees. Decrypt will return the phone and all unused funds, along with any purchased or minted assets included, to Solana Labs.)\n\nAD\n\nAD\n\nClaiming the Saga genesis token with benefits for early adopters, and buying an NFT via the Tiexo app. Image: Decrypt\n\nWallets can be used to connect to web-based Web3 apps, as well, via Solana’s mobile wallet adapter (MWA) technology. For example, the native Magic Eden dapp isn’t yet in the Saga dapp store as of this writing, but I connected to the web version of the marketplace and bought an NFT via Phantom and my fingerprint.\n\nBut there are some stumbles along the way. The native Audius streaming music dapp wouldn’t connect to my Solana mainnet wallet, and instead tried to connect via devnet. A social app (urFeed) simply would not download from the dapp store for days no matter how many times I tapped the button. And the MWA link didn’t always work, such as when I tried to connect my wallet to the Aurory game’s website… and nothing happened.\n\nTransacting on the Solana Saga. Image: Decrypt\n\nAnd broadly, while the essential core pieces are here to send and receive funds, mint and transact NFTs, and swap crypto funds, I was really hoping for more entertaining and engrossing Web3 experiences that really utilized the tech.\n\nSolana has a growing gaming scene, for example, but the dapp store currently has no playable titles. And the web-based Solana games that I tried didn’t work—shooter Mini Royale: Nations refused to load, and Aurory wouldn’t connect to my wallet. A Solana Labs rep told Decrypt that “a few games” are in the works for Saga and “coming soon,” however.\n\nFor early adopters\n\nDespite the bugs and hitches, transacting with crypto and NFTs on the Solana Saga feels more intuitive than using a web-based wallet. Handling crypto assets on mobile is typically a fraught and frustrating experience, but the Saga’s UX is undoubtedly a step forward—and at this point, best described as a starting point for Solana’s mobile ambitions.\n\nSolana Labs’ bet on mobile is admirable: it’s an attempt to cultivate a decentralized mobile ecosystem that isn’t reliant on big tech juggernauts like Apple and Google and their 30% cut of app sales, which not only impacts developers but has stymied mobile Web3 adoption.\n\nIt’s a big swing, but also a big risk—and a potentially expensive one for Solana Labs if the Saga doesn’t find a large enough audience to support the mobile dapp ecosystem. Solana co-founder Anatoly Yakovenko has previously described the Saga as a “developer play” and said he’d be “very happy” to sell tens of thousands of units in the first year.\n\nThat level of sales wouldn’t move the needle for a Web2 tech giant, but if it prompts developers to build mobile dapps and embrace the platform, then perhaps it will lead to more affordable Saga successors targeted at a broader audience—with more compelling reasons to buy in.\n\nAD\n\nAD\n\nSolana Saga: Verdict\n\nA $1,000 smartphone certainly isn’t for everyone; nor is the Saga’s emphasis on Web3 use cases. But among its peer group of expensive smartphones, the Saga is a pretty sharp offering—and its Solana integration is an exciting and promising twist that could ultimately pay off for users as a mobile hub for decentralized apps and services. But that’s an open question for now."}]