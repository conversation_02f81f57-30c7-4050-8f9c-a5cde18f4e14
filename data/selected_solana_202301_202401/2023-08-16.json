[{"id": 9, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vd3d3LmFuYWx5dGljc2luc2lnaHQubmV0L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L3NvbGFuYS1zb2wtcHJpY2UtbG9zZXMtc3RyZW5ndGgtYXMtYWx0Y29pbnMtY29sbGFwc2XSAYABaHR0cHM6Ly93d3cuYW5hbHl0aWNzaW5zaWdodC5uZXQvYW1wL3N0b3J5L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L3NvbGFuYS1zb2wtcHJpY2UtbG9zZXMtc3RyZW5ndGgtYXMtYWx0Y29pbnMtY29sbGFwc2U?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 16 Aug 2023 07:00:00 GMT", "title": "Solana (SOL) Price Loses Strength As Altcoins Collapse - Analytics Insight", "content": "For Solana price prediction to flip bullish on a higher timeframe, it needs to gain strength above $27. This would open the doors for a major upside rally targeting the swing highs of $48. However, such a positive price action can only be expected if BTC breaks above the $31,500 level in the coming weeks. Currently, SOL seems to be heading for another retest of the range lows, which is around $20.31."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vd3d3LmFuYWx5dGljc2luc2lnaHQubmV0L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L3NvbGFuYS1zb2wtcHJpY2UtbG9zZXMtc3RyZW5ndGgtYXMtYWx0Y29pbnMtY29sbGFwc2XSAYABaHR0cHM6Ly93d3cuYW5hbHl0aWNzaW5zaWdodC5uZXQvYW1wL3N0b3J5L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L3NvbGFuYS1zb2wtcHJpY2UtbG9zZXMtc3RyZW5ndGgtYXMtYWx0Y29pbnMtY29sbGFwc2U?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 16 Aug 2023 07:00:00 GMT", "title": "Solana (SOL) Price Loses Strength As Altcoins Collapse - Analytics Insight", "content": "For Solana price prediction to flip bullish on a higher timeframe, it needs to gain strength above $27. This would open the doors for a major upside rally targeting the swing highs of $48. However, such a positive price action can only be expected if BTC breaks above the $31,500 level in the coming weeks. Currently, SOL seems to be heading for another retest of the range lows, which is around $20.31."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2xpZ2h0c3BlZWQteWFrb3ZlbmtvLWxheWVyLTItc2NhbGluZ9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 16 Aug 2023 07:00:00 GMT", "title": "Launching a bunch of layer-2's is not a viable solution to blockchain scaling, says Solana's Ya<PERSON><PERSON> - Blockworks", "content": "Polygon, Arbitrum, Optimism, Base…the list of layer-2 solutions goes on — and seems to be getting longer.\n\nSolana co-founder, <PERSON><PERSON><PERSON>, believes that most of the layer-2 chains emerging, primarily on Ethereum, won’t serve as sustainable scaling solutions in the long term.\n\n“They fragment the user base. So the UX becomes very, very complicated.”\n\nOn the Lightspeed podcast (Spotify/Apple), <PERSON><PERSON><PERSON> gives an example from his experience working in Web2 company, Dropbox. “We had one giant MySQL database for all the folders because as soon as you start fragmenting them, [it] becomes very hard to create links between different users of different folders.”\n\n“Tracking consistency between two different databases is a major pain in the ass. You would have to synchronize everything through the [layer-1].”\n\nThe fragmentation, at a large enough scale, causes “massive composability” and user experience problems, according to <PERSON><PERSON><PERSON>. “You have to re-sync through the [layer-1] and that’s going to create the same kind of cost.”\n\n“That’s very, very difficult to deal with,” he says.\n\n<PERSON><PERSON><PERSON> uses the example of NFTs to illustrate the problem. “You can’t have an NFT in every rollup. It actually can only be bridged to one,” he says. “If I want a specific NFT, the marketplace where it’s sold is the marketplace that I need to buy in — and it can only exist in one of those.”\n\nBy creating different states, layer-2s break up the composability of marketplaces for a particular NFT, <PERSON>koveko explains. “That’s the fundamental challenge with [layer-2s].”\n\nIt’s an unavoidable trade-off, he says. “You get a performance benefit because you create this single global lock around a state and then you can work on it asynchronously from everything else, but you’re creating a very hard composability challenge.”\n\nWhat about just one layer-2?\n\nTheoretically, a singular layer-2 solution, rather than the current variety that exists in the Ethereum ecosystem, would simplify the composability problem, Yakovenko says.\n\n“You take [Solana Virtual Machine] that runs as many things in parallel as it can,” he explains. “It always increases hardware capacity to meet the demand.”\n\n“Then, you take all the data and you dump it into danksharding” which ideally would be, he says, the “perfect implementation of a [data availability] layer that tries to maximize its [data availability] system.”\n\n“So you have one [layer-2], one bandwidth-optimized system. That’s basically what Solana is,” he chuckles. “Solana itself can asynchronously execute all the programs, and then pick forks separately in a separate pipeline from execution of the programs.”\n\n“All the forks are quickly picked,” he says, “but then these bigger systems can execute on the programs. You can then do batch [zero-knowledge] verification, if that’s what you need. All that stuff is all doable on Solana.”\n\nYakovenko notes that a “fundamental difference” persists between Solana and multi-layered chain solutions. “There’s not going to be a demand for Solana to do danksharding because that really breaks that idea of information synchrony across the world.”\n\n“I don’t want to split the data availability,” he says. “Even though you might be able to get an improvement in bandwidth there, it actually does still have a trade-off.”\n\nYakovenko believes in the necessity of a system that actively avoids asynchronous solutions. “When I submit one bit here,” he says, “it’s equally observed in Singapore and Brazil, everywhere in the world, as fast as physics allow, because that actually creates value for the world.”\n\nThe goal, Yakovenko explains, is to minimize “information asymmetry between any two players.”\n\n“It allows for fair markets to exist,” he says. “That’s the one core use-case that you really can’t optimize away with all these other systems.”\n\n“The idea of having a single rollup that can take up all of the bandwidth of the data availability layer, whether it’s Solana or Celestia or Ethereum,” he says, “that idea is something that I think a lot of the engineers agree is probably the more efficient design.”\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2xpZ2h0c3BlZWQteWFrb3ZlbmtvLWxheWVyLTItc2NhbGluZ9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 16 Aug 2023 07:00:00 GMT", "title": "Launching a bunch of layer-2's is not a viable solution to blockchain scaling, says Solana's Ya<PERSON><PERSON> - Blockworks", "content": "Polygon, Arbitrum, Optimism, Base…the list of layer-2 solutions goes on — and seems to be getting longer.\n\nSolana co-founder, <PERSON><PERSON><PERSON>, believes that most of the layer-2 chains emerging, primarily on Ethereum, won’t serve as sustainable scaling solutions in the long term.\n\n“They fragment the user base. So the UX becomes very, very complicated.”\n\nOn the Lightspeed podcast (Spotify/Apple), <PERSON><PERSON><PERSON> gives an example from his experience working in Web2 company, Dropbox. “We had one giant MySQL database for all the folders because as soon as you start fragmenting them, [it] becomes very hard to create links between different users of different folders.”\n\n“Tracking consistency between two different databases is a major pain in the ass. You would have to synchronize everything through the [layer-1].”\n\nThe fragmentation, at a large enough scale, causes “massive composability” and user experience problems, according to <PERSON><PERSON><PERSON>. “You have to re-sync through the [layer-1] and that’s going to create the same kind of cost.”\n\n“That’s very, very difficult to deal with,” he says.\n\n<PERSON><PERSON><PERSON> uses the example of NFTs to illustrate the problem. “You can’t have an NFT in every rollup. It actually can only be bridged to one,” he says. “If I want a specific NFT, the marketplace where it’s sold is the marketplace that I need to buy in — and it can only exist in one of those.”\n\nBy creating different states, layer-2s break up the composability of marketplaces for a particular NFT, <PERSON>koveko explains. “That’s the fundamental challenge with [layer-2s].”\n\nIt’s an unavoidable trade-off, he says. “You get a performance benefit because you create this single global lock around a state and then you can work on it asynchronously from everything else, but you’re creating a very hard composability challenge.”\n\nWhat about just one layer-2?\n\nTheoretically, a singular layer-2 solution, rather than the current variety that exists in the Ethereum ecosystem, would simplify the composability problem, Yakovenko says.\n\n“You take [Solana Virtual Machine] that runs as many things in parallel as it can,” he explains. “It always increases hardware capacity to meet the demand.”\n\n“Then, you take all the data and you dump it into danksharding” which ideally would be, he says, the “perfect implementation of a [data availability] layer that tries to maximize its [data availability] system.”\n\n“So you have one [layer-2], one bandwidth-optimized system. That’s basically what Solana is,” he chuckles. “Solana itself can asynchronously execute all the programs, and then pick forks separately in a separate pipeline from execution of the programs.”\n\n“All the forks are quickly picked,” he says, “but then these bigger systems can execute on the programs. You can then do batch [zero-knowledge] verification, if that’s what you need. All that stuff is all doable on Solana.”\n\nYakovenko notes that a “fundamental difference” persists between Solana and multi-layered chain solutions. “There’s not going to be a demand for Solana to do danksharding because that really breaks that idea of information synchrony across the world.”\n\n“I don’t want to split the data availability,” he says. “Even though you might be able to get an improvement in bandwidth there, it actually does still have a trade-off.”\n\nYakovenko believes in the necessity of a system that actively avoids asynchronous solutions. “When I submit one bit here,” he says, “it’s equally observed in Singapore and Brazil, everywhere in the world, as fast as physics allow, because that actually creates value for the world.”\n\nThe goal, Yakovenko explains, is to minimize “information asymmetry between any two players.”\n\n“It allows for fair markets to exist,” he says. “That’s the one core use-case that you really can’t optimize away with all these other systems.”\n\n“The idea of having a single rollup that can take up all of the bandwidth of the data availability layer, whether it’s Solana or Celestia or Ethereum,” he says, “that idea is something that I think a lot of the engineers agree is probably the more efficient design.”\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiM2h0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3MvaG93LXRvLWJyaWRnZS10by1uZW9uLWV2bdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 16 Aug 2023 07:00:00 GMT", "title": "How to Bridge to Neon EVM? (2023 Guide) - Watcher Guru", "content": "How to Bridge to Neon EVM: A Seamless Integration of Solana and Ethereum\n\n‍Neon EVM, a runtime environment on the Solana blockchain, offers a unique opportunity to bridge assets from Ethereum, Solana, and other networks.\n\nNeon EVM merges Ethereum’s smart contracts with Solana’s speed for efficient dApp experiences. It’s a powerful combo!\n\nDiscover how to integrate Solana and Ethereum through Neon EVM with benefits, fees, and integration steps.\n\nAlso read: What Can You Buy with Bitcoin?\n\nBridging Assets to Neon EVM: A Seamless Integration\n\nWith DeFi’s rise, cross-blockchain interoperability gains significance for seamless interaction between diverse networks.\n\nNeon EVM enables Ethereum-like transactions on Solana, utilizing the native functionality of Solana’s blockchain.\n\nIntegration grants dApps Solana’s benefits: low fees, fast transactions, high throughput, and access to its expanding market.\n\nAdditionally, to facilitate the seamless integration of Solana and Ethereum, Neon EVM employs an intermediary proxy server that wraps up Ethereum-like transactions into Solana transactions.\n\nThese transactions are then executed in parallel by Neon EVM, ensuring the efficient execution of smart contracts.\n\nFurthermore, this approach allows any Ethereum application, including popular ones like UniSwap, SushiSwap, 0x, and MakerDAO, to be run on Solana without any changes to the existing codebase.\n\nMoreover, key tools for Ethereum dApps such as Solidity, Metamask, Remix, Truffle, and others are fully compatible with Neon EVM, enabling developers to leverage their existing skill sets.\n\nAlso read: What Are NFT Royalties?\n\nThe Advantages of Neon EVM\n\nIntegrating Solana and Ethereum through Neon EVM offers several advantages for developers and users alike. Let’s explore these benefits in more detail:\n\n1. Low Gas Fees and Transaction Costs\n\nOne of the primary advantages of Neon EVM is the ability to leverage the low gas fees and transaction costs native to the Solana blockchain.\n\nAdditionally, Ethereum has been known for its high gas fees, which can limit the scalability and affordability of dApps.\n\nBy bridging assets to Neon EVM, developers can take advantage of Solana’s cost-effective transactions, providing a more accessible and affordable experience for users.\n\n2. Parallel Execution on Solana\n\nNeon EVM enables parallel execution of smart contracts on the Solana blockchain. This parallel processing capability allows for faster transaction confirmation times and improved scalability, as multiple transactions can be executed simultaneously.\n\nBy harnessing Solana’s high transaction speed and throughput, Neon EVM provides a seamless and efficient experience for dApps, ensuring that the ecosystem can handle increased demand without sacrificing performance.\n\n3. Access to the Solana Market\n\nDevelopers gain access to the growing Solana market by bridging assets to Neon EVM.\n\nFurthermore, Solana has gained significant traction recently, attracting a vibrant community of developers and users.\n\nBy integrating with Solana through Neon EVM, developers can tap into this liquidity and reach new customers, expanding their user base and potentially unlocking new growth opportunities.\n\nThe Neon EVM Bridging Process\n\nSource: Medium\n\nNow that we have explored the advantages of bridging assets to Neon EVM, let’s dive into the step-by-step process of how to bridge assets from Ethereum, Solana, or other networks.\n\nStep 1: Choose a Neon-Compatible Bridge\n\nTo bridge assets to Neon EVM, you must select a Neon-compatible bridge that enables the seamless transfer of assets between different networks. One such bridge is NeonPass, which stands out as a preferred choice due to its high security, low transfer fees, and compatibility with a wide range of tokens such as WETH, USDC, SOL, and more.\n\nStep 2: Connect Your Wallet\n\nVisit the NeonPass website (https://neonpass.live/) and connect your preferred wallets, such as MetaMask or Phantom. This connection lets you securely transfer assets between Ethereum, Solana, and Neon EVM.\n\nStep 3: Select the Chains and Bridging Token\n\nOnce you have connected your wallet, select ‘Ethereum’ and ‘Neon EVM’ as your two chains. Choose a bridging token that represents the asset you wish to transfer. NeonPass supports a variety of tokens, ensuring a seamless bridging experience.\n\nStep 4: Approve and Transfer Funds\n\nEnter the number of assets you want to bridge and click ‘Approve’ to initiate the transfer process. Follow the instructions provided by your wallet, and your funds will be swiftly transferred to Neon EVM on the Solana blockchain.\n\nUnderstanding the Fees\n\nBridging assets between different blockchain networks may involve certain fees. However, NeonPass is recognized as one of the most cost-efficient platforms available.\n\nThe total cost to transfer assets from Solana to Neon EVM via NeonPass typically falls within $5-$10 USD. It’s important to note that gas fees may vary based on the prevailing rates at the transfer time.\n\nConclusion\n\nIn conclusion, Bridging assets to Neon EVM offers developers and users a seamless integration of Solana and Ethereum ecosystems.\n\nBy leveraging Neon-compatible bridges like NeonPass, users can securely transfer assets between Ethereum, Solana, and other networks, while taking advantage of Solana’s low gas fees, high transaction speed, and high throughput.\n\nNeon EVM enables parallel execution of smart contracts on the Solana blockchain, providing an efficient and cost-effective decentralized application experience.\n\nAre you a developer wanting Solana’s liquidity? End-users find affordable dApps via Neon EVM asset bridging. Explore possibilities!\n\nNeon EVM isn’t just a bridge; it’s a powerful Solana-Ethereum tool promoting openness, innovation, and diversity. Remember that!\n\nSo, leap and bridge your assets to Neon EVM to unlock the full potential of decentralized finance on the Solana blockchain."}]