[{"id": 0, "url": "https://news.google.com/rss/articles/CBMiQ2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3NvbGFuYS13ZWxjb21lcy1pbnN0aXR1dGlvbmFsLW1wYy13YWxsZXTSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 26 Jun 2023 07:00:00 GMT", "title": "Solana welcomes institutional MPC wallet - Blockworks", "content": "Institutional MPC wallet company Fordefi will integrate its native wallet solution with applications and protocols built on Solana.\n\nUnlike wallets owned by individual accounts, institutional wallets must be able to provide access to multiple different stakeholders.\n\nMPC stands for multi-party computation and is a type of wallet security that allows multiple parties to have access to the same wallet without revealing any personal information to one another.\n\n“As tokenization continues to proliferate and mature, it will be imperative to have infrastructure products like the Fordefi platform that enable institutions to efficiently and intuitively manage their assets,<PERSON> <PERSON>, head of business development at Solana Foundation, said in a press release reviewed by Blockworks.\n\nAlthough Solana already has different types of MPC wallets integrated with its ecosystem, <PERSON><PERSON>, Fordefi co-founder and chief technology officer, told Blockworks that simply being an MPC wallet is not enough.\n\nFordefi has advanced security features designed specifically for institutions, he explains. An example of this is Fordefi’s ability to simulate transactions and independently verify contract addresses.\n\n“Whenever an institution does a transaction, we simulate the transactions and show the different effects that happen as a result of this transaction,” he said.\n\nThis means that before signing off on a transaction, the signer will know exactly which smart contract the transaction is going to, the vault, the person who is transacting, the value of the token, and its associated gas fees. Risks for each transaction will also be displayed during a transaction simulation.\n\n“We use all these bits and pieces about the transaction in our policy engine to define the approval process, which is a basis for institutional wallets,<PERSON> <PERSON><PERSON> said.\n\nAnother feature that differentiates Fordefi’s wallet is the product’s policy management tool, which gives institutions the ability to set permissions and approvers, Crest Saechao, the vice president of marketing at Fordefi, told Blockworks.\n\n“The native solution — which all other wallets implement — has a very short timeout for transactions from the moment the dapp creates them, which is incompatible with an often-lengthy institutional approval process,” Saechao said.\n\nSome of Fordefi’s existing institutional clients include Keyrock and DeFiance Capital, who have chosen the product for its additional safety layers.\n\n“Fordefi stands out in the industry with its strong focus on innovation, DeFi security, and education,” Arthur Cheong, chief information officer and founder at DeFiance Capital, told Blockworks. “They actively work to enhance the ecosystem, drive institutional adoption, and ensure participant safety.”\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vZGVjcnlwdC5jby8xNDYyODYvc29sYW5hLXBvbHlnb24tY2FyZGFuby1zdGlsbC1yZWNvdmVyaW5nLWZyb20tdGhlLXNlYy1jYWxsaW5nLXRoZW0tc2VjdXJpdGllc9IBbGh0dHBzOi8vZGVjcnlwdC5jby8xNDYyODYvc29sYW5hLXBvbHlnb24tY2FyZGFuby1zdGlsbC1yZWNvdmVyaW5nLWZyb20tdGhlLXNlYy1jYWxsaW5nLXRoZW0tc2VjdXJpdGllcz9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 26 Jun 2023 07:00:00 GMT", "title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> Still Recovering From the SEC Calling Them Securities - Decrypt", "content": "We do the research, you get the alpha! Get exclusive reports and access to key insights on airdrops, NFTs, and more! Subscribe now to Alpha Reports and up your game! Go to Alpha Reports\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nSolana, Polygon, Cardano, and other tokens being dragged into the court battles by the Security and Exchange Commission (SEC) have lost 15%, or $5 billion, off their collective market capitalizations, according to a Decrypt analysis of CoinGecko data.\n\nOn June 5 and June 6, the SEC filed lawsuits against Binance and Coinbase—two of the largest crypto exchanges in the industry. Among the charges leveled at the two exchanges were allegations they were selling unregistered securities. The industry wasted no time calling the declarations “pretty unfair,” But that hasn't stopped the tokens from taking some collateral damage.\n\nAccording to the SEC’s lawsuits against Binance and Coinbase, a handful of cryptocurrencies were named, including: Solana (SOL), Cardano (ADA), Polygon (MATIC), Filecoin (FIL), Cosmos Hub (ATOM), The Sandbox (SAND), Decentraland (MANA), Algorand (ALGO), Axie Infinity (AXS), and COTI (COTI).\n\nAD\n\nAD\n\nOn June 12, a week after the SEC lawsuits were filed, Messari Crypto published its Ecosystem Brief: Rollup Specialization report. Messari's emerging markets category, which includes many of the projects labeled securities by the SEC, showed a 25% drop since the lawsuits were filed. But a handful have already started to make a recovery.\n\nFilecoin and Algorand are both within 2% of the market capitalizations they had when the lawsuits were filed. The Cosmos ATOM token has a 5% gap to close. The rest haven't been as lucky.\n\nBinance has been suffering an especially harsh regulatory clampdown around the world, with authorities closing in on the platform in several countries. That's likely why its exchange utility token, BNB, has seen its market capitalization drop 21% in value, going from $47 billion to $37 billion since the SEC lawsuit was filed.\n\nCharles Hoskinson’s Cardano also saw its market capitalization plummet. The 8th largest asset on Coingecko is currently trading at $0.29, with a market cap of $10 billion—down 17% from when the SEC lawsuits were filed.\n\nPolygon has struggled the most to recover from the blow dealt by the SEC. Today it holds roughly $6 billion in market value, down 20% from the $8 billion market cap it had at the start of th emoney. The token, according to Coingecko, trades at $0.66 today.\n\nAD\n\nAD\n\nSome assets marked by Messari as emerging tokens were not included in the SEC’s lawsuit, but still suffered losses.\n\nAvalanche (AVAX), albeit one of the biggest winners in last week’s bullish rally, has seen its market cap drop roughly 6% since the lawsuits were filed and is currently sitting at $4.6 billion. Optimism (OP), on the other hand, shed 17.5% of its total market cap over the same period and was sitting at $855 million as of writing.\n\nKeep in mind, however, that despite these important drops in token prices, the crypto market has been on a bullish rally over the past week. The BlackRock Rally, so-named due to the traditional finance titan’s filing for a spot Bitcoin ETF last week, pushed Bitcoin, the number one cryptocurrency, over $31,000 for the first time in months, raising the tide for the majority of tokens."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMia2h0dHBzOi8vY3J5cHRvbmV3cy5jb20vbmV3cy9zb2xhbmEtcHJpY2UtcHJlZGljdGlvbi1hcy1zb2xhbmEtbW9iaWxlLXNhZ2EtbGF1bmNoZXMtY2FuLXNvbC1yZWFjaC0xLTIwMjMuaHRt0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 26 Jun 2023 07:00:00 GMT", "title": "Solana Price Prediction as Solana Mobile Saga Launches – Can SOL Reach $1,000 in 2023? - Cryptonews", "content": "Solana Price Prediction as Solana Mobile Saga Launches – Can SOL Reach $1,000 in 2023?\n\nSolana. Source: Adobe\n\nNews that Solana Labs, the company behind the development of the smart-contract-enabled, high-performance Solana blockchain, will launch its crypto-integrated Android mobile phone on the 8th of May has failed to boost the Solana (SOL) price on Friday.\n\nAfter hitting fresh highs since mid-February in the mid-$25s per token earlier in the session, SOL was last changing hands just above the $24 level, down a little over 1.0% on the day.\n\nSolana’s web3-focused mobile is called Saga and pre-ordered devices are already being shipped. The highs specification phone will costs around $1,000 and duplicate as a hardware wallet.\n\nThe modest pullback in the SOL price comes back after Bitcoin and Ether also fall back slightly from multi-month highs hit earlier in the session above the $31,000 and $2,100 levels respectively.\n\nMajor cryptocurrencies have been performing well recently, with Solana still up over 14% this month and the likes of Bitcoin and Ether also nursing impressive recent gains.\n\nFriday’s pullback should thus be viewed as little more than some short-term profit-taking.\n\nPrice Prediction – Where Next for Solana (SOL)?\n\nDespite still trading about 10% down versus earlier annual highs around $27, Solana’s outlook looks upbeat.\n\nThe cryptocurrency recently broke to the north of an important technical structure that has been in play since last August and also broke convincingly above a cloud of resistance in the form of its major moving averages, all of which currently sit in the $21-22 area.\n\nIf SOL can now break above the key long-term support-turned-resistance zone in the $26-27 area, a test of last September and November’s highs in the $39 area become a strong possibility, which would mark a further 60% gain from current levels.\n\nIf SOL/USD could push above here, a retest of last August highs around $48 would be on the cards, marking a further 25% in potential gains.\n\nCheck the latest SOL price on CoinCodex.\n\nCan Solana (SOL) Hit $1,000 in 2023?\n\nIts not just the technicals that are looking good for Solana.\n\nWhile the Solana blockchain’s daily revenues remain a tad lower versus their pre-FTX collapse levels, they remain at healthy levels, as do daily active user numbers.\n\nAs the broader crypto bull market continues, more and more users are likely to flock back to the network as demand for Decentralized Finance (DeFi) returns.\n\nAnd, at this point, with Bitcoin and Ethereum both up in the 70-80% region for the year, most are expecting the crypto bull market to continue.\n\nThat’s largely thanks to expectations that macro conditions will continue to become more favorable – US inflation continues to drop and the economy is weakening, raising the risk of a recession later this year and increasingly the likelihood that the Fed soon embarks on an interest rate cutting cycle.\n\nIf Solana was to break to the north of resistance around $48 in the coming months, that would open the door to a rapid potential run higher towards $76, which would mark gains of more than 3x from current levels.\n\nBut can Solana hit new all-time highs above $259 in 2023?\n\nCould it even hit $1,000 per token this year?\n\nWell, the first thing to say is that nothing is impossible in cryptocurrency markets.\n\nIn less than a month in August to September 2021, Solana enjoyed nearly 5x gains from around $50 per token to around $215.\n\nIf Solana could match this pace of gains for a string of three months, it could theoretically achieve the more than 10x gain required to hit a new all-time high this year.\n\nHitting $1,000 would require gains to the north of 40x versus current levels.\n\nSuch a move should not be ruled out for the years ahead, particularly if 1) Solana reclaims its status as a DeFi powerhouse amongst layer-1 blockchain protocols, 2) its mobile launch is a major success and 3) we see the likes of Bitcoin reaching into the mid-upper $100,000s and Ether surpassing $10,000.\n\nBut bulls will likely need to be patient, as while Solana could certainly post decent exponential gains this year, 40x might be asking for too much."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMigAFodHRwczovL3d3dy5wcm5ld3N3aXJlLmNvbS9uZXdzLXJlbGVhc2VzL2ZvcmRlZmktbXBjLXdhbGxldC1lbXBvd2Vycy1pbnN0aXR1dGlvbnMtd2l0aC1uYXRpdmUtc29sYW5hLWRlZmktc3VwcG9ydC0zMDE4NjM2NjIuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 26 Jun 2023 07:00:00 GMT", "title": "Fordefi MPC Wallet Empowers Institutions with Native Solana DeFi Support - PR Newswire", "content": "Fordefi establishes their position as the leading institutional DeFi wallet by launching support for Solana protocols. Post this\n\n\"Our goal is to make DeFi seamless and secure for institutions and connectivity is a critical piece of that experience. Users don't want different extensions for each chain on which they interact,\" said <PERSON>, CEO and Co-founder at Fordefi. \"We're proud to take the features that our customers have come to rely on for EVM chains and bring them to non-EVM as well.\"\n\n\"We're excited to welcome Fordefi to the Solana ecosystem,\" said <PERSON>, Head of Business Development at Solana Foundation . \"As tokenization continues to proliferate and mature, it will be imperative to have infrastructure products like the Fordefi platform that enable institutions to efficiently and intuitively manage their assets.\"\n\nUnique Technical Challenges for Solana DeFi\n\nInstitutions seeking to safely transact in Solana DeFi faced a scarcity of institutional solutions that could provide them with the same level of security and accessibility as transacting on EVM (Ethereum Virtual Machine) dApps. The transition from Ethereum-based DeFi to Solana presented Fordefi with uncharted technical hurdles. Solana operates on a distinct blockchain architecture and has its own set of protocols and smart contract language. To overcome these challenges, Fordefi's team undertook the necessary research and development efforts to understand Solana's underlying technology and design their solution to seamlessly integrate with the blockchain.\n\n\"Supporting Solana at the level of security we aim for, posed a set of very interesting technical challenges, mainly around integrating our institutional-grade security controls into the existing transaction flow used by dApps,\" said Dima Kogan, CTO and Co-founder at Fordefi. \"Our team successfully tackled those challenges and the result is a novel solution, which is both secure and provides a great user experience.\"\n\n\"As the leading liquidity primitive on Solana, Orca is looking forward to working more closely with Fordefi to lower the barrier of entry for institutional onboarding to DeFi,\" said KZ, Head of Growth at Orca\n\nNative Browser Extension & Developer API for Solana DeFi\n\nFordefi delivers a previously unavailable degree of insight into transactions, translating smart-contract transactions into language that users can understand, simulating every transaction, independently verifying contract addresses, and serving time-of-transaction risk alerts, all from a single browser extension compatible with both EVM and non-EVM chains. In addition, their flexible policy engine enables clients to set granular transaction permissions based on users, dApps, and values, further protecting clients from internal or external attacks.\n\nFordefi's native Solana integration is also available to programmatic clients. Fordefi Wallet API offers a complete developer toolkit from secure private-key management and wallet creation, to full transaction lifecycle management. Wallet API empowers developers with an unparalleled solution that eliminates the complexities of DeFi transactions, enabling them to seamlessly develop and scale their Web3 projects without any limitations or compromise to security.\n\nFordefi prioritizes security and has developed an advanced MPC (Multi-Party Computation) key management and security technology stack, mitigating the risks associated with single points of failure attacks. Fordefi recently demonstrated their commitment to security by announcing the attainment of their SOC 2 Type 2 certification from Ernst & Young. This achievement highlights their ongoing efforts to prioritize security and uphold industry-leading standards to provide a secure environment for their clients.\n\n\"Fordefi stands out in the industry with its strong focus on innovation, DeFi security, and education. They actively work to enhance the ecosystem, drive institutional adoption, and ensure participant safety,\" said Arthur Cheong, CIO and Founder at DeFiance Capital .\n\nAbout Fordefi\n\nFordefi's MPC wallet platform and Web3 gateway enables institutions to seamlessly connect to dApps across a wide range of chains while keeping digital assets secure. Fordefi is the first institutional MPC wallet and security platform built for decentralized finance(DeFi). Fordefi was founded in 2021 by crypto custody and cybersecurity experts and designed in close collaboration with industry-leading trading firms, funds and custodians. Fordefi is a financial technology company with offices in New York and Tel Aviv. For more information, visit www.fordefi.com .\n\nMedia Contact:\n\nEmail: [email protected]\n\nTG: @crestmay\n\nSOURCE Fordefi"}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiO2h0dHBzOi8vY29pbmNvZGV4LmNvbS9hcnRpY2xlLzI5MTQwL2FkZC1zb2xhbmEtdG8tbWV0YW1hc2sv0gE-aHR0cDovL2FtcC5jb2luY29kZXguY29tL2FydGljbGUvMjkxNDAvYWRkLXNvbGFuYS10by1tZXRhbWFzay8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 26 Jun 2023 07:00:00 GMT", "title": "How to Add <PERSON> to MetaMask? - CoinCodex", "content": "You can’t add Solana to MetaMask because the Solana blockchain is not compatible with the EVM (Ethereum Virtual Machine). The MetaMask wallet only supports EVM-compatible blockchains, such as Ethereum, Polygon and BNB Chain.\n\nStill, if you’re wondering how to add Solana to MetaMask, you’ll most likely be interested in Solana wallets similar to MetaMask. In this article, we’ll explain why Solana is not compatible with MetaMask and feature Solana wallets that offer a similar user experience to MetaMask.\n\nWhy can’t I add Solana to MetaMask?\n\nThe reason why the MetaMask wallet doesn’t support the Solana blockchain is because the Solana blockchain is not based on the Ethereum Virtual Machine.\n\nThe EVM is the runtime environment for Ethereum smart contracts. It executes smart contracts and computes the new state of the Ethereum blockchain after each block is added.\n\nThe Ethereum Virtual Machine is a very complex topic, but you don’t need to have detailed knowledge of it if you’re just a regular cryptocurrency user. The important thing to know here is that some blockchains that support smart contracts are compatible with the EVM, while others aren’t. MetaMask can only be used with EVM-compatible blockchains.\n\nHere’s a few examples:\n\nBlockchains that are compatible with the EVM:\n\nBlockchains that are not compatible with the EVM:\n\nWill MetaMask ever support Solana?\n\nAt this moment, it appears very unlikely that the MetaMask wallet will support Solana, unless Solana somehow switches to an EVM-based design. We have to keep in mind that MetaMask is a product of ConsenSys, which is a development studio that is heavily focused on the Ethereum ecosystem. So far, ConsenSys has mostly focused on supporting projects that are working within the Ethereum framework.\n\nMetaMask alternatives for Solana users\n\nEven though it’s not possible to add Solana to MetaMask, there’s quite a few high-quality Solana wallets that you can use instead. We will feature two browser extension-based wallets that offer a similar workflow to MetaMask, and we’ll also highlight a hardware wallet option for those who are looking to safely store their SOL and Solana-based tokens over the long term.\n\nIf you’re looking for a more comprehensive overview of wallets for Solana, take a look at our list of the best Solana wallets.\n\nLedger Nano S Plus\n\nThe Ledger Nano S Plus is a hardware wallet designed to ensure secure storage of private keys and support a wide range of cryptocurrencies. Solana is included among the supported cryptocurrencies, making the Ledger Nano S Plus an excellent choice for securely storing SOL.\n\nIt's important to note that the Ledger Nano S Plus, along with its associated Ledger Live software, currently lacks direct support for SPL tokens issued on the Solana network. Presently, only SOL is supported. However, you can connect your Ledger Nano S Plus to a wallet like Solflare to securely manage your SPL tokens.\n\nThe Ledger Nano S Plus is priced affordably and significantly enhances the security of your crypto assets. If your intention is to hold SOL for the long term, acquiring a Solana hardware wallet such as the Ledger Nano S Plus is a very worthwhile investment.\n\nGet the Ledger Nano S Plus\n\nPhantom\n\nPhantom is currently the leading wallet in the Solana ecosystem. Users can access Phantom through a mobile app on both iOS and Android, or as a browser extension compatible with Chrome, Firefox, and other widely used browsers. The fact that Phantom can be used as a browser extension makes it a great option to users who are familiar with MetaMask’s workflow.\n\nPhantom offers seamless integration with SOL, SPL tokens, and Solana-based non-fungible tokens (NFTs), enabling users to conveniently manage their digital assets. Additionally, the wallet facilitates the process of earning extra SOL through staking.\n\nIn addition, the Phantom wallet offers support for Ledger hardware wallets, ensuring enhanced security for transactions involving SOL and other crypto assets based on Solana if you connect your hardware wallet.\n\nSolflare\n\nSolflare is wallet that was designed specifically with the Solana ecosystem in mind. It provides a comprehensive range of features that cover pretty much everything one would expect from a Solana wallet.\n\nSolflare can be used to safely store SOL and SPL tokens, and it also provides NFT support as well SOL staking. If your intention is to participate in Solana NFT marketplaces or use other decentralized applications, Solflare emerges as a reliable choice.\n\nOne of the best features of Solflare is that incorporates unique security measures, safeguarding users against phishing attempts and unauthorized transactions. Furthermore, Solflare sets itself apart by providing direct customer support, a rarity among non-custodial cryptocurrency wallets.\n\nThe Solflare wallet is available as a mobile app for iOS and Android mobile devices, but it can also be used as a browser extension for Chrome, similarly to MetaMask. It’s also worth mentioning that Solflare supports Ledger and Keystone hardware cryptocurrency wallets.\n\nThe bottom line\n\nEven though you can’t add the Solana network to MetaMask, there’s still many great alternatives to choose from. If you’re looking for a user experience that’s similar to MetaMask, we recommend that you pick either Phantom or Solflare as your Solana wallet.\n\nAnother alternative to consider is to use a wallet that supports a wide range of cryptocurrencies, for example Trust Wallet. If you’re interested in exploring this option, check out our article comparing Trust Wallet vs. MetaMask."}]