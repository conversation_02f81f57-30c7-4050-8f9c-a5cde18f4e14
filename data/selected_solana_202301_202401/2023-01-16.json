[{"id": 1, "url": "https://news.google.com/rss/articles/CBMiTGh0dHBzOi8vd3d3LmJzYy5uZXdzL3Bvc3QvNS1zb2xhbmEtcHJvamVjdHMtdGhhdC1wdW1wZWQtZHVyaW5nLXRoZS1wYXN0LXdlZWvSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 16 Jan 2023 08:00:00 GMT", "title": "5 Solana Projects That Pumped During the Past Week - BSC NEWS", "content": "SOL\n\nIf you invested in any of these Solana projects, you scored huge returns while the rest of the crypto market was pumping in the past week.\n\nGreen Arrows for Solana Ecosystem\n\nAfter being absolutely hammered by the collapse of Sam Bankman-Fried’s FTX empire in late 2022, the Solana ecosystem has been running on phoenix fire to start 2023.\n\nIn the past week, Solana projects got an added octane boost from the broader crypto market pump stemming from welcome news on inflation.\n\nRecently falling below $10, the blockchain’s native coin $SOL increased in price by 62% in the past seven days and now stands at $23.23, according to CoinGecko.\n\nHere are 5 Solana projects that posted the biggest gains in the past week:\n\n1. Mercurial\n\nName : Mercurial\n\n: Mercurial Symbol : $MER\n\n: $MER Description : DEX/Yield Aggregator\n\n: DEX/Yield Aggregator Market Cap: $6.0 million\n\n$6.0 million 7-day increase: 220%\n\n2. Orca\n\nName : Orca\n\n: Orca Symbol : $ORCA\n\n: $ORCA Description : DEX\n\n: DEX Market Cap : $24.7 million\n\n: $24.7 million 7-day increase: 136%\n\n3. Oxygen\n\nName : Oxygen\n\n: Oxygen Symbol : $OXY\n\n: $OXY Description : DeFi brokerage\n\n: DeFi brokerage Market Cap : $4.7 million\n\n: $4.7 million 7-day increase: 81%\n\n4. STEPN\n\nName : STEPN\n\n: STEPN Symbol : $GMT\n\n: $GMT Description : Move to Earn\n\n: Move to Earn Market Cap : $293.8 million\n\n: $293.8 million 7-day increase: 80%4.\n\n5. Tulip Protocol\n\nName : Tulip Protocol\n\n: Tulip Protocol Symbol : $TULIP\n\n: $TULIP Description : Yield farming\n\n: Yield farming Market Cap : $2.9 million\n\n: $2.9 million 7-day increase: 69%\n\nWhat is Solana:\n\nSolana is a public, open-source blockchain that allows for smart contracts, non-fungible tokens (NFTs), and various decentralized applications (dApps). The SOL token, which is native to Solana's blockchain, provides network security through staking as well as a means of transferring value.\n\nLearn more about Solana:\n\nWebsite | Twitter | Docs | Github | Discord |"}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiQGh0dHBzOi8vZm9ya2FzdC5uZXdzL2JpdGNvaW4tZXRoZXItc29sYW5hLWJvbmstcG9seWdvbi1zaGliYWludS_SAURodHRwczovL2Zvcmthc3QubmV3cy9iaXRjb2luLWV0aGVyLXNvbGFuYS1ib25rLXBvbHlnb24tc2hpYmFpbnUvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 16 Jan 2023 08:00:00 GMT", "title": "Markets: Bitcoin, <PERSON><PERSON> rises; Solana is biggest gainer in top 10 - Forkast News", "content": "Bitcoin and Ether gained in Monday afternoon trading in Asia, along with all other top 10 non-stablecoin cryptocurrencies. Solana posted the biggest gain among the top 10, followed by Polygon and Shiba Inu.\n\nSee related article: Bitcoin miners return from winter storms, difficulty at an all-time high\n\nFast facts\n\nBitcoin traded 1.87% higher to US$21,121 in the past 24 hours to 4 p.m. in Hong Kong, a 22.72% rise over the past calendar week, according to CoinMarketCap data. Bitcoin briefly rose to US$21,345 in the morning. Ether gained 2.52% to US$1,566, up 19.46% over the past seven days.\n\nSolana rose 8% to US$24.27, with a 48% week-to-date gain. Solana has surged over 140% since the new year following the launch of Bonk, a Solana-focused dog-themed memecoin. Bonk aims to support the Solana community to rebound from its involvement with FTX and Alameda Research, Bybit explained.\n\nPolygon’s MATIC traded 4.91% higher to US$1.02, strengthening 18.46% over the past week.\n\nOn Jan. 6, it was reported that Polygon had paid popular non-fungible token (NFT) projects Y00ts and DeGods to migrate to the Polygon blockchain from Solana. The same day, Polygon announced that Mastercard is launching a new program on Polygon that will help musical artists utilize blockchain technology.\n\nShiba Inu also traded 5.95% higher, in a week-long price growth of 21.87%.\n\nAsia equity markets were mixed on Monday. Hong Kong’s Hang Seng Index closed 0.11% lower, and the Shanghai Composite Index added 1.01%. Japan’s Nikkei 225 went down by 1.14%.\n\nThe December CPI reading in the U.S. fell from November’s 7.1% and marked the smallest annual increase since October 2021. While the key inflation indicator points to easing of U.S. inflation, Fed Chair Jerome Powell has warned of more rate increases in 2023, with the next Federal Reserve meeting to be held from Jan. 31 to Feb. 1.\n\nChina’s exports fell by 9.9% in December from a year ago, while imports fell by 7.5%, with both data coming in slightly better than Reuters’ poll, according to the country’s customs data released Friday.\n\nSee related article: U.S. House of Representatives forms new cryptocurrency subcommittee"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xmbGFyZS13YWxsZXQtYnJpbmdzLXByaW9yaXR5LWdhcy0wNDMwMTg3NDQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 16 Jan 2023 08:00:00 GMT", "title": "<PERSON><PERSON><PERSON><PERSON> Brings 'Priority' Gas Fees to Solana - Yahoo Finance", "content": "Solflare, a wallet on the Solana network, announced on Monday that its users can now pay higher gas fees to muscle through network congestion.\n\n“[<PERSON><PERSON><PERSON><PERSON>] is the first to implement this in a user-friendly way,\" Solrise Finance co-founder <PERSON><PERSON><PERSON> tweeted. “In-wallet transactions will automatically be prioritized with the current market price for fees, ensuring that your transactions are included faster than those in other wallets.”\n\n\"Solflare will automatically detect whether the [Solana] network is under load and slightly increase fees to prioritize your transaction over others,\" the company tweeted separately. \"When it matters the most, your transactions will go through and be fast.\"\n\nSince its launch in 2019, Solana has become a popular blockchain for NFTs and decentralized applications due to its speed and low costs. Still, those benefits have caused the popular network to fall victim to network congestion when it attempts to handle a rush of transactions.\n\nSolana Co-Founder Says 'Long-Term Fix' to Network Outages Is in the Works\n\nFees have been a part of crypto since the very beginning of the industry and the Bitcoin whitepaper. Transactions cost a small amount of the particular network's coin or token to pay for sending cryptocurrency or NFTs from one wallet to another.\n\nLast summer, Solana Labs introduced variable gas fees to the network that the company described as a “neighborhood fees” approach, which does not impact the wider network. <PERSON><PERSON><PERSON>, co-founder of Solana Labs, said at the time the model is aimed at not punishing users with high fees across the entire network during high traffic.\n\nWhat's important about all the prioritization features rolling out in 1.10 is that bidding to flip 1 switch doesn't cause fees to go up for all the other switches. — toly 🇺🇸 (@aeyakovenko) June 16, 2022\n\nPriority fees also help make a blockchain network more stable by preventing users from hammering the Solana network with repeat transactions in the hope that one of the transactions will go through.\n\nGencel says users of other wallets may be unable to transact during times of high network traffic due to a lack of proper prioritization.\n\nSolana’s New Gas Fees Won’t Make the Network 'Expensive,' Says Co-Founder\n\n\"It's worth noting that state in Solana is isolated,\" Mert Mumtaz, co-founder and CEO of Helius, told Decrypt. \"So a highly contested piece of state won't cause the gas price for other pieces of state to go up. This is unlike ETH, where, for example, if there's a hot event like the [Bored Ape Yacht Club] land mint, the [transaction] fee for all users goes up.\"\n\nStory continues\n\n🚀 Priority fees are live in Solflare Web/Extension 🚀 Solflare will automatically detect whether the @solana network is under load & slightly increase fees to prioritize your transaction over others. When it matters the most, your transactions will go through and be fast! 🤝 pic.twitter.com/4OGV4IvjxZ — Solflare Wallet (@solflare_wallet) January 16, 2023\n\nSolflare says priority fees are already live in its app on the web, in browser extensions, and for in-wallet transactions with mobile and app launching soon.\n\nIn December, a debate in the Polygon community resulted in an approved proposal to hard fork the Polygon blockchain. Polygon Labs says forking the network will help prevent gas fee spikes and address chain reorganizations, also known as reorgs. The fork is slated to take place tomorrow."}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9zb2xmbGFyZS13YWxsZXQtYnJpbmdzLXByaW9yaXR5LWdhcy0wNDMwMTg3NDQuaHRtbNIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 16 Jan 2023 08:00:00 GMT", "title": "<PERSON><PERSON><PERSON><PERSON> Brings 'Priority' Gas Fees to Solana - Yahoo Finance", "content": "Solflare, a wallet on the Solana network, announced on Monday that its users can now pay higher gas fees to muscle through network congestion.\n\n“[<PERSON><PERSON><PERSON><PERSON>] is the first to implement this in a user-friendly way,\" Solrise Finance co-founder <PERSON><PERSON><PERSON> tweeted. “In-wallet transactions will automatically be prioritized with the current market price for fees, ensuring that your transactions are included faster than those in other wallets.”\n\n\"Solflare will automatically detect whether the [Solana] network is under load and slightly increase fees to prioritize your transaction over others,\" the company tweeted separately. \"When it matters the most, your transactions will go through and be fast.\"\n\nSince its launch in 2019, Solana has become a popular blockchain for NFTs and decentralized applications due to its speed and low costs. Still, those benefits have caused the popular network to fall victim to network congestion when it attempts to handle a rush of transactions.\n\nSolana Co-Founder Says 'Long-Term Fix' to Network Outages Is in the Works\n\nFees have been a part of crypto since the very beginning of the industry and the Bitcoin whitepaper. Transactions cost a small amount of the particular network's coin or token to pay for sending cryptocurrency or NFTs from one wallet to another.\n\nLast summer, Solana Labs introduced variable gas fees to the network that the company described as a “neighborhood fees” approach, which does not impact the wider network. <PERSON><PERSON><PERSON>, co-founder of Solana Labs, said at the time the model is aimed at not punishing users with high fees across the entire network during high traffic.\n\nWhat's important about all the prioritization features rolling out in 1.10 is that bidding to flip 1 switch doesn't cause fees to go up for all the other switches. — toly 🇺🇸 (@aeyakovenko) June 16, 2022\n\nPriority fees also help make a blockchain network more stable by preventing users from hammering the Solana network with repeat transactions in the hope that one of the transactions will go through.\n\nGencel says users of other wallets may be unable to transact during times of high network traffic due to a lack of proper prioritization.\n\nSolana’s New Gas Fees Won’t Make the Network 'Expensive,' Says Co-Founder\n\n\"It's worth noting that state in Solana is isolated,\" Mert Mumtaz, co-founder and CEO of Helius, told Decrypt. \"So a highly contested piece of state won't cause the gas price for other pieces of state to go up. This is unlike ETH, where, for example, if there's a hot event like the [Bored Ape Yacht Club] land mint, the [transaction] fee for all users goes up.\"\n\nStory continues\n\n🚀 Priority fees are live in Solflare Web/Extension 🚀 Solflare will automatically detect whether the @solana network is under load & slightly increase fees to prioritize your transaction over others. When it matters the most, your transactions will go through and be fast! 🤝 pic.twitter.com/4OGV4IvjxZ — Solflare Wallet (@solflare_wallet) January 16, 2023\n\nSolflare says priority fees are already live in its app on the web, in browser extensions, and for in-wallet transactions with mobile and app launching soon.\n\nIn December, a debate in the Polygon community resulted in an approved proposal to hard fork the Polygon blockchain. Polygon Labs says forking the network will help prevent gas fee spikes and address chain reorganizations, also known as reorgs. The fork is slated to take place tomorrow."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiOWh0dHBzOi8vZGVjcnlwdC5jby8xMTkzNTYvc29sZmxhcmUtd2FsbGV0LXNvbGFuYS1nYXMtZmVlc9IBP2h0dHBzOi8vZGVjcnlwdC5jby8xMTkzNTYvc29sZmxhcmUtd2FsbGV0LXNvbGFuYS1nYXMtZmVlcz9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 16 Jan 2023 08:00:00 GMT", "title": "<PERSON><PERSON><PERSON><PERSON> Brings 'Priority' Gas Fees to Solana - Decrypt", "content": "We do the research, you get the alpha! Get exclusive reports and access to key insights on airdrops, NFTs, and more! Subscribe now to Alpha Reports and up your game! Go to Alpha Reports\n\nDecrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nSolflare, a wallet on the Solana network, announced on Monday that its users can now pay higher gas fees to muscle through network congestion.\n\n“[Solflare] is the first to implement this in a user-friendly way,\" Solrise Finance co-founder <PERSON><PERSON><PERSON> tweeted. “In-wallet transactions will automatically be prioritized with the current market price for fees, ensuring that your transactions are included faster than those in other wallets.”\n\n\"Solflare will automatically detect whether the [Solana] network is under load and slightly increase fees to prioritize your transaction over others,\" the company tweeted separately. \"When it matters the most, your transactions will go through and be fast.\"\n\nSince its launch in 2019, Solana has become a popular blockchain for NFTs and decentralized applications due to its speed and low costs. Still, those benefits have caused the popular network to fall victim to network congestion when it attempts to handle a rush of transactions.\n\nAD\n\nAD\n\nFees have been a part of crypto since the very beginning of the industry and the Bitcoin whitepaper. Transactions cost a small amount of the particular network's coin or token to pay for sending cryptocurrency or NFTs from one wallet to another.\n\nLast summer, Solana Labs introduced variable gas fees to the network that the company described as a “neighborhood fees” approach, which does not impact the wider network. <PERSON><PERSON><PERSON>, co-founder of Solana Labs, said at the time the model is aimed at not punishing users with high fees across the entire network during high traffic.\n\nWhat's important about all the prioritization features rolling out in 1.10 is that bidding to flip 1 switch doesn't cause fees to go up for all the other switches. — toly 🇺🇸 (@aeyakovenko) June 16, 2022\n\nPriority fees also help make a blockchain network more stable by preventing users from hammering the Solana network with repeat transactions in the hope that one of the transactions will go through.\n\nAD\n\nAD\n\nGencel says users of other wallets may be unable to transact during times of high network traffic due to a lack of proper prioritization.\n\n\"It's worth noting that state in Solana is isolated,\" Mert Mumtaz, co-founder and CEO of Helius, told Decrypt. \"So a highly contested piece of state won't cause the gas price for other pieces of state to go up. This is unlike ETH, where, for example, if there's a hot event like the [Bored Ape Yacht Club] land mint, the [transaction] fee for all users goes up.\"\n\n🚀 Priority fees are live in Solflare Web/Extension 🚀 Solflare will automatically detect whether the @solana network is under load & slightly increase fees to prioritize your transaction over others. When it matters the most, your transactions will go through and be fast! 🤝 pic.twitter.com/4OGV4IvjxZ — Solflare Wallet (@solflare_wallet) January 16, 2023\n\nSolflare says priority fees are already live in its app on the web, in browser extensions, and for in-wallet transactions with mobile and app launching soon.\n\nIn December, a debate in the Polygon community resulted in an approved proposal to hard fork the Polygon blockchain. Polygon Labs says forking the network will help prevent gas fee spikes and address chain reorganizations, also known as reorgs. The fork is slated to take place tomorrow."}]