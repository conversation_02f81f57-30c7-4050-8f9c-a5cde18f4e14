[{"id": 3, "url": "https://news.google.com/rss/articles/CBMixgFodHRwczovL3d3dy5hbmFseXRpY3NpbnNpZ2h0Lm5ldC9jcnlwdG9jdXJyZW5jeS1hbmFseXRpY3MtaW5zaWdodC9nb29nbGVzLXBhcnRuZXJzaGlwLXdpdGgtc29sYW5hLXNvbC1yaXBwbGVzLXhycC0xLWJpbGxpb24tdHJhZGluZy12b2x1bWUtdG1zLW5ldHdvcmtzLXRtc24tNjAwMC1nYWlucy1pcy1jcnlwdG8tbWFya2V0LWluLWEtYnVsbC1ydW7SAdABaHR0cHM6Ly93d3cuYW5hbHl0aWNzaW5zaWdodC5uZXQvYW1wL3N0b3J5L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L2dvb2dsZXMtcGFydG5lcnNoaXAtd2l0aC1zb2xhbmEtc29sLXJpcHBsZXMteHJwLTEtYmlsbGlvbi10cmFkaW5nLXZvbHVtZS10bXMtbmV0d29ya3MtdG1zbi02MDAwLWdhaW5zLWlzLWNyeXB0by1tYXJrZXQtaW4tYS1idWxsLXJ1bg?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 06 May 2023 07:00:00 GMT", "title": "Google’s Partnership With Solana (SOL), Ripple’s (XRP) $1 Billion Trading Volume, TMS Network’s (TMSN) 6,000% Gains: Is Crypto Market in a Bull Run? - Analytics Insight", "content": "Google's interest in Solana (SOL) is no secret. In fact, Google has been an early investor in Web 3.0 technologies and startups. Now, Google is taking a step forward with Solana (SOL). Google is not allowing Web 3.0 startups to use its Google Cloud platform. For instance, developers can set up Solana (SOL) validators on Google Cloud. Now, Google has also opened up a wealth of resources for Solana (SOL) ecosystem developers. These resources include free Google Workspace Business Plus, unlimited access to customer support, access to Google Cloud events at web3 conferences, etc."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMixgFodHRwczovL3d3dy5hbmFseXRpY3NpbnNpZ2h0Lm5ldC9jcnlwdG9jdXJyZW5jeS1hbmFseXRpY3MtaW5zaWdodC9nb29nbGVzLXBhcnRuZXJzaGlwLXdpdGgtc29sYW5hLXNvbC1yaXBwbGVzLXhycC0xLWJpbGxpb24tdHJhZGluZy12b2x1bWUtdG1zLW5ldHdvcmtzLXRtc24tNjAwMC1nYWlucy1pcy1jcnlwdG8tbWFya2V0LWluLWEtYnVsbC1ydW7SAdABaHR0cHM6Ly93d3cuYW5hbHl0aWNzaW5zaWdodC5uZXQvYW1wL3N0b3J5L2NyeXB0b2N1cnJlbmN5LWFuYWx5dGljcy1pbnNpZ2h0L2dvb2dsZXMtcGFydG5lcnNoaXAtd2l0aC1zb2xhbmEtc29sLXJpcHBsZXMteHJwLTEtYmlsbGlvbi10cmFkaW5nLXZvbHVtZS10bXMtbmV0d29ya3MtdG1zbi02MDAwLWdhaW5zLWlzLWNyeXB0by1tYXJrZXQtaW4tYS1idWxsLXJ1bg?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 06 May 2023 07:00:00 GMT", "title": "Google’s Partnership With Solana (SOL), Ripple’s (XRP) $1 Billion Trading Volume, TMS Network’s (TMSN) 6,000% Gains: Is Crypto Market in a Bull Run? - Analytics Insight", "content": "Google's interest in Solana (SOL) is no secret. In fact, Google has been an early investor in Web 3.0 technologies and startups. Now, Google is taking a step forward with Solana (SOL). Google is not allowing Web 3.0 startups to use its Google Cloud platform. For instance, developers can set up Solana (SOL) validators on Google Cloud. Now, Google has also opened up a wealth of resources for Solana (SOL) ecosystem developers. These resources include free Google Workspace Business Plus, unlimited access to customer support, access to Google Cloud events at web3 conferences, etc."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vZGVjcnlwdC5jby8xMzkxNjkvc3VpLWJ1aWx0LWdhbWVzLWhlcmVzLXdoeS1kZXZlbG9wZXJzLWJ1bGxpc2jSAUxodHRwczovL2RlY3J5cHQuY28vMTM5MTY5L3N1aS1idWlsdC1nYW1lcy1oZXJlcy13aHktZGV2ZWxvcGVycy1idWxsaXNoP2FtcD0x?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Sat, 06 May 2023 07:00:00 GMT", "title": "<PERSON><PERSON> Is Built for Games—Here's Why Developers Are Bullish - Decrypt", "content": "Decrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nLayer-1 blockchain network Sui has already positioned itself as one of the most appealing destinations for Web3 game developers—despite being the newest to the game so far.\n\nPolygon, Immutable, Avalanche, and Solana have growing Web3 gaming ecosystems with a wide variety of games in all stages of development. But Sui, which just launched its mainnet on Wednesday, offers unique technical specifications and a different approach to on-chain assets that’s made it a compelling alternative for budding game makers.\n\nPart of what makes Sui unique is its ability to process transactions in parallel, as well as scale horizontally by adding more nodes to keep up with demand—two features designed to keep its transaction fees very low as more and more games and decentralized apps (dapps) come online. Dynamically-updated NFTs and an \"object-based\" model may also prove appealing to game designers, Sui creator <PERSON><PERSON> claims.\n\nSui is not Ethereum-compatible via the Ethereum Virtual Machine, which sets it apart from some of the established Web3 gaming networks out there, but <PERSON><PERSON> believes that the network’s innate gaming appeal will allow it to onboard mainstream gamers.\n\nAD\n\nAD\n\nGame-first approach\n\nMysten Labs’ Head of Gaming Partnerships <PERSON> told Decrypt in an interview that over 40 games are building on Sui so far—even though the mainnet has only been live for a few days.\n\n“Our mission is to go after not ‘blockchain games’ or ‘crypto games,<PERSON><PERSON> <PERSON> said, “but to go after great games built by great game developers that want to leverage Web3 to enhance their player experience, and provide more engagement and agency.”\n\nPalma echoed a growing sentiment about Web3 games putting gameplay before any crypto elements—a take that leaders at firms like Magic Eden, Ava Labs, Solana Foundation, Gala Games, and others have expressed in recent months.\n\nWhen it comes to Sui’s technical appeal, Palma shared that Sui is an “object-based” chain. Broadly, what that means is that the network is well-optimized for games, which consist of hundreds if not thousands of in-game objects created in game engines like Unreal Engine 5 or Unity.\n\nAD\n\nAD\n\n💥Get ready to play on @SuiNetwork! 📺Watch real gameplay footage of these incredible Mysten Labs' partners: pic.twitter.com/66qlt5n68Y — Mysten Labs (@Mysten_Labs) May 1, 2023\n\nPalma believes that Sui allows game developers to more easily and seamlessly evolve in-game asset NFTs over time, because Sui’s on-chain assets can change dynamically in real-time as players are leveling up items like a sword, for example.\n\nEvolving game assets\n\nRival player kills and achievements secured with specific items can be added to that item’s metadata—or the data that describes the unique attributes of an NFT—automatically, allowing players to “write” their own histories with each item as they play.\n\n“If somebody sees there's a sword with 10,000 kills, versus the same sword with 100 kills, the 10,000-kill sword is just gonna inherently carry more value,” Palma argued.\n\n“But it also allows you to do things like upgrading the NFT based on its metadata,” he continued. “You could do something like have a sword go from ‘gold’ to ‘legendary’ to ‘mythic,’ if you have a system like that.\"\n\nAbyss World. Image: Metagame Industries / Steam.\n\nSuch dynamic items are possible on Sui without having to burn and reissue items to reflect the new data, as developers might on other blockchain networks. When tokens are burned and re-minted on other chains, their histories are typically lost unless developers make a point of manually adding an item’s previous data to the new one.\n\n“If you wanted to do this on another chain, you would either be doing a ton of syncing between your central database—where this info usually exists—and the on-chain transaction history,” Palma said, “or you would be burning the NFTs and re-minting new ones to go from 'gold' to 'mythic.'\"\n\n“Sui’s dynamic model allows you to keep all of that provenance, which should drive the value of these items over time—both for players and for the ecosystem,” he added. “So it's just a really cool new feature that you can bake into your games that wasn't really possible on-chain before.”\n\nAD\n\nAD\n\nSui developers speak\n\nMysten Labs has been working closely with game developers—the startup behind Sui won’t be taking a hands-off approach, said Palma. Over the next month, numerous games will launch on Sui, including a \"Walking Dead\" game and Final Stardust from Orange Comet, Project Eluune from Arrivant, and Run Legends from Talofa Games, as well as metaverse game Worlds Beyond plus the Overwatch-esque Bushi.\n\nBut what do game developers building on Sui think about the network, and what’s drawn them to build on it?\n\nGhost Ivy, a Web3 game studio building a first-person shooter (FPS) game called Haven’s Compass for PC and mobile, chose Sui because of its “usability” for gaming.\n\nA still from FPS Haven's Compass. Image: Ghost Ivy on YouTube.\n\n“It was a hard decision,” Ghost Ivy CEO Fares Alu-Taleb told Decrypt about choosing Sui. “For us, it’s important that the game can stay online nonstop, and that the players won’t need any interaction with the blockchain. Sui makes this easier for us as developers.”\n\nAbyss World is another Web3 game in development from Metagame Industries that will leverage Sui in a number of ways. Its developers want Abyss World to be a high-end, “AAA” role-playing game (RPG) that offers both player vs. player (PVP) and player vs. environment (PVE) game experiences, as well as an auto-battler mode through cloud gaming—which means the game can be played on either a phone or PC without having to download any files.\n\nThere will be in-game items that can be earned on-chain through gameplay in Abyss World, as well as via in-game “minting factories” where players can put existing items on-chain.\n\n“The transaction speed is super fast,” Metagame President Kinson Hou told Decrypt about Sui. “I think it’s one of the only blockchains that can go to the internet’s scale.”\n\nAbyss World. Image: Metagame Industries / Steam.\n\nAbyss World also wants players to be able to launch their own private servers. Hou sees that as an important part of its community-driven model, in which all game events impact other players and gamers get to create a collective “player-driven history” in a singular timeline.\n\nAD\n\nAD\n\n“We want to build the infinite game,” Hou said."}]