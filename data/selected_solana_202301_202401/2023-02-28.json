[{"id": 0, "url": "https://news.google.com/rss/articles/CBMieWh0dHBzOi8vd3d3LmNvaW5kZXNrLmNvbS9idXNpbmVzcy8yMDIzLzAyLzI4L3NvbGFuYXMtc3BhbS1wcm9ibGVtcy1wZXJzaXN0LWRlc3BpdGUtdGVjaC1pbXByb3ZlbWVudHMtbWV2LXJlc2VhcmNoZXJzLXNheS_SAX1odHRwczovL3d3dy5jb2luZGVzay5jb20vYnVzaW5lc3MvMjAyMy8wMi8yOC9zb2xhbmFzLXNwYW0tcHJvYmxlbXMtcGVyc2lzdC1kZXNwaXRlLXRlY2gtaW1wcm92ZW1lbnRzLW1ldi1yZXNlYXJjaGVycy1zYXkvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 28 Feb 2023 08:00:00 GMT", "title": "Solana’s Spam Problems Persist Despite Tech Improvements, MEV Researchers Say - CoinDesk", "content": "The result is wasted blockspace for the network as well as capital burnt for no reason on losing trades, according to a blog post from Jito Foundation. It blames this on the way Solana’s infrastructure handles submitted transactions: give priority to the first in line. That creates an incentive for arbitrage bots to submit multiple duplicate transactions in the hopes they will get the winner."}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMiZmh0dHBzOi8vZGVjcnlwdC5jby8xMjI0MDQvZ2FtZS1lbmdpbmUtdW5pdHktYWRkcy12ZXJpZmllZC13ZWIzLXRvb2xib3gtZm9yLW1ldGFtYXNrLXNvbGFuYS1kYXBwZXItbGFic9IBbGh0dHBzOi8vZGVjcnlwdC5jby8xMjI0MDQvZ2FtZS1lbmdpbmUtdW5pdHktYWRkcy12ZXJpZmllZC13ZWIzLXRvb2xib3gtZm9yLW1ldGFtYXNrLXNvbGFuYS1kYXBwZXItbGFicz9hbXA9MQ?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 28 Feb 2023 08:00:00 GMT", "title": "Game Engine Unity Adds Verified Web3 Toolbox for MetaMask, Solana, Dapper Labs - Decrypt", "content": "Decrypt’s Art, Fashion, and Entertainment Hub. Discover SCENE\n\nUnity, one of the most popular game developer engines for creating 3D game environments, has added a “decentralization” category in its Asset Store to show devs the kinds of Web3 tools and protocols they can integrate into their video games.\n\nThe page includes 13 different “verified solutions” that Unity says it has vetted for developer use. While the complete list can be found in Unity’s official announcement, it includes Algorand, Aptos Labs, Dapper Labs, ImmutableX, Solana, Tezos, and ConsenSys products Infura and MetaMask in the virtual toolbox.\n\n🚀 Thrilled to announce that we have listed @MetaMask, @Infura_io, and @Trufflesuite in the new decentralized section of the Unity Asset Store to empower developers to build #web3 games! 🔗 https://t.co/hzeGahgVMz pic.twitter.com/rPHYxxhqIi — ConsenSys (@ConsenSys) February 28, 2023\n\nAD\n\nAD\n\nUnity’s interest in and embrace of Web3 gaming tools is a huge and necessary step for the future of blockchain games. Many indie developers use Unity for developing games, and Web3 titles like The Sandbox, Decentraland, and Dogamí were also made using Unity’s software.\n\nAccording to Unity’s website, it will only verify Web3 tools that it defines as legitimate, dependable, and evolving—meaning that the tools are made by public-facing firms, have reliable features and support, and are being actively maintained by their creators.\n\nAny of the 13 Web3 gaming tools can be added directly to projects through Unity’s Asset Store, adding another layer of ease of adoption without developers having to navigate through third-party websites to find tools. Like the store’s other assets, the blockchain tools can be reviewed by developers on a one to five star scale.\n\nUnity did not immediately respond to Decrypt’s request for comment."}, {"id": 10, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9nYW1lLWVuZ2luZS11bml0eS1hZGRzLXZlcmlmaWVkLTIwMTc1NDc2Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 28 Feb 2023 08:00:00 GMT", "title": "Game Engine Unity Adds Verified Web3 Toolbox for MetaMask, Solana, Dapper Labs - Yahoo Finance", "content": "Unity, one of the most popular game developer engines for creating 3D game environments, has added a “decentralization” category in its Asset Store to show devs the kinds of Web3 tools and protocols they can integrate into their video games.\n\nThe page includes 13 different “verified solutions” that Unity says it has vetted for developer use. While the complete list can be found in Unity’s official announcement, it includes Algorand, Aptos Labs, Dapper Labs, ImmutableX, Solana, Tezos, and ConsenSys products Infura and MetaMask in the virtual toolbox.\n\n🚀 Thrilled to announce that we have listed @MetaMask, @Infura_io, and @Trufflesuite in the new decentralized section of the Unity Asset Store to empower developers to build #web3 games! 🔗 https://t.co/hzeGahgVMz pic.twitter.com/rPHYxxhqIi — ConsenSys (@ConsenSys) February 28, 2023\n\nUnity’s interest in and embrace of Web3 gaming tools is a huge and necessary step for the future of blockchain games. Many indie developers use Unity for developing games, and Web3 titles like The Sandbox, Decentraland, and Dogamí were also made using Unity’s software.\n\nEx-Goldman Sachs Analyst Releases Kill-to-Earn Zombie Game 'Undead Blocks' on ImmutableX\n\nAccording to Unity’s website, it will only verify Web3 tools that it defines as legitimate, dependable, and evolving—meaning that the tools are made by public-facing firms, have reliable features and support, and are being actively maintained by their creators.\n\nAny of the 13 Web3 gaming tools can be added directly to projects through Unity’s Asset Store, adding another layer of ease of adoption without developers having to navigate through third-party websites to find tools. Like the store’s other assets, the blockchain tools can be reviewed by developers on a one to five star scale.\n\nUnity did not immediately respond to Decrypt’s request for comment."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiTWh0dHBzOi8vZmluYW5jZS55YWhvby5jb20vbmV3cy9nYW1lLWVuZ2luZS11bml0eS1hZGRzLXZlcmlmaWVkLTIwMTc1NDc2Ny5odG1s0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 28 Feb 2023 08:00:00 GMT", "title": "Game Engine Unity Adds Verified Web3 Toolbox for MetaMask, Solana, Dapper Labs - Yahoo Finance", "content": "Unity, one of the most popular game developer engines for creating 3D game environments, has added a “decentralization” category in its Asset Store to show devs the kinds of Web3 tools and protocols they can integrate into their video games.\n\nThe page includes 13 different “verified solutions” that Unity says it has vetted for developer use. While the complete list can be found in Unity’s official announcement, it includes Algorand, Aptos Labs, Dapper Labs, ImmutableX, Solana, Tezos, and ConsenSys products Infura and MetaMask in the virtual toolbox.\n\n🚀 Thrilled to announce that we have listed @MetaMask, @Infura_io, and @Trufflesuite in the new decentralized section of the Unity Asset Store to empower developers to build #web3 games! 🔗 https://t.co/hzeGahgVMz pic.twitter.com/rPHYxxhqIi — ConsenSys (@ConsenSys) February 28, 2023\n\nUnity’s interest in and embrace of Web3 gaming tools is a huge and necessary step for the future of blockchain games. Many indie developers use Unity for developing games, and Web3 titles like The Sandbox, Decentraland, and Dogamí were also made using Unity’s software.\n\nEx-Goldman Sachs Analyst Releases Kill-to-Earn Zombie Game 'Undead Blocks' on ImmutableX\n\nAccording to Unity’s website, it will only verify Web3 tools that it defines as legitimate, dependable, and evolving—meaning that the tools are made by public-facing firms, have reliable features and support, and are being actively maintained by their creators.\n\nAny of the 13 Web3 gaming tools can be added directly to projects through Unity’s Asset Store, adding another layer of ease of adoption without developers having to navigate through third-party websites to find tools. Like the store’s other assets, the blockchain tools can be reviewed by developers on a one to five star scale.\n\nUnity did not immediately respond to Decrypt’s request for comment."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMidmh0dHBzOi8vd3d3LnNlY3VyaXRpZXMuaW8vY2F1c2Utb2Ytc29sYW5hcy13ZWVrZW5kLW5ldHdvcmstZGVncmFkYXRpb24tc3RpbGwtdW5rbm93bi1hcy1lY29zeXN0ZW0tbmF2aWdhdGVzLXVwaGVhdmFscy_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "<PERSON><PERSON>, 28 Feb 2023 08:00:00 GMT", "title": "Solana Navigates Upheavals as Cause of Weekend Network Degradation Remains Unknown - Securities.io", "content": "The Solana network suffered another blow over the weekend as the protocol crashed twice, adding to recent unpromising developments that have come upon the Layer 1 ecosystem in the last week. The incident, which resulted in transactions on the platform freezing temporarily, elicited deprecatory remarks from the broader crypto community. The majority were very scathing about the repeated occurrences of the network suffering degradation issues and outages, resulting in periods of downtime.\n\nMeanwhile, in the market, the Solana (SOL) native token, which fell briefly hours after reports of the event surfaced, has returned to trading tightly between $22.80 and $23.50 since the start of the week. Though SOL has maintained its rank among the top cryptocurrencies in terms of market capital (excluding stablecoins) according to CoinMarketCap data, market observers have widely called into question the status of its network.\n\nRecovery from the 20-hour outage after a failed initial network restart\n\nThe Solana network started experiencing extended sluggishness on Saturday at around 5:45 UTC. This later evolved into an inability to validate transactions and produce blocks. Solana struggled to remedy the issue before deciding on a validator restart of the network.\n\nAttempted remedies\n\nThe team behind Solana Status was among the first to notice the problem with performance, explaining in a series of tweets that the issue emerged while upgrading the network from v1.13 to v1.14. Solana validators sought a coordinated mainnet-beta cluster restart to remedy the slow block finalization. This, however, resulted in an even longer wait as the network ran into a discrepancy snag regarding the correct restart slot. The initial reboot eventually had to be scrapped at 76% progress, according to blockchain infrastructure company and Solana validator Stakewiz, to include a block of confirmed user transactions. The transactions had been erroneously omitted due to the inaccurately chosen slot height for the reboot.\n\nSlightly over nine hours later, subsequent instructions were issued to restart the network again, this time successfully reviving the network on Sunday, around 1:30 UTC. The network outage lasted nearly 20 hours and 30 minutes, and the root cause of the network problems remains under active investigation by core contributors of Solana. Head of strategy and communications at Solana Foundation, Austin Federa, noted that the errant issue emerged while upgrading in the “vote-only mode,” which typically allows for quick problem resolution, but the blockchain could not recover this time, necessitating an entire restart of the network.\n\nFiredancer developer Richard Patel explained that the debased network performance set Solana to function in the vote-only mode, a fail-safe measure that initializes when network-wide finalization stalls. Despite block production remaining active, all economic activity, such as non-voting transactions, ceases in this state.\n\nFiredancer to eventually remedy Solana's issues?\n\nFiredancer is a next-generation validator client for the Solana network that Chicago-based trading expert Jump is building via its Jump Crypto arm, seeking to enhance its throughput, efficiency, and resilience. The Firedancer project is expected to eliminate the single point of failure horrors haunting Solana by implementing a second, fully independent consensus node for Solana.\n\nIn the latest update as of Tuesday, the Solana Status team said blockchain engineers had yet to determine the cause. The update extended an invitation to those looking to help with the diagnosis. Separately, Solana founder and CEO Anatoly Yakovenko vehemently denied a proposition fronted on Feb 27 by one Twitter user, DBCryptoX. The user theorized that validator messages and on-chain votes clogged the consensus layer, choking the network. The argument was not only dismissed by Yakovenko as ‘pure ignorance' but also by the majority of users interacting with the tweet.\n\nSetbacks in the path of building products and achieving real-life use cases\n\nLaunched in 2020, the Solana blockchain platform has grown in several aspects, including scaling and gaining real-life utility. However, the almost routine instances of the network going offline have blemished its reputation, to say the least.\n\nTowards the end of last year, the blockchain outfit acknowledged surviving a challenging year in the 2022 issue of Solana Solstice, adding that it had much to look forward to. The Layer 1 protocol assured that the several timeouts it had experienced until then pushed its engineering team and the broader ecosystem to improve the stability and performance of the network. These efforts have, however, seemingly failed to deliver on the stability, decentralization, and speed pillars.\n\nLeveraging Web3 for real-life use cases\n\nNotwithstanding the discredit brought upon by the network issues, Solana still experienced a considerable surge in developer activity in 2022, with a total of 2053 active developers working on projects using the platform as of the end of November. Of these, 1654 developers were focused on Solana-exclusive projects. The increase in developer participation may be attributed to the efforts made to improve education and the provision of tools within the Solana ecosystem. This has made it easier for new developers to quickly become proficient in using the platform and accelerate the development of projects.\n\nThe ecosystem welcomed the launch of the Solana Mobile Stack and Solana blockchain-powered flagship phone, Saga, mid last year. The release aimed to improve the usability of mobile devices for dApps, and their access to Web3. With the first Saga development kits (DVT-1) already in shipping, consumer units are set for early this year. These initiatives aim to drive broader adoption of Web3 and dApps by making them accessible and secure on a range of devices.\n\nSolana Spaces to wind up a seven-month experiment at the end of February\n\nSolana also said at the time that it ported from the internet to actual physical locations, as it launched retail Solana-themed stores in Miami and New York. The former set up in Wynwood, one of Miami's most happening districts, opened in August, a month after the one in the Hudson Yards neighborhood. Though the initiative briefly enabled the Solana community to engage in a more tangible and accessible way, Solana Spaces revealed plans to pull the plug in a tweet last week.\n\nSolana Spaces founder Vibhu Norby said the closure of the two community-oriented stores was down to the failure to realize its objective of pitching the adoption of the eponymous chain. The Spaces team projected to onboard an average of at least 100,000 people monthly but only managed 75% of this target during the seven-month run. The two shops will sunset this month's end as the firm shifts its priorities to the non-fungible tokens (NFTs) niche – specifically, its NFT artwork airdrop platform ‘DRiP,' which has attracted more traffic in recent days. In the Feb 21 farewell note, Norby invited users to get free or discounted merch as a ‘generous' gesture.\n\nNFT marketplaces track a decline in activity across February\n\nThe NFTs niche has seen a resurgence in activity this year, but the upturn in sales and user activity hasn't worked in favor of all blockchains amid stiff competition. Data from blockchain data aggregator Dune Analytics shows that the Solana NFT marketplace has struggled to keep up with the average figures it posted earlier this month. The number of visitors to Solana's top five NFT marketplace recorded a sudden decline on Feb 25 but has since recovered.\n\nSolana trails only behind Ethereum among top blockchains by NFT sales volume in the last 30 days behind, according to CryptoSlam data. First-rank Ethereum, which enjoys the first-mover advantage, has processed NFT sales of roughly $903 million, followed by the Solana blockchain, which has logged $86 million in the same period.\n\nIn January, Solana recorded $158.25 million in NFT sales, with both unique seller and buyer figures exceeding 130,000, bringing the average sale mark to $92.87. The total NFT sales figure has shrunk by more than 50% in February to $72.3 million. Markedly, the total NFT transactions on the blockchain have surpassed the 1 million figure for the second consecutive month after failing to crack 900,000 in November and December.\n\nSolana (SOL) price action\n\nThe Solana (SOL) token has printed a small red candle in the last few hours after Monday's tight trading action. The SOL/USD pair was last spotted at $22.20 at writing, 2.56% below yesterday's range.\n\nIn a research report last Wednesday, Coinbase analysts opined that Solana is favorably positioned to take a lead spot among Layer 1 ecosystems. The report pointed out that the potential lies in its “legitimately differentiated approach within the layer 1 landscape” as well as improved network activity.\n\n“The fundamental value proposition of the Solana protocol persists from a technical perspective,” the author wrote.\n\nThe note also implied that the Solana (SOL) token could be undervalued relative to Ethereum (ETH) based on NFT activity in which Solana – at present – compares favorably to Ethereum.\n\nTo learn more about Solana, visit our Investing in Solana guide."}]