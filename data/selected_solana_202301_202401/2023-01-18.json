[{"id": 5, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vY3J5cHRvLm5ld3Mvc29sYW5hcy1ib25rLWlzLWRvd24tNi1hcy1jcnlwdG8tdHdpdHRlci1jb25mdXNlcy1pdC13aXRoLWNsb25lL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Jan 2023 08:00:00 GMT", "title": "Solana's BONK is down 6% as crypto twitter confuses it with clone - crypto.news", "content": "Solana-based meme coin BONK is experiencing a fall in price as blockchain security firm PeckShield Alert warns against a cloned token going by the same moniker.\n\nEarlier today, leading blockchain security company PeckShield Alert tweeted that the price of the BONK meme coin had plummeted by almost 100%. The alert was intended for holders of the BONK meme coin listed on the Solana blockchain.\n\nHowever, some crypto community members were quick to point out that the BONK token addressed by the security firm was separate from the original meme token listed on Solana. Indeed, the transaction data provided by Peckshield was part of the Polygon ecosystem.\n\nThat’s w scam BONK, the legit one only on Solana. — B I M ◎ / mbimoprasetya.eth (@mbimoprasetya) January 18, 2023\n\nSome members went on to say that they anticipate better work from the company that asserts to be the “Industry Leading Blockchain Security Company.”\n\nSince its inception nearly a month ago, the Solana-based canine meme coin Bonk (BONK) has had a bumpy ride. One week after launch, Bonk’s popularity skyrocketed by 4424% due to rapid adoption by the Solana community. The developers airdropped 50% of the overall supply to the SOL blockchain community, sparking intense interest in the newly bred dog-themed cryptocurrency.\n\nBONK is down 6% amid clone confusion\n\nThe Solana-based token captured the attention of mainstream crypto investors in a phenomenally short time. Indeed, this turned out to be very beneficial for the severely bleeding SOL, whose stock had dropped to single digits, and its market cap had fallen ten times since the beginning of 2022. Because of the growing public interest, some crypto exchanges, such as Huobi, have announced that they will list the meme coin.\n\nNevertheless, the latest confusion regarding the BONK clone had taken a hit on the meme coin bringing down its price by 6% soon after the alert was issued. At the time of writing, BONK was trading at $0.0000012 per coin, according to data from CoinMarketCap."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMiVGh0dHBzOi8vY3J5cHRvLm5ld3Mvc29sYW5hcy1ib25rLWlzLWRvd24tNi1hcy1jcnlwdG8tdHdpdHRlci1jb25mdXNlcy1pdC13aXRoLWNsb25lL9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Jan 2023 08:00:00 GMT", "title": "Solana's BONK is down 6% as crypto twitter confuses it with clone - crypto.news", "content": "Solana-based meme coin BONK is experiencing a fall in price as blockchain security firm PeckShield Alert warns against a cloned token going by the same moniker.\n\nEarlier today, leading blockchain security company PeckShield Alert tweeted that the price of the BONK meme coin had plummeted by almost 100%. The alert was intended for holders of the BONK meme coin listed on the Solana blockchain.\n\nHowever, some crypto community members were quick to point out that the BONK token addressed by the security firm was separate from the original meme token listed on Solana. Indeed, the transaction data provided by Peckshield was part of the Polygon ecosystem.\n\nThat’s w scam BONK, the legit one only on Solana. — B I M ◎ / mbimoprasetya.eth (@mbimoprasetya) January 18, 2023\n\nSome members went on to say that they anticipate better work from the company that asserts to be the “Industry Leading Blockchain Security Company.”\n\nSince its inception nearly a month ago, the Solana-based canine meme coin Bonk (BONK) has had a bumpy ride. One week after launch, Bonk’s popularity skyrocketed by 4424% due to rapid adoption by the Solana community. The developers airdropped 50% of the overall supply to the SOL blockchain community, sparking intense interest in the newly bred dog-themed cryptocurrency.\n\nBONK is down 6% amid clone confusion\n\nThe Solana-based token captured the attention of mainstream crypto investors in a phenomenally short time. Indeed, this turned out to be very beneficial for the severely bleeding SOL, whose stock had dropped to single digits, and its market cap had fallen ten times since the beginning of 2022. Because of the growing public interest, some crypto exchanges, such as Huobi, have announced that they will list the meme coin.\n\nNevertheless, the latest confusion regarding the BONK clone had taken a hit on the meme coin bringing down its price by 6% soon after the alert was issued. At the time of writing, BONK was trading at $0.0000012 per coin, according to data from CoinMarketCap."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiaGh0dHBzOi8vdGVjaGNydW5jaC5jb20vMjAyMy8wMS8xOC9zb2xhbmEtY28tZm91bmRlci1zZWVzLXBvdGVudGlhbC1mb3ItZGV2cy10by1sZWFkLWl0cy1uZXR3b3JrLWluLTIwMjMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Jan 2023 08:00:00 GMT", "title": "Solana co-founder sees potential for devs to lead its network in 2023 - TechCrunch", "content": "As the crypto developer ecosystem expands, major ecosystems outside of the top two cryptocurrencies — Bitcoin and Ethereum — are growing, according to a new report.\n\nAbout 72% of monthly active developers (devs) are working on blockchains that are not part of the Bitcoin and Ethereum networks, according to the 2022 Electric Capital Developer Report, which trawled code commits across open source repositories.\n\nSolana saw the highest number of new developers contributing to the ecosystem, with its developer count rising by 83%, the fastest of any major blockchain. Compared to that, Polygon saw a 40% rise in developer count, Cosmos saw 25% and Polkadot saw 2%, the report showed.\n\n“Developers will build where they see technology advantages, and Solana delivers faster transactions and lower costs than alternatives,” Solana co-founder <PERSON> told TechCrunch. “But they will also build where they see other kinds of advantages, such as an active community.”\n\nThe growth of the developer ecosystem on Solana can be attributed to a mix of education efforts and resources, community engagement, technical advantages and business opportunities, <PERSON><PERSON> said.\n\nEven with around 2,000 total developers as of December 2022, Solana is still about 3,500 developers short of Ethereum’s count, which had a total of about 5,700 developers. It’s important to note, though, that Ethereum launched in 2013 and Solana launched in 2018, so the former has enjoyed a five-year head start.\n\nThese Solana developers are working on “a range of use cases,” from evolving the NFT and DeFi spaces to getting retailers to experiment with decentralized payment rails like Solana Pay, <PERSON><PERSON> said. “Right now, game devs on Solana are very active and innovating heavily to create games that are both fun to play and rewarding for players to participate.”\n\nSeparately, The Solana Mobile Stack and Saga phone are attracting attention from developers interested in creating mobile web3 apps, Gokal added. “The mobile opportunities on Solana will only get better as Saga and SMS get exposed to more people.”\n\nSolana, which is approaching its fifth birthday, has the highest number of developers since it was launched compared to other blockchains, according to a chart (shown below) from Electric Capital.\n\nSolana’s total developer count spiked in November 2022 during its Breakpoint conference and hackathon event. In comparison, Polygon has roughly half the developers but has existed for around the same time.\n\n“The creation of new technologies at different times naturally brings about different use cases and opportunities,” Gokal said. “Solana is a high-performance blockchain where transactions are inexpensive and fast to settle. There will continue to be use cases for other networks, but speed, efficiency and low costs usually always attract the most activity.”\n\nAccording to Solana Beach data, Solana’s network can currently execute 3,531 transactions per second (TPS). The TPS measures a network’s speed and scalability while showing the maximum number of transactions the blockchain can carry out — if needed. In comparison, Ethereum averages about 15 TPS.\n\nGokal said Solana Foundation will continue to host hackathons and hacker houses to help get more devs get involved. But this year might be different.\n\n“2023 might just be the year when other devs already building on Solana collectively lead the direction of the network. We’ve already seen that with a community-led hackathon happening right now and more devs are stepping forward to contribute to the core Solana network,” he added.\n\nEarlier last year, there was more hope in the crypto community that it was in a temporary downturn, but sentiments have changed.\n\n“I think people fully understand the extent to which macroeconomic conditions have deteriorated across all aspects of the economy and that there are bigger issues at play, such as inflation,” Gokal said. “People now seem mentally prepared for an extended bearish period, which is the best time to experiment and find product-market fit.”\n\nAfter crypto exchange FTX collapsed in November, there was a lot of uncertainty across the space, Gokal noted. “[It] quickly gave way to an understanding that the ecosystem is bigger than any one individual.”\n\n“With all that in mind, as well as the Solana network performing better than ever, the time is now for devs to really dream up new use cases and build them,” Gokal added."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiaGh0dHBzOi8vdGVjaGNydW5jaC5jb20vMjAyMy8wMS8xOC9zb2xhbmEtY28tZm91bmRlci1zZWVzLXBvdGVudGlhbC1mb3ItZGV2cy10by1sZWFkLWl0cy1uZXR3b3JrLWluLTIwMjMv0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Wed, 18 Jan 2023 08:00:00 GMT", "title": "Solana co-founder sees potential for devs to lead its network in 2023 - TechCrunch", "content": "As the crypto developer ecosystem expands, major ecosystems outside of the top two cryptocurrencies — Bitcoin and Ethereum — are growing, according to a new report.\n\nAbout 72% of monthly active developers (devs) are working on blockchains that are not part of the Bitcoin and Ethereum networks, according to the 2022 Electric Capital Developer Report, which trawled code commits across open source repositories.\n\nSolana saw the highest number of new developers contributing to the ecosystem, with its developer count rising by 83%, the fastest of any major blockchain. Compared to that, Polygon saw a 40% rise in developer count, Cosmos saw 25% and Polkadot saw 2%, the report showed.\n\n“Developers will build where they see technology advantages, and Solana delivers faster transactions and lower costs than alternatives,” Solana co-founder <PERSON> told TechCrunch. “But they will also build where they see other kinds of advantages, such as an active community.”\n\nThe growth of the developer ecosystem on Solana can be attributed to a mix of education efforts and resources, community engagement, technical advantages and business opportunities, <PERSON><PERSON> said.\n\nEven with around 2,000 total developers as of December 2022, Solana is still about 3,500 developers short of Ethereum’s count, which had a total of about 5,700 developers. It’s important to note, though, that Ethereum launched in 2013 and Solana launched in 2018, so the former has enjoyed a five-year head start.\n\nThese Solana developers are working on “a range of use cases,” from evolving the NFT and DeFi spaces to getting retailers to experiment with decentralized payment rails like Solana Pay, <PERSON><PERSON> said. “Right now, game devs on Solana are very active and innovating heavily to create games that are both fun to play and rewarding for players to participate.”\n\nSeparately, The Solana Mobile Stack and Saga phone are attracting attention from developers interested in creating mobile web3 apps, Gokal added. “The mobile opportunities on Solana will only get better as Saga and SMS get exposed to more people.”\n\nSolana, which is approaching its fifth birthday, has the highest number of developers since it was launched compared to other blockchains, according to a chart (shown below) from Electric Capital.\n\nSolana’s total developer count spiked in November 2022 during its Breakpoint conference and hackathon event. In comparison, Polygon has roughly half the developers but has existed for around the same time.\n\n“The creation of new technologies at different times naturally brings about different use cases and opportunities,” Gokal said. “Solana is a high-performance blockchain where transactions are inexpensive and fast to settle. There will continue to be use cases for other networks, but speed, efficiency and low costs usually always attract the most activity.”\n\nAccording to Solana Beach data, Solana’s network can currently execute 3,531 transactions per second (TPS). The TPS measures a network’s speed and scalability while showing the maximum number of transactions the blockchain can carry out — if needed. In comparison, Ethereum averages about 15 TPS.\n\nGokal said Solana Foundation will continue to host hackathons and hacker houses to help get more devs get involved. But this year might be different.\n\n“2023 might just be the year when other devs already building on Solana collectively lead the direction of the network. We’ve already seen that with a community-led hackathon happening right now and more devs are stepping forward to contribute to the core Solana network,” he added.\n\nEarlier last year, there was more hope in the crypto community that it was in a temporary downturn, but sentiments have changed.\n\n“I think people fully understand the extent to which macroeconomic conditions have deteriorated across all aspects of the economy and that there are bigger issues at play, such as inflation,” Gokal said. “People now seem mentally prepared for an extended bearish period, which is the best time to experiment and find product-market fit.”\n\nAfter crypto exchange FTX collapsed in November, there was a lot of uncertainty across the space, Gokal noted. “[It] quickly gave way to an understanding that the ecosystem is bigger than any one individual.”\n\n“With all that in mind, as well as the Solana network performing better than ever, the time is now for devs to really dream up new use cases and build them,” Gokal added."}]