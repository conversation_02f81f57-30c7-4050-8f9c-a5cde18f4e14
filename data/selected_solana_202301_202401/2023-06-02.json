[{"id": 7, "url": "https://news.google.com/rss/articles/CBMigwFodHRwczovL3d3dy5waGlsc3Rhci5jb20vbGlmZXN0eWxlL2Zvb2QtYW5kLWxlaXN1cmUvMjAyMy8wNi8wMi8yMjcwNzE0L2hlcmFsZC1zdWl0ZXMtb2ZmZXJzLXR3by1iaWctdmFsdWUtcm9vbS1wcm9tb3MtdW50aWwtanVuZS0zMNIBiAFodHRwczovL3d3dy5waGlsc3Rhci5jb20vbGlmZXN0eWxlL2Zvb2QtYW5kLWxlaXN1cmUvMjAyMy8wNi8wMi8yMjcwNzE0L2hlcmFsZC1zdWl0ZXMtb2ZmZXJzLXR3by1iaWctdmFsdWUtcm9vbS1wcm9tb3MtdW50aWwtanVuZS0zMC9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 02 Jun 2023 07:00:00 GMT", "title": "Herald Suites offers two Big Value Room promos until June 30 - Philstar.com", "content": "Herald Suites offers two Big Value Room promos until June 30\n\nMore than just broadening the hotel's customer niche, Herald Suites has come up with two big value room promotions valid until June 30.\n\nMANILA, Philippines — More than just broadening the hotel's customer niche, Herald Suites has come up with two big value room promotions: Herald Suites \"Suitescapades\" and the \"Bonding Bundle Deal\" valid until June 30.\n\nFor a personal and individual experience for yourself, with friends and family, these promos offer real value for money.\n\nTo avail these Big Value Promos, you may book directly through emailing us and booking through our websites. Don't miss out, inquire and book today!\n\nHerald Suites Solana and Polaris’ Herald Suitescapades\n\nFor Herald Suites Solana call (632) 5336 6151 to 53, 8844 7733, 0917-3189248 (Globe/Viber), 0939-9383717 (Smart), <NAME_EMAIL> or <EMAIL>.\n\nFor Herald Suites Polaris call, 0917-315-9249 (Globe/Viber); 0939-938-3716 (Smart), <NAME_EMAIL> or <EMAIL>.\n\nHerald Suites Solana’s Bonding Bundle Deal\n\nFor updates, visit Herald Suites www.heraldsuites.com, polaris.heraldsuites.com, solana.heraldsuites.com, or follow on Facebook at facebook.com/heraldsuiteshotel, facebook.com/heraldsuitespolaris, or facebook.com/officialheraldsuitessolana."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vZmluYm9sZC5jb20vc29sYW5hLXByaWNlLXJhbGxpZXMtMTAtaW4tYS13ZWVrLWlzLTI1LW5leHQtZm9yLXNvbC_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 02 Jun 2023 07:00:00 GMT", "title": "Solana price rallies 10% in a week; Is $25 next for SOL? - Finbold - Finance in Bold", "content": "After piercing through the $20 barrier earlier this week, Solana (SOL) continues on its upward trajectory, buoyed by growing user engagement and recent bullish comments by the project’s co-founder.\n\nOver the past week, SOL’s price climbed around 10%, propelling the cryptocurrency’s market cap from $7.6 billion to $8.4 billion. At press time, Solana was trading at $21.14, up 1.5% in the past 24 hours.\n\nSOL 1-week price chart. Source: Finbold\n\nMost of SOL’s gains came during the week as the crypto token’s price action over the last month remains in the red, down nearly 4%. Still, year-to-date, Solana’s price jumped by a whopping 112% from just $9.99 at the start of 2023.\n\nSolana currently faces a resistance level of $21.40, which, if broken, could potentially position the cryptocurrency for another major upside move toward the $25 level.\n\nSolana expert insight\n\nMeanwhile, crypto expert <PERSON><PERSON><PERSON> pointed out a crucial support level at $20.73 highlighting this area as ‘a scary spot’ for SOL and breaking below it would likely push the token’s price down as low as $14-$15.\n\nSolana’s key support level. Source: Altcoin Sherpa\n\nOn the other hand, the next obvious resistance level for the cryptocurrency stands at $26, as noted by trading expert <PERSON><PERSON>.\n\n“IF SOL can close above the trailer ($21.3) then longing/buying is maybe a good idea.” – he added.\n\nWhat is driving SOL’s price?\n\nPrimarily, Solana’s latest price rally has been fueled by a drastic surge in the network’s number of unique users.\n\nAccording to on-chain data, the number of active weekly users on Solana skyrocketed to 3.6 million on May 1, marking a significant increase of 146.58% from April 1, according to Dune Analytics data.\n\nChart showing the number of unique active users on Solana. Source: Dune Analytics\n\nFollowing the spike, the cumulative number of unique active users on Solana stood at 39.75 million on May 1, up from 36.15 million a month earlier, the chart shows.\n\nAccording to Messari’s research analyst Ally Zach, one of the factors that attracted new users and played a crucial role in their onboarding process is the shifting focus of Solana’s gateway apps.\n\nAdditionally, new features such as “programmable NFTs and NFT compression, as well as @solana’s low cost, high throughput environment, make it an ideal network for consumer applications to thrive,” Zach added.\n\nOver the past year, the focus of these apps has transitioned through different sectors, from decentralized finance (DeFi), over gaming and non-fungible tokens (NFT), to a mix of the aforementioned markets.\n\nDisclaimer: The content on this site should not be considered investment advice. Investing is speculative. When investing, your capital is at risk."}, {"id": 2, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vZm9ya2FzdC5uZXdzL3NvbGFuYXMtbmZ0LXNhbGVzLWRpdmUtYXMtYml0Y29pbi1vcmRpbmFscy1zdXJnZS_SAUpodHRwczovL2Zvcmthc3QubmV3cy9zb2xhbmFzLW5mdC1zYWxlcy1kaXZlLWFzLWJpdGNvaW4tb3JkaW5hbHMtc3VyZ2UvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 02 Jun 2023 07:00:00 GMT", "title": "Solana’s NFT sales dive as Bitcoin Ordinals surge - Forkast News", "content": "Solana’s secondary non-fungible token (NFT) sales dropped nearly 50% in May, to US$44.9 million from US$85.7 million in April. The Forkast SOL NFT Composite, a measure of NFT activities in the Solana blockchain, also decreased by 12.13% throughout May.\n\nThe dip in Solana’s NFT market coincides with a surge in Ordinals, a type of NFTs on the Bitcoin network. Bitcoin’s monthly NFT sales volume skyrocketed by 474% in May, reaching over US$189 million, according to CryptoSlam data. This boost positioned Bitcoin as the second most popular NFT chain, a title often held by Solana.\n\n“The single reason for the major change in the Solana ecosystem is simply the impact of Bitcoin NFTs,” said <PERSON><PERSON><PERSON>, NFT strategist at Forkast Labs. “Since Bitcoin NFTs launched at the end of January you can see Solana’s numbers steadily decreasing. Sellers, buyers, total transactions, are all 50% or less than they were prior to Bitcoin NFTs.”\n\nBitcoin’s monthly NFT sales multiplied by more than six-fold, growing from US$32 million in April to US$195.7 million in May. The Bitcoin blockchain recorded its 10 millionth Ordinals inscription on Monday, Dune Analytics data showed.\n\n“People who use Solana are likely already Bitcoin users, but it doesn’t work the other way around. I would assume that many who use Bitcoin don’t touch other chains other than Ethereum. The point here is that the Bitcoin blockchain appeals to every single crypto and NFT trader, and the same can’t be said for Solana,” said Petscher.\n\nThe recent surge in memecoins has also factored into Solana’s falling NFT sales, according to Brian Boisjoli, product manager at Forkast Labs.\n\n“The majority of people were trading memecoins instead of NFTs,” said Boisjoli, who has also traded memecoins over NFTs in the recent fad. “It suggests people are going to chase the hot item and leave the last shiny [NFT] in the dust.\n\nInterest in memecoins surged in April, led by tokens like Pepe and Floki that were listed on major crypto exchanges like Binance. Pepe rose nearly 7,000% in two weeks after it launched on April 17 and is up over 2,100% since its launch, according to CoinMarketCap.\n\nThe number of unique NFT buyers on Solana dropped to 56,729 in May from 83,241 in April, according to CryptoSlam.\n\n“When memecoin season settles, I’m sure the capital will come back to NFTs,” Boisjoli added.\n\nWill Bitcoin reach Ethereum’s NFT sales volume?\n\nBitcoin’s May NFT sales of US$195.7 stood at nearly half of Ethereum’s monthly total of US$356 million, which is the largest blockchain by NFT sales volume. Monthly NFT sales volume decreased by 21.98% on Ethereum, and the Forkast ETH NFT Composite fell 4.35%. Meanwhile, Bitcoin’s monthly sales volume increased by 377.25% in the same period, according to CryptoSlam.\n\n“People should really pay attention to how significant, and how disruptive Bitcoin NFTs are. Especially if you are of the mind that Bitcoin NFTs volume could eclipse Ethereum’s in the future. At that point, will there be any demand for blockchains other than Bitcoin, Ethereum, and Ethereum’s layer 2s,” said Petscher.\n\nHe added that Bitcoin will continue to capture market share from alternative chains to reshape the NFT landscape.\n\nThe new BRC-721E token standard launched on Monday enables Ethereum-native NFTs to be transferred to Bitcoin. The new standard permanently burns the ERC-721 NFT on Ethereum, allowing users to claim the recreated version of the NFT on Bitcoin as a BRC-721E inscription.\n\nSee related article: Bitcoin and Ethereum: Two titans battle for NFT supremacy"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiM2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL2Nvc21vcy1ib2FyZHMtZGVmaS10cmFpbtIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 02 Jun 2023 13:59:39 GMT", "title": "Cosmos Boards the DeFi Train with Native USDC Support - Blockworks", "content": "For any decentralized finance or DeFi ecosystem to function efficiently, stablecoins are crucial. They enable liquidity — the easy movement of value from one place to another — with less friction and volatility risk.\n\nBoth Ethereum and Solana have enjoyed vibrant DeFi environments, at least partly due to the ease of access to stablecoin utility within their networks. This has not yet been the case for the appchain ecosystem that calls itself the “internet of blockchains” — Cosmos.\n\nThe folks at Noble and Celestia are well aware of this shortcoming. Recently deciding to team up for the cause, the companies behind Noble, a general asset issuance chain and Celestia, a data availability (DA) layer, are together building native stablecoin capacity into the Cosmos ecosystem.\n\nOn the 0xResearch podcast, Blockworks Research senior analyst <PERSON> says the two companies are partnering up to “enable the minting of native USDC, by default, for any modular chain that leverages Celestia as a DA layer.”\n\nThe move is an important step, <PERSON> says. “In order to have a vibrant DeFi ecosystem, stablecoins are incredibly important.” Stablecoin tokens like USDC and USDT allow investors to generate yield on digital assets while minimizing the risks of market volatility through the token’s steady value, which is pegged to fiat currency.\n\n“That’s been a main pain point for Cosmos,<PERSON> <PERSON> says, “but now that they have USDC, I feel like there’s gonna be a lot less liquidity fragmentation.”\n\n“I definitely think that this is a huge development,” Blockworks Research analyst <PERSON> agrees. “The reason that their DeFi hasn’t taken off, despite them being around for a while, is because there wasn’t that native stablecoin.”\n\nThe ability to use Circle’s USDC stablecoin natively via Celestia’s DA layer will “supercharge” growth for the ecosystem, West believes.\n\nCelestia is a layer-2 rollup that uses light nodes to verify that transaction data is public and available “without needing to download all the data for a block.” This is achieved through a mechanism called “data availability sampling,” according to the company website.\n\n“I think it’s really gonna jump-start things,” West says.\n\nWill Tether miss out?\n\nBlockworks Research analyst Dan Smith asserts that USDC will enjoy “first-mover advantage” as the first native fiat stablecoin in the ecosystem. He imagines the move might encourage leading stablecoin issuer, Tether (USDT) to “copycat” and follow in Circle’s footsteps, “but we haven’t seen any of those actions being taken.”\n\nThree major ecosystems — Solana, Ethereum and the Cosmos ecosystem of appchains — have been at the forefront of development over the past year, Smith says.\n\n“We already have USDC and Tether on Solana,” Smith adds, in addition to their presence on Ethereum and its layer-2s, but “we haven’t really seen that happen in the Cosmos ecosystem.”\n\nSmith says he wonders if the addition of native USDC to Cosmos may spark the beginnings of a “Cambrian explosion of Defi applications and use-cases” in the industry.\n\n“And if we do start moving in this world,” he asks, “is this gonna be a miss for Tether?”\n\n“It’s definitely good for Defi to have that trustworthy fiat stablecoin that can provide that one-to-one pegged value,” but it would be preferable, he says, for “market diversity to shard some of the risk of relying on a single asset.”\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}, {"id": 0, "url": "https://news.google.com/rss/articles/CBMiKWh0dHBzOi8vbWlsa3JvYWQuY29tL3Jldmlld3Mvc29sYW5hLXNhZ2Ev0gEA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 02 Jun 2023 07:00:00 GMT", "title": "Solana Saga Review 2024 - Solana Phone - Milk Road", "content": "What Is A Solana Phone?\n\nThe Solana Saga is a new offering from Solana and OSOM that combines a flagship phone with a clever way to manage built-in wallet security for popular Solana wallets like Solflare and Phantom. So, basically, it’s a crypto phone — with impressive specs and dependable everyday performance.\n\nBefore we get into the nitty-gritty details, let’s answer the question on everyone’s mind:\n\nWhy does this phone exist?\n\nAfter all, I can download a Solana wallet from the Play Store on any Android device, and I’m off to the races — making poorly timed crypto trades like usual, right? Well, yes. But the Saga brings some unique features that put it ahead of the standard solution, namely built-in wallet management.\n\nWe’ll cover that in detail in just a bit. It’s worth understanding, and it’s a key feature that separates the Saga from other phones.\n\nThe Saga is part of a larger goal for Solana to onboard one billion people to the network. That’s a lofty ambition, and the Saga ultimately targets users who use their phones as their primary method of online access. In the short term, it targets crypto geeks — likely why I was chosen for the Solana phone review.\n\nTraditional smartphones and wallet apps work well (sometimes), but there’s also lingering doubt about wallet security in a tech industry where “spying” on users or limiting functionality is commonplace.\n\nThe Saga’s innovative seed vault stores your wallet keys separately from the operating system, safely eluding digital snoops like Google, your mobile network carrier, rogue apps, or whomever else may have an inquisitive interest in what you do with your phone.\n\nThere’s also the question of dApps, decentralized apps that let users play games, swap crypto, earn a yield, and more. Mobile app stores often make it difficult to get apps approved — and then they take a cut of the revenue, like digital tax collectors. That might be fine for Candy Crush, but it’s a non-starter for many crypto applications.\n\nThe solution: The Solana Saga comes with its own dApp store, which operates separately from Google Play.\n\nSolana Saga Phone Specs\n\nNetwork Storage RAM Processor Camera Display Battery OS MSRP Manufacturer Helium Network, T-Mobile, and other 5G (2 SIMS supported) 512GB internal storage + additional MicroSD support (MicroSD card not included) 12GB RAM Qualcomm Snapdragon 8+ Gen1 50MP and 12MP ultrawide 6.67 inch 1080p AMOLED screen 120Hz 4,110mAh Android 13 $1,000 OSOM\n\nHow Much Is A Solana Phone?\n\nThe Solana phone price is $1,000, and it’s available now — or soon, at least. The official Solana Saga phone release date is May 8th, 2023. If you pre-ordered, you can get one now. If you haven’t ordered the Saga yet, you can sign up on Solana Mobile to get notified when handsets are available to ship.\n\nKey Features\n\nSeed Vault: The Saga keeps your private keys private. Not even the built-in wallet apps know the recovery phrase.\n\nThe Saga keeps your private keys private. Not even the built-in wallet apps know the recovery phrase. Solana dApp Store: Choose from a selection of Solana-powered apps that are managed separately from the Google Play Store.\n\nChoose from a selection of Solana-powered apps that are managed separately from the Google Play Store. Android 13: With the latest version of Android paired with powerful hardware, the Saga can be anything you want it to be.\n\nMilk Road’s Solana Saga Review\n\nSolana was kind enough to send us a demo for our Solana Saga phone review. Sadly, we don’t get to keep it, but I wish we could. Overall, the Saga is a solid device with a promising approach to crypto wallet security.\n\nWe’ll get into the weeds on the security part in a bit.\n\nSetup\n\nInside the slick-yet-discreet packaging, we have the basics you’d expect:\n\nSolana Saga phone\n\nUSB-C charging cable (wireless charging supported)\n\nSIM card tool (but no SIM card; I popped in a T-Mobile SIM)\n\nInstructions (I read them this time, sorta)\n\nSeed phrase recovery sheet\n\nThe setup is standard Android (13) fare, centered on a Google account, with the ability to add additional accounts. If you’re an Android veteran, nothing new here — until you get to the seed vault, up next.\n\nSeed Vault\n\nThe Solana seed vault is where the Saga zigs while the other smartphones zag.\n\nAs experienced Solana users know, Solana wallets like Phantom and Solfare use a 12-word seed phrase to build or restore a wallet’s private keys (the keys that let you spend crypto). Solana’s seed vault ups the ante by creating a secure key with a 24-word seed phrase in encrypted storage and then letting select apps — the aforementioned Phantom and Solflare wallets — access the seed vault for secure transactions.\n\nThese apps aren’t installed through Google Play but rather through Solana’s own dApp Store.\n\nThink of it like compartments:\n\nEncrypted seed vault (knows your seed phrase) Solana dApp wallets (don’t know your seed phrase) Solana dApps (just talk to the wallet)\n\nAndroid 13 OS Other apps\n\n\n\nThis setup keeps your seed phrase separate from the OS and other apps.\n\nOf note, the seed vault only works for Solana and the two wallets mentioned above. If you need to use another blockchain, the setup works as it would on any other Android phone; you set up or restore the seed phrase from within the app itself.\n\nThe seed vault’s 24-word seed phrase works with other Solana wallets as well.\n\nIn the interest of science, I imported the wallet into the Phantom Wallet Chrome extension without issues. After a couple of tries, I was able to do the same on another Android phone, but the difficulty may have been a hardware issue. The phone in question was once rescued from an ice-encrusted snowbank in what we’ll just call “The Klondike Derby sled incident.”\n\nIn short, if you were to run over your shiny Saga with a heavy, hand-crafted sled, for instance, you can still recover your crypto wallet, albeit in a less secure way than using Saga’s seed vault.\n\nSolana dApp Store\n\nWhat good is a wallet without apps? The Solana Saga answers this question as well, sorta.\n\nThe Solana Saga phone comes with 18 available dApps you can download. During seed setup, you can automatically download one or both of the wallets. Other dApps, like Marinade (liquid Solana staking, anyone?) and Jupiter (swaps galore), are available to install through a dedicated dApp store.\n\nUpdates and permissions for these Solana dApps are also separate from the Play Store.\n\nWhile the dApp selection is slim pickin’s thus far, some of the top names are already there, and you can expect the selection to grow over time. The dApp store is built around the Solana Mobile Stack (SMS), which is an open-source toolkit that helps developers build mobile apps.\n\nHere’s what you’ve got to play with:\n\nWallets DeFi NFTs Social Game (singular) Other dApps Phantom\n\nSolflare\n\nLedger\n\nLiveSquads (multisig) Marinade\n\nJupiter\n\nTulip\n\nMango Pay Nokiaman\n\nMinty Fresh\n\nTIEXO\n\nMagic Eden Dialect\n\nAudius Music\n\nRosen Bridgeur\n\nFeed Alpha League Racing Workspace\n\nWallets\n\nIf you have a favorite wallet between Phantom and Solflare, you might be happier just choosing one. I ended up removing Solflare to cut down on the number of screens I had to navigate to perform simple tasks. I also encountered an error on Solflare when minting an NFT.\n\nPilot error, perhaps. But like main squeezes, wallets are generally more agreeable if you only have one.\n\nEach of the wallets supports the seed vault as well as biometric authentication. Approve transactions with the touch of a finger. They both also have links to some additional dApps within the wallet apps themselves. For instance, Phantom features Solend (a crypto lending app), and Solfare has a built-in browser, so you can venture off into the DeFi weeds if you’re so inclined.\n\nAlternatively, you can use Chrome or another browser to reach DeFi apps. I connected to Solend and Marinade through Chrome by choosing “Mobile Wallet Adapter” in the connect menu. This magically summons your seed vault wallet of choice, Phantom in my case.\n\nMinty Fresh\n\nI couldn’t resist. The Saga, with a bit of help from Minty Fresh, made minting what might be the first-ever Milk Road NFT a breeze. Minty Fresh is among the 18 apps available in the dApp store, and it’s super easy to use.\n\nThat little cube icon on the top right is a dApp update.\n\nThe NFTs you mint on Minty Fresh come from your own images or photos. If you’ve always wanted an NFT of your big toe or your dog who barks like a nut during Zoom calls (I have one of those), this is your chance to mint one for pennies.\n\nSeparately, the Saga comes with a complimentary Genesis NFT — and a few bucks to get you started on your Solana journey. Nice touch.\n\nJupiter And Marinade dApps\n\nFree money is great and all, but most of it was in USDC, which won’t appreciate in value. That won’t do, so I swapped it for some SOL on Jupiter (a Solana-powered decentralized exchange) and then staked some SOL on Marinade to earn a yield with liquid Solana staking.\n\nYou can also just swap for mSOL (Marinade staked SOL) to save a step.\n\nIn fact, there isn’t much you can’t do in the Solana ecosystem with the Saga’s installed apps, but it would be nice to see a greater selection — a minor complaint likely to be sorted as more developers port their dApps using the Solana Mobile Stack.\n\nFees\n\nOutside of network fees for transactions, swap fees, and staking fees (all normal), I didn’t encounter any extra fees at all. Thus far, the Solana dApp store is free to use with no hidden costs. This might change at some point in the future. There are no guarantees in crypto.\n\nPhone Performance\n\nI read more than one Solana Saga review while waiting for the test phone to arrive. Some suggested issues with the fingerprint sensor, a feature that can come up often when using your fingerprint to authorize wallet transactions. I didn’t find any problems in that regard — outside of my faulty aim. Your mileage may vary.\n\nTo reduce the number of interactions with the fingerprint sensor, you can “trust” certain dApps, reducing the number of times the Saga asks you for permission. I trust no one, so I opted not to do this. Alternatively, you can use a PIN.\n\nMy daily driver is an old phone that was run over by a sled, so I can’t compare the Saga to my iPhone 14 or Galaxy S23. But for most folks’ needs, the Saga is a solid device with zippy performance, great battery life, and a clear, bright screen. This phone offers more power than needed for most use cases. The seed vault and dApp store just add to the appeal.\n\nOh, and the pictures it takes with that 50MP camera are gorgeous.\n\nCustomer Support\n\nThe Saga is built by OSOM, a company focused on privacy tools (like the seed vault). Support, however, is accessed through Solana Mobile. The site offers a ticket system or a chat (email required), as well as several FAQ-style help articles. We didn’t find a phone number for support.\n\nThe Saga comes with a 1-year limited warranty against defects available for the original owner only.\n\nWho’s The Solana Saga For?\n\nSolana ecosystem users: The Saga has a niche audience. For the same money, you could wrangle a deal on a Samsung S23 with similar specs and a Gen2 processor. Buuuuut, you’d forgo the seed vault, which is super cool if you’re a Solana ecosystem user. And honestly, the Gen1 processor is plenty fast.\n\nThe Saga has a niche audience. For the same money, you could wrangle a deal on a Samsung S23 with similar specs and a Gen2 processor. Buuuuut, you’d forgo the seed vault, which is super cool if you’re a Solana ecosystem user. And honestly, the Gen1 processor is plenty fast. Solana devs: If you’re developing for this phone or future Solana Mobile endeavors, you need one to test the user experience. The good news is that the Saga is more than just a dev toy. It’s a solid device worthy of everyday use.\n\nIf you’re developing for this phone or future Solana Mobile endeavors, you need one to test the user experience. The good news is that the Saga is more than just a dev toy. It’s a solid device worthy of everyday use. Casual gamers: I absolutely crushed my competitor in Clash Royale. The Saga’s bright colors and crisp sound made victory taste even sweeter.\n\nWho’s It Not For?\n\nBudget-minded buyers: You won’t find a free (with a 2-year contract) phone offer on the Saga. The Solana phone price is $1,000, with no apologies, and there’s a waiting list.\n\nYou won’t find a free (with a 2-year contract) phone offer on the Saga. The Solana phone price is $1,000, with no apologies, and there’s a waiting list. Apple ecosystem people: The Saga won’t AirDrop an Instagram pic to your MacBook Air or FaceTime to your Apple Watch. This phone is better suited to Google ecosystem people or those who simply want the Saga for its crypto-centric approach.\n\nWhat Could Be Better?\n\nIf the goal of the Saga and the Solana network as a whole is to onboard a billion people, the seed vault is a step in the right direction — but only the first step of a long journey. We still haven’t escaped the recovery phrase paradigm that’s challenged the industry since the beginning.\n\nIs there a way to hide this geekiness in the background for average users? Most people won’t want to fuss with a 24-word recovery phrase, keeping it under lock and key. A new approach might be needed at some point to reach Solana’s goal. (But it’s still a great phone.)\n\nFinal Thoughts On The Solana Saga Phone\n\nOne of the first things we did in this Solana Saga review was to answer the question Why — as in, why does this phone exist? The Saga isn’t the most powerful phone out there, nor is it the cheapest. But its innovative approach to wallet security is something you won’t find anywhere else. And all things considered, it’s a solid device with plenty of power and storage for average users. It looks cool too. Gotta love those green buttons. Sold. I’ll take it.\n\nFrequently Asked Questions"}]