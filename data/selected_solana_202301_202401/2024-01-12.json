[{"id": 14, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vd2F0Y2hlci5ndXJ1L25ld3Mvc29sYW5hLXNvbC13ZWVrZW5kLXByaWNlLXByZWRpY3Rpb24tNdIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 12 Jan 2024 08:00:00 GMT", "title": "Solana SOL Weekend Price Prediction - Watcher Guru", "content": "Solana [SOL], a digital currency that has recently gained attention due to its price fluctuations, is poised for an intriguing weekend. Following a surge to an impressive $106.95, the cryptocurrency underwent a correction, bringing it down to $97.83. As of the current moment, SOL is being traded at $99.91, reflecting a modest 1.5% increase. This article will delve into Solana’s weekend price prediction and explore its anticipated performance throughout the year 2024.\n\nPrice Forecast for January 2024\n\nUpon analyzing the price movements observed at the onset of 2023, experts in the cryptocurrency field anticipate an average SOL rate of $100.23 in January 2024. The expected range for the month varies between a minimum of $99.99 and a maximum of $100.47.\n\nWeekend Prediction for Solana\n\nZooming in on the imminent weekend, specifically on January 13, projections indicate that SOL is likely to undergo a 12% dip, bringing its value down to $87.59. Despite this decline, optimistic market sentiments suggest a subsequent recovery, with the altcoin expected to surge by 5% and attain a high of $104.48.\n\nAlso Read: Here’s How Much $1,000 Invested in Solana (SOL) 3 Years Ago Would be Worth Now\n\nOutlook for 2024: Unveiling Solana’s Strength\n\nFurther contributing to the positive market indicators, Solana recently unveiled its 2024 outlook report, offering insights into the network’s strength as it enters the new year. The report underscores key metrics that underscore the resilience of the Solana ecosystem.\n\nA notable highlight is the impressive developer community, boasting over 2500 participants. According to the report, the number of monthly active developers has consistently ranged from 2500 to 3000 throughout the past year, demonstrating the ecosystem’s ability to attract and retain talent.\n\nAnother encouraging aspect is the proficiency of Solana developers, with more than half possessing over three years of experience. This skilled developer pool significantly contributes to the success of applications built on the network, enhancing the platform’s appeal to users.\n\nGlobal expansion remains a focal point for Solana, with active efforts to onboard developers from diverse regions worldwide. Communities in India, Taiwan, Singapore, the United States, and others have played a crucial role in expanding the platform’s global reach. The recent Solana Hyperdrive hackathon, which featured participants from 151 countries, serves as a testament to the widespread appeal of the platform.\n\nAlso Read: Solana [SOL] Weekly Price Prediction\n\nIn conclusion, despite the recent price correction, Solana SOL’s price outlook for the upcoming weekend and the entirety of 2024 remains positive. The blockchain platform, with its thriving developer community, skilled talent pool, and global expansion initiatives, positions itself as a formidable contender in the competitive cryptocurrency landscape. As the cryptocurrency market continues to evolve, Solana appears well-positioned to navigate the waves of strength and make a significant impact in the year ahead. Investors and enthusiasts alike will be closely monitoring weekend price movements, eager to witness whether Solana can live up to the predictions and sustain its positive momentum."}, {"id": 8, "url": "https://news.google.com/rss/articles/CBMiRmh0dHBzOi8vY29pbmdhcGUuY29tL3NvbGFuYS11bnZlaWxzLTIwMjQtcm9hZG1hcC1zb2wtcHJpY2UtdG8tcmVib3VuZC_SAUpodHRwczovL2NvaW5nYXBlLmNvbS9zb2xhbmEtdW52ZWlscy0yMDI0LXJvYWRtYXAtc29sLXByaWNlLXRvLXJlYm91bmQvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 12 Jan 2024 08:00:00 GMT", "title": "Solana Unveils 2024 Roadmap, SOL Price To Rebound? - CoinGape", "content": "Solana emerges as a prominent player as the cryptocurrency landscape evolves, gearing up for a transformative 2024. Notably, the recent updates from Solana, as shared on the X platforms, shed light on the platform’s robust developer community, surging on-chain activity, and exciting prospects for the coming year.\n\nMeanwhile, the announcement highlights that the developers in the Solana ecosystem can anticipate groundbreaking enhancements, from token extensions to institutional support, promising a year of innovation and growth.\n\nadvertisement\n\nSolana Unveils Roadmap For 2024\n\nSolana’s meteoric rise in 2023 sets the stage for an even more promising 2024. Boasting a thriving community of over 2,500 developers, the platform has witnessed a remarkable increase in dev retention, now surpassing 50%. In addition, with 40.7 million daily user transactions and an upsurge in daily fee payers, Solana has become a hub for blockchain enthusiasts.\n\nBesides, in the early months of 2024, Solana developers can expect the arrival of token extensions, a feature designed in collaboration with major institutions, according to its recent announcement. These extensions bring versatility to token creation, simplifying the process and ensuring compatibility. Other functionalities include confidential transfers, transfer hooks, and metadata pointers, paving the way for a more open and customizable token platform.\n\nOn the other hand, the introduction of Firedancer, Solana’s ground-up rebuild of the validator client, promises to optimize networking, runtime, and consensus components. Notably, with benchmarks showcasing over 1 million transactions per second per core, Firedancer eliminates bottlenecks, ensuring scalability with future hardware advancements. Coupled with other validator clients, this development enhances the network’s resilience, stability, and security.\n\nMeanwhile, over the past year, Solana has garnered support from large traditional institutions, offering blueprints on AWS for blockchain node development. This enables enterprises to deploy their consensus and RPC nodes effortlessly, fostering more widespread adoption of Solana in the business world. Besides, Solana data is now live on Google Cloud’s BigQuery, further integrating the platform into mainstream cloud services.\n\nAlso Read: XRP Whale Shifts 48 Mln XRP As Price Nears $0.6\n\nPrice & Performance\n\nSolana has been one of the top percentage gainers in 2023, gaining attention from global investors. However, the bull run seems to have slowed this year, with the traders’ shifting focus towards the Spot Bitcoin ETF approval and other major developments in the crypto space.\n\nAs of writing, the Solana price is down 7% to $90.9, with its trading volume declining to $2.76 billion. However, over the last 24 hours, the crypto has touched a high of $100 and a low of $88. In addition, the recent analysis of the Solana charts and patterns also suggests that the asset might see a rebound in the near future.\n\nMeanwhile, the Solana ecosystem has matured significantly, providing developers with a rich project toolkit. Its recent announcement showed that with an influx of tools, educational courses, and documentation, developers can now write in up to 12 programming languages.\n\nNotably, updates include Gameshift API for gaming integration, toolkit enhancements for token launches by Armada Markets, Solana Permissioned Environments (SPEs) for large enterprises, and the upcoming Runtime v2 focusing on faster execution and enhanced composability.\n\nAlso Read: Shiba Inu Burn Rate Soars 400% As SHIB Price Continues To Rally"}, {"id": 5, "url": "https://news.google.com/rss/articles/CBMihgFodHRwczovL2NvaW5jb2RleC5jb20vYXJ0aWNsZS8zNjYyMi9zb2xhbmEtaXMtZ2VhcmluZy11cC1mb3ItYS1iaWctMjAyNC11cGdyYWRlZC10b2tlbi1mZWF0dXJlcy1hbmQtbmV3LWNsaWVudC10aGF0LWNhbi1oYW5kbGUtMW0tdHBzL9IBiQFodHRwOi8vYW1wLmNvaW5jb2RleC5jb20vYXJ0aWNsZS8zNjYyMi9zb2xhbmEtaXMtZ2VhcmluZy11cC1mb3ItYS1iaWctMjAyNC11cGdyYWRlZC10b2tlbi1mZWF0dXJlcy1hbmQtbmV3LWNsaWVudC10aGF0LWNhbi1oYW5kbGUtMW0tdHBzLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 12 Jan 2024 08:00:00 GMT", "title": "Solana is Gearing Up for a Big 2024 — Upgraded Token Features and New Client that Can Handle 1M+ TPS - CoinCodex", "content": "Solana is Gearing Up for a Big 2024 — Upgraded Token Features and New Client that Can Handle 1M+ TPS\n\nKey highlights: In 2024, the Solana blockchain will benefit from upgraded token functionality through new features like confidential transfers, transfer hooks, and metadata pointers. This will make tokens more customizable and functional for dApps.\n\nWe will also see the release of the Firedancer validator client, which is written from scratch for maximum optimization of performance. Early tests show the Firedancer validator can handle more than 1 million transactions per second.\n\nRuntime v2 will simplify and improve composability between Solana programs, enhancing the developer experience.\n\nSolana users and developers have plenty to look forward to in 2024\n\nThe Solana blockchain platform is gearing up for a potentially massive 2024, with a roadmap focused on major upgrades to token functionality and core infrastructure that could lay the foundation for the network to scale to over 1 million transactions per second.\n\n1/ 2024 is the year of Solana ?☀️\n\n\n\nThe network is strong heading into 2024, with recent reports highlighting the 2500+ developer community, 40.7 million daily user transactions, & mature tooling.https://t.co/ZHlK9M8cUk\n\n\n\nThis is why Solana is the place to build in 2024?? — Solana (@solana) January 11, 2024\n\nToken extensions will provide more flexibility for Solana-based tokens\n\nSolana developers have been hard at work building new features and optimizing the blockchain platform's capabilities. One of the most anticipated upgrades is the addition of token extensions, which will add features like confidential transfers, transfer hooks, and metadata pointers directly into Solana's token programming.\n\nWith this, confidential transfers will enable private transactions, while transfer hooks allow custom logic on token movements. Metadata pointers provide a way to store supplemental off-chain data.\n\nAdditionally, these extensions will make token creation and usage far more customizable on Solana without requiring external smart contracts. The new options cater to decentralized applications across DeFi, NFTs, governance, rewards programs and more. Developers from projects and institutions like Pyth, Jump Crypto and others contributed to the design.\n\nThe Firedancer client is poised to significantly boost Solana's scalability\n\nOn the infrastructure side, Solana Labs is collaborating with Jump Crypto on an entirely new validator client called Firedancer. Built from the ground up, Firedancer aims to optimize networking, consensus rules and runtime processing in order to smash through current software bottlenecks.\n\nAccording to revealed benchmarks, Firedancer can already handle over 1 million TPS per core in testing environments. As new validators running Firedancer come online, the mainnet could see dramatic leaps in throughput and efficiency. The client diversity also improves decentralization and resilience.\n\nBeyond the headline upgrades, Solana developers are working on Runtime v2, a rebuilt VM engine focused on simplicity and composability between programs. Solana Pay also continues to expand as a payments SDK, with a new Shopify plugin that enables USDC acceptance.\n\nSolana has already seen tremendous growth since launching in 2020, with over 400 projects spanning DeFi, NFTs, Web3 and more. The network touts advantages in speed, low costs and emerging mobile capabilities. Its native token SOL, hit a peak price over $260 in late 2021.\n\nHowever, Solana faces stiff competition from rival layer 1s like Ethereum, Polkadot, Near and Algorand, all of which boast innovations in scalability, security and usability.\n\nIn terms of price action, SOL has been relatively quiet as of late after being one of the crypto market's most dominant forces in 2023. Currently, SOL is down 4.6% on a 7-day basis, but is still posting a very strong 38% gain in the last 30 days."}, {"id": 1, "url": "https://news.google.com/rss/articles/CBMiSmh0dHBzOi8vd3d3Lm5ld3NidGMuY29tL25ld3Mvc29sYW5hL3NvbGFuYS0yMDI0LXJvYWRtYXAta2V5LXByaWNlLXRhcmdldHMv0gFOaHR0cHM6Ly93d3cubmV3c2J0Yy5jb20vbmV3cy9zb2xhbmEvc29sYW5hLTIwMjQtcm9hZG1hcC1rZXktcHJpY2UtdGFyZ2V0cy9hbXAv?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 12 Jan 2024 17:05:30 GMT", "title": "Solana In 2024: Roadmap And Key Price Targets For This Year - NewsBTC", "content": "The Solana Foundation has unveiled a roadmap for 2024, focusing on innovation, developer engagement and network scalability. “2024 is the year of Solana,” the foundation proclaimed, focusing on the key milestones achieved and strategic goals for the year in the statement.\n\nThe “Solana Foundation’s State of Developer Ecosystem Report” highlights a surge in developer activity, with over “2,500 active developers committing to open source repositories” and an impressive increase in developer retention, rising “from 31% to over 50% throughout the previous year.”\n\nThe report further elucidated the evolution of Solana’s infrastructure, which in 2023 saw a leap in maturity with the deployment of “program frameworks for Rust, Python, and more,” as well as “SDKs available for 10 languages, laying a solid foundation for diverse dApp development.” Solana Labs’ innovative GameShift API has been a game-changer, a piece of “app-specific tooling” designed to revolutionize the gaming space on Solana’s blockchain.\n\nRelated Reading\n\nMassive Solana Heist: CLINKSINK Drainer Campaigns Swipe Nearly $1M Worth Of SOL 4 months ago\n\nOn-chain data provided by Messari reinforces the network’s growth narrative, citing “a 65% quarter-over-quarter increase in daily average non-voting transactions, reaching 40.7 million, and a remarkable 102% quarter-over-quarter rise in average daily fee payers, amounting to 190,000.”\n\nThe roadmap also unveils forthcoming advancements poised to redefine blockchain capabilities. First, Solana aims to introduce “token extensions to empower more complex and multifaceted tokenomics.”\n\nSecond, a focus in 2024, will be on the launch of Firedancer, a new independent validator client for the Solana blockchain, built by Jump Crypto. It aims to support a higher number of concurrent transactions, increase network throughput, resilience, and efficiency, and address historical weaknesses in Solana’s peer-to-peer interface. Notably, Firedancer went live on the testnet in October 2023.\n\nA third focus in the 2024 roadmap will be the development of Runtime v2 by Solana Labs, which aims to “significantly enhance the network’s performance and developer experience.”\n\nThis runtime is a concurrent transaction processor, handling transactions with specified data dependencies and explicit dynamic memory allocation. It introduces changes coordinated by epochs, influencing the cluster’s behavior. Moreover, Solana Core announced support for the Move programming language as a major modification in Runtime v2.\n\nRelated Reading\n\nSolana Poised For Major Upside: Analyst Predicts 47% Surge After Price Breakout 4 months ago\n\nThe Solana Foundation’s message via X echoes a commitment to innovation and community engagement: “The strength of the Solana ecosystem is amplified by our passionate community. With the community’s unwavering support, we are ready to accelerate into 2024 and solidify Solana’s position as the premier platform for blockchain development. It’s time to accelerate. Let’s keep building & make 2024 the year of Solana.”\n\nSolana Price Prediction 2024: A Technical Analysis\n\nA technical analysis of the Solana price action in the weekly chart (SOL/USD) offers predictions for 2024. Since mid-November, SOL’s price movement has been encapsulated within a parallel uptrend channel, indicative of a stable and consistent upward trajectory. The parallel lines representing support and resistance have guided the price action, providing clear levels for potential buy and sell points.\n\nThe Fibonacci retracement tool, applied from the swing high of around $262 to the swing low of $7.93, unveils key levels that may act as barriers or support in the future.\n\n0.236 at $69.59: A retracement level that previously acted as resistance and has turned into support.\n\n0.382 at $107.74: This level has been tested and is the next major price target of a weekly close.\n\n0.5 at $138.57: From 2021 till early 2022, this price level acted as strong support, but was turned into resistance in April 2022.\n\n0.618 at $169.41: Often considered the ‘golden ratio,’ this level is crucial for assessing trend continuation.\n\n0.786 at $213.30: Breaching this level could signal strong bullish momentum.\n\n1 at $269.22: As soon as SOL reaches its all-time high, the price discovery phase begins.\n\nExtended Fibonacci levels, such as 1.618 at $430.69 and 2.618 at $691.98, offer aspirational targets should the uptrend persist. The latter would be an ultra-bullish price target.\n\nThe Exponential Moving Averages (EMA) for the 20, 50, 100, and 200 periods all lie below the current price, confirming the strength of the uptrend. A ‘golden cross’ is evident in mid-December with the 50-EMA crossing above the 100-EMA, traditionally a bullish signal.\n\nThe trading volume shows a constructive pattern, with higher volume seen on upswings, a positive sign for continued interest in SOL. The Relative Strength Index (RSI) is positioned around 60, suggesting that while the momentum is upward, there is still room for growth before reaching overbought conditions.\n\nThe technical analysis, grounded in the weekly chart’s display of a parallel uptrend channel, robust Fibonacci levels, supportive moving averages, and healthy volume and RSI readings, paints a very optimistic price outlook for Solana in 2024.\n\nFeatured image from Shutterstock, chart from TradingView.com"}, {"id": 7, "url": "https://news.google.com/rss/articles/CBMiP2h0dHBzOi8vYmxvY2t3b3Jrcy5jby9uZXdzL3BhcmFsbGVsaXplZC1ldm1zLWdhaW5pbmctcG9wdWxhcml0edIBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Fri, 12 Jan 2024 13:05:41 GMT", "title": "Parallelized EVMs are gaining popularity, but they won't scale blockchains alone - Blockworks", "content": "Improving technology for scaling blockchains requires tackling multifaceted problems. New attempts to parallelize the Ethereum Virtual Machine (EVM) have gained interest in recent weeks, as developers look for possibilities to increase transaction throughput.\n\nThe EVM is the execution environment that runs on Ethereum and its layer-2 rollups, and also has been adopted by other layer-1 networks such Avalanche, Binance Chain and many others, which use it to process smart contract code.\n\nIn typical EVM implementations, transactions are processed one after the other in the order they arrive. This simple approach can lead to longer processing times and higher costs when there are many transactions, explains <PERSON>, the CEO of Neon Labs, which is implementing the EVM as a smart contract on Solana.\n\n“For Ethereum, the traditional sequential execution model simplifies hardware requirements, but on the downside, it creates limitations in handling the increasing volume of transactions,” <PERSON><PERSON><PERSON> told Blockworks. “This worsens during peak usage times resulting in longer transaction wait times, higher gas fees vis-a-vis demand surge, and an overall fractured user experience.”\n\n<PERSON><PERSON><PERSON> said that much of the growing interest in parallel EVMs is due to its ability to tackle these challenges around blockchain scalability, addressing issues related to the blockchain trilemma.\n\n“The parallel processing approach addresses the longstanding challenges of scalability and transaction processing efficiency within the Ethereum network,<PERSON> <PERSON><PERSON><PERSON> said.\n\nMany transactions can be processed at the same time, instead of sequentially, so long as they don’t depend on each other.\n\nThere are other ways to achieve parallel processing, such as at the database-level with approaches such as sharding, that essentially divide Ethereum’s state into smaller “shards” but full sharding remains years away.\n\nRead more: Dencun and Pralectra: Ethereum core devs chart an ambitious 2024\n\nParallelized EVMs, however, including the likes of Sei network, Monad and Neon, are already production ready or nearly so. The Sei network launched its mainnet in August 2023 and has seen recent monthly trading volume of about $136 million with a little over 29 thousand active DEX users, according to data compiled by Flipside Crypto.\n\nIts native token, SEI, has surged from around $0.23 at the beginning of December to around $0.68 today — an almost 200% jump. Neon too, has seen a significant increase in its token price, with NEON trading from $0.50 on Dec. 1 to roughly $2.55 today.\n\nMonad has yet to launch and does not yet have its own token.\n\nA look into popular parallel EVM protocols\n\nMuch of the work being done on the Neon EVM is to bring the Ethereum dapps into the Solana ecosystem, Guryeva said.\n\n“It is a fully compatible Ethereum environment on Solana and allows developers to scale Ethereum dapps using Solana as the settlement layer,” she said.\n\nGuryeva notes that Ethereum remains the dominant blockchain that supports smart contracts and dapps, but Solana’s Sealevel technology — which enables virtual machines to process multiple transactions simultaneously through distributing transaction workload across multiple threads on a validator’s hardware — enables lower gas fees and higher scalability.\n\nJay Jog the co-founder of Sei Labs shares this sentiment, he notes that Ethereum rollups struggle to get over 30 sustained transactions per second, which limits the design space on the blockchain.\n\n“A parallelized EVM lets you get the best of Ethereum and Solana — the EVM and all of the mindshare/tooling around it and the fast execution environment that Solana offers,” he said.\n\nSei’s latest version has its EVM built into the chain, Jog added.\n\n“Sei uses Geth — the Go implementation of the EVM — to get full EVM bytecode compatibility. Geth is very battle tested and currently processes ~85% of [Ethereum] blocks,” he said.\n\nThis is different from its competitor Monad, which is creating a custom EVM implementation, Jog notes.\n\nDespite these differences, both Monad and Sei are using optimistic parallel execution to achieve parallel processing.\n\nWith this technique, virtual machines run in parallel on separate threads within the network computer. Each thread would execute transactions and generate what Keone Hon, the co-founder and CEO of Monad Labs, calls “pending results.”\n\nThese pending results track the inputs and outputs of the transactions which are then committed back into their original linear order. If any pending result has inputs that have been invalidated, then a transaction must be rescheduled.\n\n“This means that Monad still supports linear blocks and linear transactions within the blocks,” Hon told Blockworks. “Monad can basically take transaction formats that are exactly the same as Ethereum and then, under the hood, implement parallelism without any changes from the user perspective.”\n\nOptimistic parallel execution is different from what Solana uses, for example, where dapps have to pre-specify dependencies of the transactions, Hon said, analogous to the access list approach on Ethereum.\n\nParallelized EVMs are a stepping stone\n\nIt is important to note, Hon adds, that parallel execution is a small fraction of the bigger picture, with Monad’s more general mission being centered around accelerating EVM execution to make it as performant as possible.\n\n“The real bottleneck is actually the state access that all of those transactions are being run, they all have dependencies on state, and that state lives on SSD (solid-state drive),” Hon said.\n\nThe majority of smart contract work has relatively simple computation, he said, and bigger constraints for smart contracts are often the input/output component — the process of reading from the SSD and then writing back data.\n\nParallelism only cuts a small fraction of costs, according to Rachel Bousfield, the tech lead at Offchain Labs, because parallel execution is rarely possible in practice across current Web3 dapps.\n\nLoading Tweet..\n\nEthereum-like blockchains generally use commodity databases for reading and writing data. These have the advantage of being widely accessible, purchased “off the shelf,” but they are not optimized for the problem of storing the Merkle tree data, necessary for parallel processing on the blockchain, Hon notes.\n\nThis means that, fundamentally, in order to decrease transaction processing times and gas prices, infrastructure around the database will have to change.\n\n“So one of the other really major things that Monad is building is a custom state database from scratch that supports parallel access so that when there are many threads running in parallel, touching the database — to get inputs, or write outbacks back to it — those can proceed in parallel,” Hon said.\n\nMonad is not alone in focusing on alleviating state dependencies. Sei too, is working to make improvements in this area, Jog notes.\n\n“We are doing a lot of work on the state layer to improve state access, state commit, and state storage,” he said.\n\nStart your day with top crypto insights from David Canellis and Katherine Ross. Subscribe to the Empire newsletter.\n\nThe Lightspeed newsletter is all things Solana, in your inbox, every day. Subscribe to daily Solana news from Jack Kubinec and Jeff Albus."}]