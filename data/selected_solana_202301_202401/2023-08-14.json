[{"id": 11, "url": "https://news.google.com/rss/articles/CBMiSWh0dHBzOi8vd3d3LmRlbG1hcnRpbWVzLm5ldC9uZXdzL3N0b3J5LzIwMjMtMDgtMTQvc29sYW5hLXNhbnRhLWZlLXJlbW9kZWzSAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 14 Aug 2023 07:00:00 GMT", "title": "Solana Santa Fe's new two-story building completes campus modernization - Del Mar Times", "content": "After being under construction for a little over two years, the Solana Santa Fe School modernization is now complete.\n\nThe temporary fencing along El Apajo Road came down just days before students returned to the Rancho Santa Fe campus to meet their teachers. Principal <PERSON> admittedly fretted that everything would get done in time and it was a relief that teachers were able to move into their new classrooms on schedule, with just finishing touches and last checklist items remaining before the first day of school on Aug. 14.\n\n“The teachers, students and the entire community showed an unbelievable amount of patience and we’re really grateful for what we got out of it,” <PERSON><PERSON><PERSON><PERSON> said.\n\nA classroom inside the new building at Solana Santa Fe. (<PERSON>)\n\nNew this school year is Solana Santa Fe’s two-story classroom building, home to eight classrooms for first through fourth grades as well as a learning center. The building blends perfectly into the existing campus, with some modern touches but architecture that matches the look of the Fairbanks Ranch community.\n\nThe school also debuted an improved parking lot, with extended queues to keep the drop-off and pick-off lines flowing and avoid traffic backups on El Apajo and San Dieguito Road. A new pick-up area was also created for students, with multiple benches to wait for rides.\n\nDuring the summer, the school’s front office was also tweaked to improve efficiency.\n\nThis summer’s work caps off last summer’s construction which included the revamped front entrance and administrative offices, new lunch shelters and school kitchen, an expanded kindergarten play area and new staff parking lot. The school’s fire alarm and heating and air conditioning systems were also upgraded.\n\nIn addition to the new building, all of the campus classrooms received new carpet and new furniture over the summer: “The teachers are thrilled,” <PERSON>umovitz said.\n\nThe $26 million project was funded by a combination of Measure JJ and community facilities district monies from the Crosby and Pacific Highlands Ranch.\n\nSince the Solana Beach School District’s general obligation bond passed in 2016, Measure JJ projects have included the Solana Highlands and Solana Santa Fe modernizations and the new Skyline and the new Solana Vista campuses. Next up will be modernization projects at Carmel Creek and Solana Pacific Schools.\n\n"}, {"id": 4, "url": "https://news.google.com/rss/articles/CBMiT2h0dHBzOi8vYW1iY3J5cHRvLmNvbS9zb2xhbmEtdnMtcG9seWdvbi1ob3ctaXMtMjAyMy10dXJuaW5nLW91dC10by1iZS1mb3ItYm90aC_SAVNodHRwczovL2FtYmNyeXB0by5jb20vc29sYW5hLXZzLXBvbHlnb24taG93LWlzLTIwMjMtdHVybmluZy1vdXQtdG8tYmUtZm9yLWJvdGgvYW1wLw?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 14 Aug 2023 07:00:00 GMT", "title": "Solana vs Polygon: How is 2023 turning out to be for both? - AMBCrypto News", "content": "Solana’s TVL gained and hit its highest level since the beginning of 2023\n\nMATIC’s market cap plummeted in June, while SOL registered gains\n\nOver the last few months, Polygon [MATIC] has witnessed a surge in its network activity, with the same higher than that of Solana [SOL]. However, Solana still dominated Polygon by a significant margin in terms of transactions. Not only that, but SOL’s performance in the DeFi space also remains promising, as its TVL hit this year’s highest level recently. In fact, SOL also outshined MATIC by its price action, as the former overtook the latter in market capitalization.\n\nRead Polygon’s [MATIC] Price Prediction 2023-24\n\nIs Polygon actually ahead of Solana?\n\nArtemis’ data pointed out that Solana’s daily active addresses declined over the last three months. This allowed Polygon to overtake Solana in terms of that metric. Moreover, MATIC has consistently managed to have more users on the network to date. However, looking at the whole picture revealed that MATIC is still way behind SOL in the number of daily transactions processed by the networks.\n\nNot only that, Solana is also closing in on Polygon in terms of their captured value. As is evident from Artemis’ chart, Solana’s fees and revenue have gained since mid-July, even flipping Polygon on two occasions.\n\nHowever, at press time, Polygon’s fees and revenue remained slightly higher than those of SOL. Nonetheless, SOL recently touched a new milestone. DeFiLlama pointed out that SOL’s TVL was moving up and, at press time, was sitting at the highest level since the beginning of the year.\n\nA wider look at both blockchains\n\nApart from network activity, both blockchains’ staking ecosystems are also interesting to observe. SOL is way ahead of MATIC in terms of staking market cap, as the former’s figures stood at $9.77 billion, while the latter had a staking market cap of $2.4 billion at press time. While MATIC had a staking ratio of 38.91%, SOL posted a reading of 70.84%. Moreover, the number of staking wallets was also higher on Solana than on Polygon.\n\nNonetheless, Santiment’s chart revealed both blockchains have been performing neck to neck in their respective NFT ecosystems. Both MATIC and SOL had similar numbers of NFT trade counts and trade volume in USD, which proves the competition.\n\nBullish price action for Solana?\n\nTowards the beginning of May, MATIC’s market cap was well above that of SOL. However, things started to turn sour for MATIC as it bled considerably over the next few weeks. This allowed Solana to take over, as the token flipped MATIC on 6 July. Since then, the latter has been able to reclaim its lead in terms of market cap.\n\nIs your portfolio green? Check the Solana Profit Calculator\n\nIn fact, as per CoinMarketCap, SOL’s price appreciated by over 7% over the past week. On the contrary, MATIC only managed to hike by 1%. At press time, SOL was trading at $24.77 while MATIC’s value was $0.6794 on the charts."}, {"id": 12, "url": "https://news.google.com/rss/articles/CBMiLmh0dHBzOi8vd3d3LnNlY3VyaXRpZXMuaW8vc29sYW5hLXZzLWNoYWlubGluay_SAQA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 14 Aug 2023 07:00:00 GMT", "title": "Solana Vs. Chainlink - What's the Difference? - Securities.io", "content": "When learning the key differences between Solana and Chainlink, it's vital to understand that the two networks serve very different roles in the market. These projects may share certain traits, such as popularity, but from there, the many differences become obvious. Here's some valuable insight into Solana vs. Chainlink.\n\nWhat is Solana?\n\nSolana is a third-generation open-source public blockchain. This layer 1 protocol supports high-speed transactions and smart contract programmability. The network features an open and adaptable infrastructure that supports the creation of blockchain assets and networks.\n\nNotably, the project was founded by <PERSON><PERSON><PERSON> and <PERSON> in 2018. Solana went live in 2020, with the first block logged on March 16 of the same year. Interestingly, the name Solana refers to a quiet beach town that the developers lived in for many years before launching the project. Today, Solana is one of the most popular blockchains in the market.\n\nWhat is Chainlink?\n\nChainlink operates as a decentralized Oracle network and infrastructure provider. The platform is open source and public – similar to Solana. However, Chainlink was built to serve a much different role than Solana in that its main goal is to support the integration and creation of a decentralized oracle network.\n\nOracles are sensors that enable blockchains to connect to nearly any actions on or off-chain. There are oracles that do things like monitor payments to release documents at the time of completion, monitor weather, track stocks, and everything else you can imagine. As such, oracles are a crucial part of today's crypto market.\n\nChainlink entered the market as a smart contract consulting firm in 2014 before changing focus towards oracles in 2017. <PERSON> and <PERSON> are the founders of this unique network. Today, Chainlink is a vital component of many other platforms' operations. Its reliable and secure oracles have earned a reputation as one of the best options for those seeking oracles.\n\nWhat Problems Was Solana Built to Alleviate?\n\nSolana was designed to eliminate or reduce very specific issues that existed at the time of its entrance into the market. Solana was created when Ethereum, the world's top DeFi and token launch network, was amid massive slowdowns and high fees due to congestion.\n\nSolana's developers wanted to provide a more programmable and easier-to-use blockchain experience. They desired to make the network more flexible and easier to work with for Dapp developers. This strategy has proved successful as the platform continues to garner new Dapps.\n\nAnother major pain point that Solana targets are network fees. It was amid record-high fees when Solana launched. The network charges a fraction of the cost of using Ethereum. Additionally, it's much faster than the original Ethereum network. Solana can handle 29,171 tps.\n\nWhat Problems Was Chainlink Built to Alleviate?\n\nChainlink was developed to tackle a very specific issue that had plagued the crypto market for years. The platform's developers wanted to make it easy and cost-effective to integrate distributed oracles into Dapps. This concept was revolutionary at the time, as most networks relied on centralized oracles.\n\nCorrupt Oracles\n\nThe problem with that approach was that data could be corrupted and entered into the blockchain incorrectly. Since most public blockchains are immutable and uneditable, this bad information could cause a slew of issues down the line. To combat these concerns, Chainlink introduced a self-monitoring and healing oracle network.\n\nThere are a lot of efficiencies gained from using Chainlink as opposed to creating custom oracles. For one, the research and testing of new oracles can be a time-consuming and expensive task. Additionally, to provide the same level of service, you would need to create a decentralized self-checking and healing network which is far outside the budget of most projects.\n\nRoadblocks to Integration and Adoption\n\nChainlink makes it easy for developers to integrate a decentralized oracle system into their dapps without the need to master new coding. The process continues to be streamlined to help improve results. Today, there are a variety of networks leveraging Chainlink oracles to send and receive data.\n\nHow Does Solana Work?\n\nSolana leverages two consensus mechanisms to improve performance. The primary Proof-of-Stake (PoS) consensus system enables users to stake their tokens to secure passive returns. The network has a 2.34-second block time currently.\n\nSolana is one of only a few blockchain networks that leverage dual consensus systems to improve performance. The Proof of History (POH) protocol leverages time stamps to create a cryptographic clock that speeds up the reference process.\n\nSOL\n\nSol is the utility token for Solana. It can be staked and used to pay for transaction fees. Developers also use SOL to cover gas fees. SOL is a popular token that can be found on most top-performing CEXs (centralized Exchanges) at this time.\n\nHow Does Chainlink Work?\n\nChainlink operates as a decentralized oracle network. The system uses a self-healing protocol that monitors the input of oracles from multiple sources and then cross-references the results against others. Oracles that provide slow or incorrect results get penalized or removed automatically.\n\nChainlink leverages a dual token approach which enables the community to distinguish which network nodes are the most reliable. This structure has proven to be effective as today Chainlink is the premier oracle provider to the industry,\n\nLINK\n\nLINK is the main utility token for Chainlink. The token is issued to pay for services and other network tasks. The platform also leverages a secondary token called REP which is used to reward the most consistent and effective node operators.\n\nHow to Buy Solana (SOL) and Chainlink (LINK)\n\nCurrently, Solana (SOL) and Chainlink (LINK) are each available for purchase on the following exchanges.\n\nUphold – This is one of the top exchanges for United States & UK residents that offers a wide range of cryptocurrencies. Germany & Netherlands are prohibited.\n\nUphold Disclaimer: Terms Apply. Cryptoassets are highly volatile. Your capital is at risk. Don’t invest unless you’re prepared to lose all the money you invest. This is a high-risk investment, and you should not expect to be protected if something goes wrong..\n\nKraken – Founded in 2011, Kraken is one of the most trusted names in the industry with over 9,000,000 users, and over $207 billion in quarterly trading volume.\n\nThe Kraken exchange offers trading access to over 190 countries including Australia, Canada, Europe, and is a top exchange for USA residents. (Excluding New York & Washington state).\n\nSolana Vs. Chainlink – Projects that Could Work Together\n\nAs you delve deeper into the Solana Vs. Chainlink discussion, it's easy to see that both the platforms could work together across several Dapps. Solana's ultra-fast performance and Chainlink's reliability make a good combo for dApp development. For these reasons and many more, both projects are a solid addition to your portfolio.\n\nYou can learn more about Solana and Chainlink here."}, {"id": 3, "url": "https://news.google.com/rss/articles/CBMiUWh0dHBzOi8vd3d3LnNlY3VyaXRpZXMuaW8vc29sYW5hLXNvbC12cy1wb2xrYWRvdC1kb3QtZXZlcnl0aGluZy15b3UtbmVlZC10by1rbm93L9IBAA?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 14 Aug 2023 07:00:00 GMT", "title": "Solana Vs. <PERSON> (2024 Edition) - Securities.io", "content": "Learning the differences between Solana (SOL) vs Polkadot (DOT) is a great way to improve your market knowledge. Both of these projects provide significant advantages over earlier generation blockchains. However, they do serve different roles with some overlap. Here is everything you need to know about Solana (SOL) vs Polkadot (DOT).\n\nWhat is Solana?\n\nSolana was built to provide top-notch scalability and developer flexibility. The protocol integrates an open infrastructure and low gas fees to empower developers to build more immersive and useful Dapps. The network is much faster and cheaper than Ethereum with the average cost per transaction sitting at around $0.00025.\n\nSolana is the brainchild of <PERSON><PERSON><PERSON>. The network entered the market in 2017 during the crypto breakout year. The network received immediate attention due to its high transaction throughput and experienced developers including <PERSON>, and <PERSON>. These two held prior positions at Dropbox and Qualcomm.\n\nIn January, Solana suffered a string of outages with some lasting over 8-hours. These outages were the result of bots rushing to swoop up arbitrage opportunities to earn returns on leveraged positions. These outages dropped the price of the network's token briefly and caused some traders to question the project's sustainability. Despite the losses, Solana is still a popular option for developers and traders alike.\n\nWhat is Polkadot?\n\nPolkadot is another smart contract programmable blockchain ecosystem. The protocol differs from Solana in that it integrates a multi-chain application environment. To provide interoperability with other blockchain networks. The goal of Polkadot is to drive blockchain adoption and interoperability by enabling developers to send data fluently between networks in a secure manner.\n\nPolkadot (DOT) first emerged as a concept in 2016. That's when Ethereum co-developer Gavin Wood released the network's whitepaper. Wood is one of the most influential people in the crypto sector and is credited for his development of the Solidity smart contract programming language used by Ethereum.\n\nPolkadot raised eyebrows across the market when it secured $145 million in funding during its record-breaking ICO. However, the success was short-lived as the Parity wallet in which the ICO funds were stored was hacked and $150 million in crypto stolen. While these setbacks did slow the project at the start, today, Polkadot is a top-performing network.\n\nWhat Problems does Solana Solve?\n\nSolana was designed to provide developers with more scalability and flexibility than Ethereum. The network eliminates scaling concerns due to its technical structure. The protocol can scale proportionally to match network bandwidth. Bench test put Solana at 29,171 tps (transactions per second).\n\nFragmentation\n\nAnother major concern for Solana developers was network fragmentation. Most inter-chain protocols rely on second layer protocols which create more centralization in the network. Solana maintains a single global state which provides a more secure and efficient alternative.\n\nData Delays\n\nEarlier blockchains looked towards sharding as a way to provide more storage space. However, the technical layout of sharded networks creates delays in data retrieval. Solana integrates proprietary systems to provide a more streamlined alternative that can meet the needs of a global market.\n\nWhat Problems does Polkadot Solve?\n\nPolkadot was built to enable private and public blockchains to communicate freely. The developers behind the project see this integration as a key step in driving blockchain adoption. Additionally, creators gain a lot of benefits using Polkadot including the ability to integrate the best characteristics from varying blockchain networks into their Dapps.\n\nPolkadot streamlines programming tasks associated with Dapp development. It provides scalability on par with top-performing blockchains. Additionally, the multi-chain structure of the network simplifies the creation of sub-networks, tokens, and more.\n\nHow Does Solana Work?\n\nSolana combines a variety of protocols to accomplish its goal to become the fastest blockchain in the market. The network leverages a protocol called turbine to separate and transmit data across the network's nodes. This data is then sent to a feature called Gulf Stream.\n\nGolf Stream replaces the need for a mempool like Bitcoin uses which speeds up the transactions process. The Gulf Stream protocol can handle 50,000 tps. One of the most unique aspects of this approach is that the system forwards the packets to nodes before the last batch completes confirmation.\n\nCloudbreak\n\nThe Cloudbreak protocol operates as the networks accounts database. The system improves on earlier blockchain in multiple ways. For example, Cloudbreak can read and write data simultaneously which improves transactions speeds and enables Dapps to operate more responsively.\n\nSolana Coin\n\nSOL is the native utility token for the Solana network. This token can be used as a cryptocurrency to make payments in a permissionless manner. It’s also how developers and users pay network fees and execute smart contracts. There is a total supply of 510 million SOL slated for issuance over the life of the project. You may stake SOL to secure passive rewards with minimal risk exposure.\n\nHow Does Polkadot Work?\n\nPolkadot operates as a multi-blockchain ecosystem. The network can be broken into separate layers with the Polkadot relay chain operating as the core communication layer. This layer handles consensus for the network. Polkadot leverages a new style of consensus called GRANDPA (GHOST-based Recursive Ancestor Deriving Prefix Agreement). GRANDPA enables the network to pool security. Additionally, it’s incredibly scalable with near-instant confirmation times.\n\nThe network integrates sovereign blockchains developers can create called Parachains. These sub-networks can leverage Polkadot for security or integrate unique technical structures. A Polkadot parachain can have its own governance, consensus, and tokens.\n\nUniquely, Polkadot offers a more affordable option for developers that only require very limited blockchain exposure to operate their Dapps. The Parathreads feature enables developers to create networks using a pay-as-you-go model. For Dapps that don’t require constant blockchain connectivity, this option makes way more sense and saves money.\n\nDOT Token\n\nDOT is the native utility token for Polkadot. This token enables users to participate in the protocols community governance system. They can also stake DOT to earn rewards. You need to hold DOT to launch parachains or parathreads.\n\nHow to Buy Solana (SOL) and Polkadot (DOT)\n\nCurrently, Solana (SOL) and Polkadot (DOT) can each be purchased on the following exchanges.\n\nUphold – This is one of the top exchanges for United States & UK residents that offers a wide range of cryptocurrencies. Germany & Netherlands are prohibited.\n\nUphold Disclaimer: Terms Apply. Cryptoassets are highly volatile. Your capital is at risk. Don’t invest unless you’re prepared to lose all the money you invest. This is a high-risk investment, and you should not expect to be protected if something goes wrong..\n\nKraken – Founded in 2011, Kraken is one of the most trusted names in the industry with over 9,000,000 users, and over $207 billion in quarterly trading volume.\n\nThe Kraken exchange offers trading access to over 190 countries including Australia, Canada, Europe, and is a top exchange for USA residents. (Excluding New York & Washington state).\n\nSolana (SOL) vs Polkadot (DOT) – Two Projects Seeking to Take the Crown\n\nSolana and Polkadot both provide developers with a more flexible and open infrastructure to build within. These networks possess high scalability and can improve interoperability within the market. For these reasons, both projects could have a bright future.\n\nTo learn more visit our Investing in Solana or our Investing in Polkadot guide."}, {"id": 6, "url": "https://news.google.com/rss/articles/CBMihgFodHRwczovL3d3dy5kYWlseW5ld3MuY29tLzIwMjMvMDgvMTQvZm9vLWZpZ2h0ZXJzLWd1aXRhcmlzdC1jaHJpcy1zaGlmbGV0dC1zaGFrZXMtdGhpbmdzLXVwLXdpdGgtbG9zdC1hdC1zZWEtc29sby1hbGJ1bS1hbmQtbWluaS10b3VyL9IBigFodHRwczovL3d3dy5kYWlseW5ld3MuY29tLzIwMjMvMDgvMTQvZm9vLWZpZ2h0ZXJzLWd1aXRhcmlzdC1jaHJpcy1zaGlmbGV0dC1zaGFrZXMtdGhpbmdzLXVwLXdpdGgtbG9zdC1hdC1zZWEtc29sby1hbGJ1bS1hbmQtbWluaS10b3VyL2FtcC8?oc=5&hl=en-SG&gl=SG&ceid=SG:en", "time": "Mon, 14 Aug 2023 07:00:00 GMT", "title": "Foo Fighters’ guitarist <PERSON> shakes things up with ‘Lost at Sea’ solo album and mini tour - LA Daily News", "content": "Though <PERSON> gets to rock out on guitar in front of thousands of fans at mega music festivals and sold-out stadiums around the world with the Foo Fighters, he’s equally as excited to bust out a well-worn acoustic guitar and play alt-country and Americana songs inside more intimate clubs.\n\nHe’s fortunate, he said, to have a foot in both worlds and though he’s earned a certain level of notoriety, at his core he’s just a huge music fan.\n\nAside from being in arguably one of the biggest rock bands on the planet, <PERSON><PERSON><PERSON>, who came up in the punk rock world in bands like No Use for a Name and Me First and the Gimme Gimmes, has enjoyed a fulfilling solo career. He’s got three albums under his belt, including the forthcoming “Lost at Sea,” which will be out on Oct. 20 via Blue Élan Records. While on a brief break in the Foo Fighters’ busy touring schedule, <PERSON><PERSON><PERSON> will bring his new music to the Belly Up in Solana Beach on Thursday, Aug. 24 and The Venice West in Venice on Friday, Aug. 25.\n\n<PERSON> will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by <PERSON>)\n\n<PERSON> will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by <PERSON>)\n\n<PERSON> will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo courtesy of Blue Élan Records)\n\nChris Shiflett will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by Joey Martinez)\n\nChris Shiflett will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by Joey Martinez)\n\nChris Shiflett will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by Joey Martinez)\n\nChris Shiflett will play the Belly Up in Solana Beach on Aug. 24 and The Venice West in Venice on Aug. 25 in support of his third solo record, the forthcoming “Lost at Sea,” which is due out on Oct. 20 on Blue Élan Records. (Photo by Joey Martinez)\n\n“We’ve been playing about four songs off the record live so far, but we should probably get down a couple more,” Shiflett said during a phone interview from his Los Angeles home. “It has been really fun. It’s interesting to play a song nobody’s heard before. If you can get a crowd to react to a song no one has ever heard, it’s like, all right, this could be good.”\n\nSo far, he’s peppered in the rippin’ guitar-forward rock track “Black Top White Lines,” which Shiflett wrote with album producer and The Cadillac Three vocalist-guitarist Jaren Johnston and Brothers Osborne songwriter-guitarist John Osborne; the country twang-filled “Dead and Gone” featuring the talents of Charlie Worsham (Dierks Bentley, Old Crow Medicine Show) on dobro and Tom Bukovac (Bob Seger, Willie Nelson) on guitar; as well as and the Tom Petty and The Heartbreakers-influenced, “Overboard.”\n\nWhile his first two albums, 2017’s “West Coast Town” and 2019’s “Hard Lessons,” lean more heavily on an alt-country sound, “Lost at Sea” is more all over the map sonically, with tunes that could please audiences in both punk rock bars or a boot-scootin’ honky tonks. At the time of our interview, “Damage Control,” which conjures a reggae-island music à la Jimmy Buffett vibe, was Shiflett’s favorite new song.\n\n“I pretty much wrote all the songs during lockdown in 2020-2021, except for ‘Damage Control,’” he said. He actually wrote that song 15 years ago and while on tour with a night off in New York City several years back, he and a friend recorded a version of it in a basement studio. He unearthed the old song while digging through files to create this album and sent the raw recording to Johnston, who insisted it should live on “Lost at Sea.”\n\n“It’s pretty different from the demo, which I will never play anyone because it’s really, really (bad),” he says with a laugh. “But now it’s one of my favorite songs on the whole record. I love what Charlie Worsham played on that, he’s singing and playing acoustic guitar and there’s a banjo track on there that kinda became the hook of the song and I love it.”\n\nOn top of his numerous musical projects, Shiflett also has a popular podcast, “Walking the Floor,” on which he interviews famous musicians, writers and athletes about their creative inspirations. He also recently launched a new video podcast series, “Shred with Shifty,” during which he chats with accomplished guitarists like Brad Paisley, Chic’s Nile Rodgers, Lindsay Ell and his debut episode featured Rush’s Alex Lifeson.\n\n“I interviewed Mike Campbell for it the other day, which was unbelievable because I had him breaking down a bunch of his guitar parts and it’s one of those things where you’re pinching yourself while it’s happening because you’re going ‘I can’t believe I’m talking to Mike Campbell,’” he said of the Tom Petty and the Heartbreakers’ guitarist, while noting that these podcasts are a bit self-serving because he really wanted to meet and talk to a lot of these people.\n\n“When do you get a chance to sit and talk with these people you love about their craft,” he said. “Honestly, in my line of work, we get to meet people and it blows your mind, but it’s usually at a festival or backstage at a show and rarely do you get a quiet moment with somebody. If Jimmy Page comes to your show, everyone in the building wants to bum-rush him, as you’d expect. It’s not like you get to sit there and pick their brain about guitar tone and so that’s a big reason why I do these shows.”\n\nWith the use of modern technology, Shiflett is able to bring fans into the homes and studios of a lot of these players, who are step-by-step breaking down some iconic guitar parts. It’s something that growing up in Santa Barbara and learning to play guitar, he wished he’d had access to himself.\n\n“I didn’t sit around trying to figure out how to play the guitar parts on records that I loved, but occasionally I’d take a song in to my guitar teacher and have them figure out the bits,” he said. “I was never one of those people that learned all the stuff like the record and I didn’t get into that until much later, when technology evolved to where you could do that a little easier. Now we have apps and you can slow down a song and dilute the part you’re trying to figure out and hone in on it. If I had that as a kid, I’d be a much better guitar player now for it.”\n\nHe said he’s also been envious of the guitar collections of some of his subjects.\n\n“There’s no end to coveting other people’s vintage guitar collections, that never ends,” he said. “I’ve got some nice stuff, but by the time I could afford to buy a nice vintage guitar, the prices are now insane so I just can’t do it. I sold off a bunch of guitars a few years ago and bought one nice old ’57 Les Paul. I should do that again. You end up buying a bunch of guitars and there’s only a few that ever become really special to you. The older I get, the more it appeals to me to get rid of my clutter.”\n\nShiflett says he usually doesn’t regret offloading instruments, but he often thinks about a pair of guitars that got away from him back when he played his first gig in ninth grade during a talent show at Santa Barbara High School.\n\n“I didn’t have a good guitar at that point, so I borrowed my older brother Mike’s and he had an old vintage SG and a Strat and I took them both to the show and for some reason I left them at the high school and they got stolen,” he said. “Never to be heard from again. It haunts me to this day. Whoever has got them, give them back! I’ll buy them back at an inflated price. I’m serious.”\n\nThe now 52-year-old husband and father of three will have a bit of downtime at home this month, but the rest of 2023 is filled with a few solo shows and national and international festival tour dates with Foo Fighters in support of the band’s latest album, “But Here We Are,” its first record without drummer and Laguna Beach native Taylor Hawkins, who died while on tour in 2022.\n\nAfter a lot of speculation, Foo Fighters revealed its new drummer to be Orange County native and current Long Beach resident Josh Freese, who has famously anchored the punk rock band The Vandals and played in the studio and toured with acts like Sting, Devo, A Perfect Circle and Nine Inch Nails. Foo Fighters will headline the final evening of the three-day Eddie Vedder-curated Ohana Festival in Dana Point on Sunday, Oct. 1.\n\n“It’s great for me to have Josh in the band because I’ve known him for a very long time,” Shiflett said. “He played a couple of shows with a band I was in back in the early ’90s and we’ve crossed paths a million times ever since over the years. I’ve been a fan, an admirer and friend for a long time, but I’d never gotten to play with him in a band day after day and show after show … I love it and he’s fantastic.”\n\nChris Shiflett\n\nWhen: 8 p.m. Friday, Aug. 25\n\nWhere: The Venice West, 1717 Lincoln Blvd., Venice\n\nTickets: $20; 21-and-older only. Ticketweb.com.\n\nAlso: 8 p.m. Thursday, Aug. 24, Belly Up, 143 S. Cedros Ave., Solana Beach. $20; 21-and-older only. Ticketweb.com."}]