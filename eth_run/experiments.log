
*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_fee': np.float64(0.0006577349210978), 'unique_addresses': np.int64(1035628), 'total_size_used': np.int64(282844364454), 'total_transactions': np.int64(5340461), 'coinbase_transactions': np.int64(426), 'total_value_transferred': np.int64(597957914)}, 'date': '2023-10-01T23:59:59.999Z'}
*** <PERSON>ND STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': 'N/A', 'successful_transactions': 'N/A', 'total_value_transferred': 'N/A', 'total_gas_used': 'N/A', 'unique_addresses': 'N/A', 'total_transactions': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'average_gas_price': 'N/A', 'unique_addresses': 'N/A', 'successful_transactions': 'N/A', 'total_value_transferred': 'N/A', 'total_transactions': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'total_value_transferred': 'N/A', 'successful_transactions': 'N/A', 'average_gas_price': 'N/A', 'total_transactions': 'N/A', 'unique_addresses': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(1116164), 'unique_addresses': np.int64(617596), 'total_value_transferred': np.float64(2.17324e+24), 'total_transactions': np.int64(1137621), 'average_gas_price': np.float64(29203802451.0), 'total_gas_used': np.float64(107737000000.0)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(1063574), 'successful_transactions': np.int64(1039534), 'total_value_transferred': np.float64(1.65818e+24), 'total_gas_used': np.float64(107985000000.0), 'average_gas_price': np.float64(25898811752.0), 'unique_addresses': np.int64(522702)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(1.65818e+24), 'average_gas_price': np.float64(25898811752.0), 'total_transactions': np.int64(1063574), 'unique_addresses': np.int64(522702), 'successful_transactions': np.int64(1039534), 'total_gas_used': np.float64(107985000000.0)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(1039534), 'unique_addresses': np.int64(522702), 'total_value_transferred': np.float64(1.65818e+24), 'average_gas_price': np.float64(25898811752.0), 'total_transactions': np.int64(1063574), 'total_gas_used': np.float64(107985000000.0)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'total_value_transferred': 'N/A', 'successful_transactions': 'N/A', 'unique_addresses': 'N/A', 'total_transactions': 'N/A', 'average_gas_price': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'total_transactions': 'N/A', 'unique_addresses': 'N/A', 'total_value_transferred': 'N/A', 'successful_transactions': 'N/A', 'average_gas_price': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'average_gas_price': 'N/A', 'total_value_transferred': 'N/A', 'total_transactions': 'N/A', 'successful_transactions': 'N/A', 'unique_addresses': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': 'N/A', 'successful_transactions': 'N/A', 'average_gas_price': 'N/A', 'total_value_transferred': 'N/A', 'unique_addresses': 'N/A', 'total_transactions': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': 'N/A', 'total_gas_used': 'N/A', 'average_gas_price': 'N/A', 'unique_addresses': 'N/A', 'total_value_transferred': 'N/A', 'total_transactions': 'N/A'}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(1063574), 'total_value_transferred': np.float64(1.65818e+24), 'successful_transactions': np.int64(1039534), 'unique_addresses': np.int64(522702), 'total_gas_used': np.float64(107985000000.0), 'average_gas_price': np.float64(25898811752.0)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(25898811752.0), 'successful_transactions': np.int64(1039534), 'unique_addresses': np.int64(522702), 'total_transactions': np.int64(1063574), 'total_gas_used': np.float64(107985000000.0), 'total_value_transferred': np.float64(1.65818e+24)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_size_used': np.int64(213952984554), 'unique_addresses': np.int64(1325936), 'total_value_transferred': np.float64(96543710.67222372), 'total_transactions': np.int64(7291493), 'coinbase_transactions': np.int64(423), 'average_fee': np.float64(0.0012732761796149)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(14140325), 'total_transactions': np.int64(23340943)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(1.65818e+24), 'total_transactions': np.int64(1063574), 'unique_addresses': np.int64(522702), 'successful_transactions': np.int64(1039534), 'total_gas_used': np.float64(107985000000.0), 'average_gas_price': np.float64(25898811752.0)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': np.float64(107985000000.0), 'total_transactions': np.int64(1063574), 'successful_transactions': np.int64(1039534), 'unique_addresses': np.int64(522702), 'average_gas_price': np.float64(25898811752.0), 'total_value_transferred': np.float64(1.65818e+24)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(25898811752.0), 'total_value_transferred': np.float64(1.65818e+24), 'total_transactions': np.int64(1063574), 'successful_transactions': np.int64(1039534), 'total_gas_used': np.float64(107985000000.0), 'unique_addresses': np.int64(522702)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(1.65818e+24), 'total_gas_used': np.float64(107985000000.0), 'total_transactions': np.int64(1063574), 'average_gas_price': np.float64(25898811752.0), 'unique_addresses': np.int64(522702), 'successful_transactions': np.int64(1039534)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(832839), 'average_gas_price': np.float64(30469108368.0), 'total_transactions': np.int64(854249), 'unique_addresses': np.int64(487167), 'total_gas_used': np.float64(107230000000.0), 'total_value_transferred': np.float64(9.60145e+23)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'unique_addresses': np.int64(487167), 'total_transactions': np.int64(854249), 'total_value_transferred': np.float64(9.60145e+23), 'average_gas_price': np.float64(30469108368.0), 'successful_transactions': np.int64(832839), 'total_gas_used': np.float64(107230000000.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(854249), 'successful_transactions': np.int64(832839), 'total_value_transferred': np.float64(9.60145e+23), 'unique_addresses': np.int64(487167), 'total_gas_used': np.float64(107230000000.0), 'average_gas_price': np.float64(30469108368.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': np.float64(107230000000.0), 'unique_addresses': np.int64(487167), 'average_gas_price': np.float64(30469108368.0), 'successful_transactions': np.int64(832839), 'total_value_transferred': np.float64(9.60145e+23), 'total_transactions': np.int64(854249)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_size_used': np.int64(213952984554), 'total_transactions': np.int64(7291493), 'unique_addresses': np.int64(1325936), 'average_fee': np.float64(0.0012732761796149), 'total_value_transferred': np.float64(96543710.67222372), 'coinbase_transactions': np.int64(423)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_size_used': np.int64(213952984554), 'unique_addresses': np.int64(1325936), 'total_transactions': np.int64(7291493), 'total_value_transferred': np.float64(96543710.67222372), 'coinbase_transactions': np.int64(423), 'average_fee': np.float64(0.0012732761796149)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_fee': np.float64(0.0012732761796149), 'total_transactions': np.int64(7291493), 'total_size_used': np.int64(213952984554), 'unique_addresses': np.int64(1325936), 'coinbase_transactions': np.int64(423), 'total_value_transferred': np.float64(96543710.67222372)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_size_used': np.int64(213952984554), 'coinbase_transactions': np.int64(423), 'average_fee': np.float64(0.0012732761796149), 'total_value_transferred': np.float64(96543710.67222372), 'unique_addresses': np.int64(1325936), 'total_transactions': np.int64(7291493)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'total_size_used': np.int64(175095759616), 'coinbase_transactions': np.int64(405), 'total_value_transferred': np.float64(138008799.0431452), 'total_transactions': np.int64(6873309), 'average_fee': np.float64(0.0018949105768953), 'unique_addresses': np.int64(1217618)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'coinbase_transactions': np.int64(405), 'average_fee': np.float64(0.0018949105768953), 'total_size_used': np.int64(175095759616), 'total_transactions': np.int64(6873309), 'total_value_transferred': np.float64(138008799.0431452), 'unique_addresses': np.int64(1217618)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'total_size_used': np.int64(175095759616), 'unique_addresses': np.int64(1217618), 'total_value_transferred': np.float64(138008799.0431452), 'total_transactions': np.int64(6873309), 'average_fee': np.float64(0.0018949105768953), 'coinbase_transactions': np.int64(405)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'total_value_transferred': np.float64(138008799.0431452), 'total_size_used': np.int64(175095759616), 'unique_addresses': np.int64(1217618), 'total_transactions': np.int64(6873309), 'average_fee': np.float64(0.0018949105768953), 'coinbase_transactions': np.int64(405)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(14140325), 'total_transactions': np.int64(23340943)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(14140325), 'total_transactions': np.int64(23340943)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(37668999), 'successful_transactions': np.int64(18919745)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(18919745), 'total_transactions': np.int64(37668999)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(37668999), 'successful_transactions': np.int64(18919745)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(37668999), 'successful_transactions': np.int64(18919745)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(1039534), 'total_gas_used': np.float64(107985000000.0), 'total_value_transferred': np.float64(1.65818e+24), 'unique_addresses': np.int64(522702), 'average_gas_price': np.float64(25898811752.0), 'total_transactions': np.int64(1063574)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'unique_addresses': np.int64(522702), 'average_gas_price': np.float64(25898811752.0), 'total_gas_used': np.float64(107985000000.0), 'total_transactions': np.int64(1063574), 'total_value_transferred': np.float64(1.65818e+24), 'successful_transactions': np.int64(1039534)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': np.float64(107985000000.0), 'unique_addresses': np.int64(522702), 'total_value_transferred': np.float64(1.65818e+24), 'total_transactions': np.int64(1063574), 'average_gas_price': np.float64(25898811752.0), 'successful_transactions': np.int64(1039534)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(25898811752.0), 'total_gas_used': np.float64(107985000000.0), 'successful_transactions': np.int64(1039534), 'total_transactions': np.int64(1063574), 'total_value_transferred': np.float64(1.65818e+24), 'unique_addresses': np.int64(522702)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(9.60145e+23), 'total_transactions': np.int64(854249), 'successful_transactions': np.int64(832839), 'unique_addresses': np.int64(487167), 'average_gas_price': np.float64(30469108368.0), 'total_gas_used': np.float64(107230000000.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(854249), 'average_gas_price': np.float64(30469108368.0), 'total_value_transferred': np.float64(9.60145e+23), 'successful_transactions': np.int64(832839), 'unique_addresses': np.int64(487167), 'total_gas_used': np.float64(107230000000.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(9.60145e+23), 'unique_addresses': np.int64(487167), 'total_transactions': np.int64(854249), 'average_gas_price': np.float64(30469108368.0), 'successful_transactions': np.int64(832839), 'total_gas_used': np.float64(107230000000.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(832839), 'total_transactions': np.int64(854249), 'average_gas_price': np.float64(30469108368.0), 'unique_addresses': np.int64(487167), 'total_gas_used': np.float64(107230000000.0), 'total_value_transferred': np.float64(9.60145e+23)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(96543710.67222372), 'unique_addresses': np.int64(1325936), 'coinbase_transactions': np.int64(423), 'average_fee': np.float64(0.0012732761796149), 'total_transactions': np.int64(7291493), 'total_size_used': np.int64(213952984554)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'unique_addresses': np.int64(1325936), 'total_value_transferred': np.float64(96543710.67222372), 'total_size_used': np.int64(213952984554), 'total_transactions': np.int64(7291493), 'average_fee': np.float64(0.0012732761796149), 'coinbase_transactions': np.int64(423)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_fee': np.float64(0.0012732761796149), 'coinbase_transactions': np.int64(423), 'total_size_used': np.int64(213952984554), 'unique_addresses': np.int64(1325936), 'total_value_transferred': np.float64(96543710.67222372), 'total_transactions': np.int64(7291493)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(18.540906709276292), 'open': np.float64(26967.3974331494), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_fee': np.float64(0.0012732761796149), 'coinbase_transactions': np.int64(423), 'total_transactions': np.int64(7291493), 'total_value_transferred': np.float64(96543710.67222372), 'unique_addresses': np.int64(1325936), 'total_size_used': np.int64(213952984554)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'coinbase_transactions': np.int64(405), 'total_transactions': np.int64(6873309), 'unique_addresses': np.int64(1217618), 'total_size_used': np.int64(175095759616), 'total_value_transferred': np.float64(138008799.0431452), 'average_fee': np.float64(0.0018949105768953)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'total_value_transferred': np.float64(138008799.0431452), 'unique_addresses': np.int64(1217618), 'coinbase_transactions': np.int64(405), 'total_size_used': np.int64(175095759616), 'total_transactions': np.int64(6873309), 'average_fee': np.float64(0.0018949105768953)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'unique_addresses': np.int64(1217618), 'total_value_transferred': np.float64(138008799.0431452), 'total_size_used': np.int64(175095759616), 'total_transactions': np.int64(6873309), 'average_fee': np.float64(0.0018949105768953), 'coinbase_transactions': np.int64(405)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(15.885764141437308), 'open': np.float64(31474.7213636247), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'buy'}, 'txnstat': {'average_fee': np.float64(0.0018949105768953), 'coinbase_transactions': np.int64(405), 'unique_addresses': np.int64(1217618), 'total_transactions': np.int64(6873309), 'total_value_transferred': np.float64(138008799.0431452), 'total_size_used': np.int64(175095759616)}, 'date': '2023-07-14T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(14140325), 'total_transactions': np.int64(23340943)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(23373.158919695892), 'open': np.float64(21.3920592299), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(23340943), 'successful_transactions': np.int64(14140325)}, 'date': '2023-10-01T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(37668999), 'successful_transactions': np.int64(18919745)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(37668999), 'successful_transactions': np.int64(18919745)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(18919745), 'total_transactions': np.int64(37668999)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(19096.555410046454), 'open': np.float64(26.1827323967), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'successful_transactions': np.int64(18919745), 'total_transactions': np.int64(37668999)}, 'date': '2023-02-21T00:00:00.000Z'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(1.65818e+24), 'average_gas_price': np.float64(25898811752.0), 'total_gas_used': np.float64(107985000000.0), 'successful_transactions': np.int64(1039534), 'unique_addresses': np.int64(522702), 'total_transactions': np.int64(1063574)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_gas_used': np.float64(107985000000.0), 'successful_transactions': np.int64(1039534), 'total_value_transferred': np.float64(1.65818e+24), 'average_gas_price': np.float64(25898811752.0), 'unique_addresses': np.int64(522702), 'total_transactions': np.int64(1063574)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_transactions': np.int64(854249), 'total_value_transferred': np.float64(9.60145e+23), 'unique_addresses': np.int64(487167), 'total_gas_used': np.float64(107230000000.0), 'successful_transactions': np.int64(832839), 'average_gas_price': np.float64(30469108368.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'total_value_transferred': np.float64(9.60145e+23), 'unique_addresses': np.int64(487167), 'successful_transactions': np.int64(832839), 'average_gas_price': np.float64(30469108368.0), 'total_transactions': np.int64(854249), 'total_gas_used': np.float64(107230000000.0)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'unique_addresses': np.int64(487167), 'total_value_transferred': np.float64(9.60145e+23), 'total_gas_used': np.float64(107230000000.0), 'average_gas_price': np.float64(30469108368.0), 'total_transactions': np.int64(854249), 'successful_transactions': np.int64(832839)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(236.00504380350608), 'open': np.float64(2118.598789), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(30469108368.0), 'unique_addresses': np.int64(487167), 'successful_transactions': np.int64(832839), 'total_gas_used': np.float64(107230000000.0), 'total_value_transferred': np.float64(9.60145e+23), 'total_transactions': np.int64(854249)}, 'date': '2023-04-17 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(25898811752.0), 'total_transactions': np.int64(1063574), 'successful_transactions': np.int64(1039534), 'total_gas_used': np.float64(107985000000.0), 'unique_addresses': np.int64(522702), 'total_value_transferred': np.float64(1.65818e+24)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***

*** START STATE ***
{'cash': 500000.0, 'eth_held': np.float64(283.297304970244), 'open': np.float64(1764.930309), 'net_worth': np.float64(1000000.0), 'roi': np.float64(0.0), 'today_roi': np.float64(0.0), 'technical': {'macd_signal': 'sell'}, 'txnstat': {'average_gas_price': np.float64(25898811752.0), 'unique_addresses': np.int64(522702), 'total_value_transferred': np.float64(1.65818e+24), 'total_gas_used': np.float64(107985000000.0), 'total_transactions': np.int64(1063574), 'successful_transactions': np.int64(1039534)}, 'date': '2023-10-24 00:00:00 UTC'}
*** END STATE ***
